package com.hightop.benyin.rfid.domain.service;

import com.github.yulichang.base.MPJBaseServiceImpl;
import com.hightop.benyin.rfid.infrastructure.entity.RfidTrajectory;
import com.hightop.benyin.rfid.infrastructure.mapper.RfidTrajectoryMapper;
import org.springframework.stereotype.Service;

/**
 * rfid资产轨迹领域服务
 *
 * <AUTHOR>
 * @date 2024-10-12 16:36:01
 */
@Service
public class RfidTrajectoryServiceDomain extends MPJBaseServiceImpl<RfidTrajectoryMapper, RfidTrajectory> {
}
