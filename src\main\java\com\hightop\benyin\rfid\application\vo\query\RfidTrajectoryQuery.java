package com.hightop.benyin.rfid.application.vo.query;

import com.hightop.benyin.rfid.infrastructure.enums.TrajectorySource;
import com.hightop.benyin.rfid.infrastructure.enums.TrajectoryType;
import com.hightop.fario.common.mybatis.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldDefaults;

import java.util.List;

/**
 * RFID基站查询DTO
 *
 * <AUTHOR>
 * @date 2024-10-12 16:36:01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@FieldDefaults(level = AccessLevel.PRIVATE)
@ApiModel("RFID轨迹查询DTO")
public class RfidTrajectoryQuery extends PageQuery {

    @ApiModelProperty("资产ID")
    Long assetId;

    @ApiModelProperty("编码")
    String rfidCode;

    @ApiModelProperty("资产名称")
    String assetName;

    @ApiModelProperty("资产编码")
    String assetCode;


    @ApiModelProperty("基站设备")
    String deviceId;

    @ApiModelProperty("轨迹类型")
    String trajectoryType;

    @ApiModelProperty("异动来源")
    String trajectorySource;

    @ApiModelProperty("位置")
    List<String> locations;

    @ApiModelProperty("开始日期")
    String startDate;
    @ApiModelProperty("结束日期")
    String endDate;

}
