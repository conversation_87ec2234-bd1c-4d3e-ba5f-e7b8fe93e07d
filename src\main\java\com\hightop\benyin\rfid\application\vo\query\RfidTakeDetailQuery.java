package com.hightop.benyin.rfid.application.vo.query;

import com.hightop.fario.common.mybatis.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldDefaults;

import java.util.List;

/**
 * RFID基站查询DTO
 *
 * <AUTHOR>
 * @date 2024-10-12 16:36:01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@FieldDefaults(level = AccessLevel.PRIVATE)
@ApiModel("盘点明细查询DTO")
public class RfidTakeDetailQuery extends PageQuery {

    @ApiModelProperty("盘点编码")
    String takeCode;

    @ApiModelProperty("编码")
    String rfidCode;

    @ApiModelProperty("基站id")
    Long readerId;

    @ApiModelProperty("位置id")
    Long applyId;

    @ApiModelProperty("资产名称")
    String name;

    @ApiModelProperty("资产编码")
    String code;

    @ApiModelProperty("资产型号")
    String model;

    @ApiModelProperty("使用状态")
    List<String> useState;

    @ApiModelProperty("资产类型")
    List<String> assetType;

    @ApiModelProperty("是否有标签")
    Boolean hasTag;

    @ApiModelProperty("所属单位")
    String departmentName;

    @ApiModelProperty("位置")
    List<Long> locations;

    @ApiModelProperty("位置")
    List<Long> departmentIds;

    @ApiModelProperty("盘点状态")
    List<String> status;

    @ApiModelProperty("结果状态")
    List<String> takeStatus;


}
