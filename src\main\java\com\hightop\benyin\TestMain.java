package com.hightop.benyin;

import java.math.BigInteger;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDate;
import java.time.YearMonth;
import java.util.ArrayList;
import java.util.List;

public class TestMain {
    public static void main(String[] args) {
        LocalDate startDate = LocalDate.of(2023, 12, 1); // 设置起始时间为2023年12月1日
        LocalDate endDate = LocalDate.now();

        LocalDate lastYear = endDate.plusYears(-1);
        if(startDate.isBefore(lastYear)){
            startDate = lastYear;
        }
        List<YearMonth> allMonths = new ArrayList<>(); // 创建一个列表来存储所有月份

        YearMonth currentMonth = YearMonth.from(startDate); // 从起始时间开始
        while (!currentMonth.isAfter(YearMonth.from(endDate))) { // 当当前月份在结束时间之前
            allMonths.add(currentMonth); // 将当前月份添加到列表中
            System.out.println(currentMonth); // 打印当前月份
            currentMonth = currentMonth.plusMonths(1); // 获取下一个月份
        }
    }
    public static String md5HashText(String input) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] messageDigest = md.digest(input.getBytes());
            BigInteger number = new BigInteger(1, messageDigest);
            String hashtext = number.toString(16);
            while (hashtext.length() < 32) {
                hashtext = "0" + hashtext;
            }
            return hashtext;
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        }
    }
}
