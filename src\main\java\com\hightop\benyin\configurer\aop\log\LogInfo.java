package com.hightop.benyin.configurer.aop.log;

import lombok.Data;

import java.util.Date;

@Data
public class LogInfo {
    /** 日志主键*/
//    @TableId(type = IdType.UUID)
    private String logId;

    /** 日志类型*/
    private String type;

    /** 日志标题*/
    private String title;

    /** 日志摘要*/
    private String description;

    /** 请求IP*/
    private String ip;

    /** URI*/
    private String requestUri;

    /** 请求方式*/
    private String method;

    /** 提交参数*/
    private String params;

    /** 异常*/
    private String exception;

    /** 操作时间*/
    private Date operateDate;

    /** 请求时长*/
    private Long timeout;

    /** 用户登入名*/
    private String loginName;

    /** requestID*/
    private String requestId;

    /** 历史数据*/
    private String dataSnapshot;

    /** 日志状态*/
    private Integer status;
}
