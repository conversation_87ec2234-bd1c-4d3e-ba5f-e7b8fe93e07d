package com.hightop.benyin.rfid.application.vo.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Description: 资产领用DTO
 * @Author: X.S
 * @Date: 2023/12/22 17:16
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@ApiModel("基站添加资产DTO")
public class AssetReaderDto {

    @ApiModelProperty("资产id集合")
    @NotNull(message = "资产信息不能为空")
    List<Long> assetIds;

    @ApiModelProperty("基站编码")
    @NotNull(message = "基站编码不能为空")
    Long readerCode;

}
