package com.hightop.benyin.rfid.infrastructure.enums;

import com.hightop.magina.core.custom.entry.EnumEntry;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

/**
 * 资产状态
 *
 * <AUTHOR>
 * @date 2024/5/15 13:42
 */
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Getter

public enum VariationStatus implements EnumEntry<String> {
    /**
     * 上报
     */
    WAIT("待处理"),
    /**
     * 已通知
     */
    NOTICE("已通知"),
    /**
     * 已完成
     */
    SUCCESS("已处理"),
    /**
     * 超时未处理
     */
    OVERTIME("超时未处理");

    /**
     * 状态名称
     */
    String name;

    @Override
    public String getCode() {
        // 以枚举名称作为标识
        return super.name();
    }

    @Override
    public String getName() {
        return this.name;
    }

}
