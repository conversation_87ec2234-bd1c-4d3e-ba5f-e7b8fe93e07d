package com.hightop.benyin.share.api.controller;

import com.hightop.benyin.share.application.service.CosService;
import com.hightop.benyin.share.application.vo.CosBucket;
import com.hightop.benyin.share.infrastructure.restful.tencent.sts.FederationCredentials;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 腾讯cos
 * @Author: X.S
 * @date 2023/10/25 20:06
 */
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@RestController
@RequestMapping("/cos")
@Api(tags = "腾讯COS")
public class CosController {
    CosService cosService;

    @GetMapping("/credentials")
    @ApiOperation(
        value = "cos临时上传密钥",
        notes = "使用临时密钥文件上传，[腾讯SDK文档](https://cloud.tencent.com/document/product/436/11459)"
    )
    public FederationCredentials credentials() {
        return this.cosService.credentials();
    }

    @GetMapping("/bucket")
    @ApiOperation("桶基本信息")
    public CosBucket bucket() {
        return this.cosService.bucket();
    }
}
