package com.hightop.benyin.system.api.vo.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotNull;


/**
 * 用户启停DTO
 *
 * @Author: X.S
 * @date 2024/09/29 13:58
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@ApiModel("用户启停DTO")
public class UserStatusDto {

    @NotNull(message = "启停状态不能为空")
    @ApiModelProperty("启停状态1 启用 0 停用")
    Boolean isAvailable;

    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    @NotNull(message = "用户id不能为空")
    Long id;
}