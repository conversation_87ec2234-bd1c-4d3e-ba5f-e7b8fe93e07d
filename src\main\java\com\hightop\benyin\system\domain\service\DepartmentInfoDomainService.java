package com.hightop.benyin.system.domain.service;

import com.github.yulichang.base.MPJBaseServiceImpl;
import com.hightop.benyin.system.api.vo.DepartmentInfoTreeVo;
import com.hightop.benyin.system.api.vo.query.DepartmentQuery;
import com.hightop.benyin.system.infrastructure.entity.DepartmentInfo;
import com.hightop.benyin.system.infrastructure.mapper.DepartmentInfoMapper;
import com.hightop.magina.core.exception.MaginaException;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Service
public class DepartmentInfoDomainService extends MPJBaseServiceImpl<DepartmentInfoMapper, DepartmentInfo> {


    public List<DepartmentInfoTreeVo> getDepartmentList(DepartmentQuery query) {
        return baseMapper.getDepartmentList(query);
    }


    /**
     * 根据部门ID获取公司信息
     *
     * @param deptId
     * @return
     */
    public DepartmentInfo getCompanyInfo(Long deptId) {
        DepartmentInfo departmentInfo = this.getById(deptId);
        if (departmentInfo == null) {
          throw new MaginaException("当前用户未添加部门信息");
        }
        while (departmentInfo.getDeptType() !=1) {
            departmentInfo = this.getById(departmentInfo.getParentId());
        }
        return departmentInfo;
    }

    /**
     * 根据公司获取部门信息
     *
     * @param deptId
     * @return
     */
    public List<Long> getCompanyDeptList(Long deptId) {
        DepartmentInfo departmentInfo = this.getCompanyInfo(deptId);
        return this.lambdaQuery().likeRight(DepartmentInfo::getCode, departmentInfo.getCode().getValue()).list()
                .stream().map(DepartmentInfo::getId).collect(Collectors.toList());
    }

    public void clearDepartment() {
        baseMapper.clearDepartment();
    }
}
