package com.hightop.benyin.share.infrastructure.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.hightop.fario.base.util.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;
import java.util.Objects;
import java.util.Optional;

/**
 * 地区实体
 * @Author: X.S
 * @date 2023/10/20 17:43
 */
@FieldDefaults(level = AccessLevel.PRIVATE)
@Data
@TableName("b_region")
public class Region implements Serializable {
    @TableId
    @ApiModelProperty("地区编码")
    Integer code;
    @TableField("parent_code")
    @ApiModelProperty("地区父编码")
    Integer parentCode;
    @TableField
    @ApiModelProperty("地区名称简称")
    String name;
    @TableField("full_name")
    @ApiModelProperty("地区名称全称")
    String fullName;
    @TableField
    @ApiModelProperty("地区编码全路径")
    String path;
    @TableField("has_children")
    @ApiModelProperty("是否存在子节点")
    Boolean hasChildren;

    @TableField(exist = false)
    @ApiModelProperty("地区父名称")
    String parentName;

    /**
     * 顶层节点父编码值
     */
    public static final Integer TOP = 0;

    /**
     * 是否顶层节点
     * @return true/false
     */
    @JsonIgnore
    public boolean isTop() {
        return Objects.equals(this.parentCode, TOP);
    }

    /**
     * 获得地区全称
     * @return 地区全称
     */
    @JsonIgnore
    public String getRegionFullName() {
        return Optional.ofNullable(this.fullName).filter(StringUtils::isNotBlank).orElseGet(this::getName);
    }
}
