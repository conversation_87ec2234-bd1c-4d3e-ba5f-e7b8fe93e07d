package com.hightop.benyin.share.application.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import lombok.experimental.FieldDefaults;

/**
 * 员工小程序认证对象
 * @Author: X.S
 * @date 2023/11/07 14:03
 */
@FieldDefaults(level = AccessLevel.PRIVATE)
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Data
public class WeChatStaffAuthVo extends WeChatStaffTokenVo {
    @ApiModelProperty("是否绑定用户")
    Boolean isBind;
    @ApiModelProperty("终端openId(用于登录用户绑定) 当未绑定用户时返回")
    String openId;
}
