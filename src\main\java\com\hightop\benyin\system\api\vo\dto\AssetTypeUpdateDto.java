package com.hightop.benyin.system.api.vo.dto;

import com.hightop.benyin.system.infrastructure.entity.AssetType;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotNull;

/**
 * 资产类型修改dto
 *
 * <AUTHOR>
 * @date 2022/10/09 15:48
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AssetTypeUpdateDto extends AssetTypeAddDto {
    @ApiModelProperty("id")
    @NotNull(message = "id不能为空")
    Long id;

    @Override
    public AssetType toAssetType() {
        return super.toAssetType().setId(this.id);
    }
}
