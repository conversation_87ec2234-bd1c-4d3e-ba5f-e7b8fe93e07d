package com.hightop.benyin.rfid.application.vo.query;

import cn.afterturn.easypoi.excel.annotation.ExcelIgnore;
import com.hightop.benyin.rfid.infrastructure.enums.AssetApplyStatus;
import com.hightop.benyin.rfid.infrastructure.enums.AssetFlowStatus;
import com.hightop.benyin.system.api.vo.query.AuthPageQuery;
import com.hightop.fario.common.jackson.annotation.JsonAmount;
import com.hightop.fario.common.mybatis.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldDefaults;

import java.util.List;

/**
 * 资产查询DTO
 *
 * <AUTHOR>
 * @date 2024-10-12 16:36:01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@FieldDefaults(level = AccessLevel.PRIVATE)
@ApiModel("资产查询DTO")
public class RfidAssetQuery extends AuthPageQuery {

    @ApiModelProperty("id")
    Long id;

    @ApiModelProperty("rfid编码")
    String rfidCode;

    @ApiModelProperty("rfid编码列表")
    List<String> rfidCodes;

    @ApiModelProperty("原rfid编码")
    String oriRfidCode;

    @ApiModelProperty("上级编码")
    String parentCode;

    @ApiModelProperty("基站编码")
    String readerCode;
    @ApiModelProperty("资产编码")
    String code;
    @ApiModelProperty("位置编码")
    String locationCode;

    @ApiModelProperty("位置id")
    Long locationId;


    @ApiModelProperty("基站id")
    Long readerId;

    @ApiModelProperty("资产类型1父类")
    Integer type;

    @ApiModelProperty("规格型号")
    String model;

    @ApiModelProperty("标签绑定")
    Boolean bindTag;

    @ApiModelProperty("基站绑定")
    Boolean bindReader;

    @ApiModelProperty("状态")
    AssetApplyStatus status;

    @ApiModelProperty("不含状态")
    String neStatus;

    @ApiModelProperty("入库状态")
    Integer inStatus;

    @ApiModelProperty("资产名称")
    String name;

    @ApiModelProperty("所属单位")
    String departmentName;

    @ApiModelProperty("负责人")
    String managerName;

    @ApiModelProperty("领用人姓名")
    String applyName;

    @ApiModelProperty("资产来源1期初导入0业务新增")
    List<Integer> dataSource;

    @ApiModelProperty("领用人id")
    List<Long> applyIds;

    @ApiModelProperty("登记单号")
    String signCode;


    @ApiModelProperty("创建人姓名")
    String createdByName;

    @ApiModelProperty("所属部门")
    Long departmentId;


    @ApiModelProperty("创建人id")
    Long createdUserId;

    @ApiModelProperty("责任人")
    List<Long> managerIds;

    @ApiModelProperty("责任部门")
    List<Long> managerDeptIds;

    @ApiModelProperty("位置")
    List<Long> locations;

    @ApiModelProperty("保管部门列表")
    List<Long> departmentIds;


    @ApiModelProperty("登记人部门列表")
    List<Long> createDepIds;


    @ApiModelProperty("登记人列表")
    List<Long> createdByIds;

    @ApiModelProperty("资产类型")
    List<String> assetType;

    @ApiModelProperty("使用状态")
    List<String> useState;

    @ApiModelProperty("财务归口")
    List<String> financialClassify;

    @ApiModelProperty("取得方式")
    List<String> acquireMode;

    @ApiModelProperty("用途")
    List<String> assetPurpose;

    @ApiModelProperty("状态多选")
    List<AssetFlowStatus> statusList;

    @ApiModelProperty("储藏位置(选填)")
    String storageLocation;

    @ApiModelProperty("登记/领用")
    Integer inOutType;

    @ApiModelProperty("登记起始时间")
    String startDate;

    @ApiModelProperty("登记截止时间")
    String endDate;

    @ApiModelProperty("折旧起始时间")
    String startBreakageDate;

    @ApiModelProperty("折旧截止时间")
    String endBreakageDate;

    @ApiModelProperty("入账起始时间")
    String startEnterDate;

    @ApiModelProperty("入账截止时间")
    String endEnterDate;

    @ApiModelProperty("取得起始时间")
    String startAcquireDate;

    @ApiModelProperty("取得截止时间")
    String endAcquireDate;

    @ApiModelProperty("有无标签")
    List<Boolean> hasTag;

    @ApiModelProperty("是否扫描")
    List<Boolean> isScan;

    @ApiModelProperty("是否上报异常")
    List<Boolean> isReport;

    @ApiModelProperty("是否盘点")
    List<Boolean> isTake;

    @ApiModelProperty("盘点是否统计")
    List<Boolean> isTakeStatis;

    @ApiModelProperty("财务是否统计")
    List<Boolean> isStatis;

    @ApiModelProperty("是否流动")
    Boolean isFlow;

    @ApiModelProperty("财务归口列表")
    List<String> financialClassifys;

    @ApiModelProperty("价格-起始")
    @JsonAmount
    Long startPrice;

    @ApiModelProperty("价格-截止")
    @JsonAmount
    Long endPrice;

    @ApiModelProperty("现残值-起始")
    @JsonAmount
    Long startSalvagePrice;

    @ApiModelProperty("现残值-截止")
    @JsonAmount
    Long endSalvagePrice;

    @ApiModelProperty("是否有查询条件")
    Boolean hasCondition;


}
