package com.hightop.benyin.configurer;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class EnvUtilsComponent {

    @Value("${spring.profiles.active}")
    private String profile;

    /**
     * 判断是否是生产环境
     * @return
     */
    public boolean isProd() {
        return "prod".equals(profile);
    }

    /**
     * 是否测试环境
     * @return
     */
    public boolean isDev() {
        return profile.equals("dev");
    }

    /**
     * 是否测试环境
     * @return
     */
    public boolean isTest() {
        return profile.equals("test");
    }
}
