<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hightop.benyin.system.infrastructure.mapper.DepartmentInfoMapper">

    <select id="getDepartmentList" resultType="com.hightop.benyin.system.api.vo.DepartmentInfoTreeVo">

        SELECT t.id,
        t.parent_id,
        t.name,
        t.code,
        t.manager_id,
        t.dept_type,
        t.is_enable,
        t.sort,
        t.is_available,
        t.expire_at,
        t.created_at,
        t.updated_at,
        t.updated_by,
        t.full_id_path,
        t1.name AS managerName,
        t1.code AS managerCode,
        t2.code AS parentCode,
        t2.name AS parentName
        FROM st_department t
        LEFT JOIN st_user_basic t1 ON (t1.id = t.manager_id)
        LEFT JOIN st_department t2 ON (t2.id = t.parent_id)
        WHERE t.is_available = 1
        <if test="qo.code!= null and qo.code!= ''">
            and t.code like concat ('%',#{qo.code},'%')
        </if>

        <if test="qo.name!= null and qo.name!= ''">
            and t.name like concat ('%',#{qo.name},'%')
        </if>

        <if test="qo.companyCode!= null and qo.companyCode!= ''">
            and t.code like concat (#{qo.companyCode},'%')
        </if>

        <if test="qo.id!= null">
            and t.id = #{qo.id}
        </if>

        <if test="qo.isEnable!= null">
            and t.is_enable = #{qo.isEnable}
        </if>

        <if test="null != qo.startDisableDate and '' != qo.startDisableDate ">
            and t.expire_at &gt;= concat(#{qo.startDisableDate},' 00:00:00')
        </if>
        <if test="null != qo.endDisableDate and '' != qo.endDisableDate ">
            and t.expire_at &lt;= concat(#{qo.endDisableDate},' 23:59:59')
        </if>

        <if test="null != qo.startEnableDate and '' != qo.startEnableDate ">
            and t.created_at &gt;= concat(#{qo.startEnableDate},' 00:00:00')
        </if>
        <if test="null != qo.endEnableDate and '' != qo.endEnableDate ">
            and t.created_at &lt;= concat(#{qo.endEnableDate},' 23:59:59')
        </if>
        <if test="null!=qo.departmentCodes and !qo.departmentCodes.isEmpty()">
            AND (
            <foreach collection="qo.departmentCodes" item="code" separator=" or ">
                t.code like concat (#{code},'%')
            </foreach>
            )
        </if>

        <if test="null!=qo.departmentIds and !qo.departmentIds.isEmpty()">
            AND t.id IN
            <foreach collection="qo.departmentIds" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>

        <if test="null!=qo.managerId and !qo.managerId.isEmpty()">
            AND t.manager_id IN
            <foreach collection="qo.managerId" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>

    </select>

</mapper>