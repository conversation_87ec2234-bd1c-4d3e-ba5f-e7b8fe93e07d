package com.hightop.benyin.rfid.application.vo.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.util.List;

/**
 * 资产盘点查询DTO
 *
 * <AUTHOR>
 * @date 2024-10-12 16:36:01
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@ApiModel("资产盘点查询DTO")
public class RfidAssetTakeReaderQuery {

    @ApiModelProperty("盘点状态")
    List<String> status;

    @ApiModelProperty("盘点单号")
    String takeCode;

    @ApiModelProperty("设备ID")
    String deviceId;

    @ApiModelProperty("盘点状态")
    List<Long> locationIds;

    @ApiModelProperty("盘点状态")
    List<Long> departmentIds;


}
