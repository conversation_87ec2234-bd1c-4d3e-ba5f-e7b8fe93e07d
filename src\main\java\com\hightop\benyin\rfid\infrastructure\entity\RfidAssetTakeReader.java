package com.hightop.benyin.rfid.infrastructure.entity;

import cn.afterturn.easypoi.excel.annotation.ExcelIgnore;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.hightop.benyin.rfid.infrastructure.enums.ReaderStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 资产盘点进度表
 *
 * <AUTHOR>
 * @date 2024-10-12 16:36:01
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@TableName(value = "b_rfid_asset_take_reader", autoResultMap = true)
@ApiModel
public class RfidAssetTakeReader {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @ApiModelProperty("id")
    Long id;

    @TableField("take_code")
    @ApiModelProperty("盘点单号")
    String takeCode;

    @TableField("ip_addr")
    @ApiModelProperty("ip地址")
    String ipAddr;

    @TableField("reader_id")
    @ApiModelProperty("基站编码")
    Long readerId;

    @TableField("device_id")
    @ApiModelProperty("基站设备id")
    String deviceId;

    @TableField("location_id")
    @ApiModelProperty("位置id")
    Long locationId;

    @TableField("location")
    @ApiModelProperty("位置")
    String location;

    @TableField(value = "remark", updateStrategy = FieldStrategy.IGNORED)
    @ApiModelProperty("备注")
    String remark;

    @TableField(value = "department_ids", typeHandler = JacksonTypeHandler.class)
    @ApiModelProperty("位置的部门id列表")
    List<Long> departmentIds;

    @TableField("department_names")
    @ApiModelProperty("位置所属单位")
    String departmentNames;

    @TableField("asset_num")
    @ApiModelProperty("资产数量")
    Integer assetNum;

    @TableField("bind_num")
    @ApiModelProperty("绑定标签数量")
    Integer bindNum;

    @TableField("scan_num")
    @ApiModelProperty("扫描数量")
    Integer scanNum;

    @TableField("status")
    @ApiModelProperty("状态-1扫描中  0未开始  1扫描完成  2基站故障  ")
    Integer status;

    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @ApiModelProperty("上报时间")
    LocalDateTime createdAt;

    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty("更新时间")
    LocalDateTime updatedAt;

    @TableField("deleted")
    @ApiModelProperty("是否删除  0未删除  1删除")
    @TableLogic
    Integer deleted;

    @TableField(exist = false)
    @ApiModelProperty("基站连接状态")
    @ExcelIgnore
    Boolean isConnect;


    @TableField(exist = false)
    @ApiModelProperty("是否扫描完成")
    @ExcelIgnore
    Boolean isCompleted=false;


}
