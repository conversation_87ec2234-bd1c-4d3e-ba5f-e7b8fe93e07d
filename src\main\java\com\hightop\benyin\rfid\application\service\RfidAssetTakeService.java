package com.hightop.benyin.rfid.application.service;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.yulichang.toolkit.MPJWrappers;
import com.github.yulichang.toolkit.SpringContentUtils;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.hightop.benyin.rfid.application.vo.dto.RfidAssetTakeAuditDto;
import com.hightop.benyin.rfid.application.vo.dto.RfidAssetTakeDto;
import com.hightop.benyin.rfid.application.vo.po.ReaderCheck;
import com.hightop.benyin.rfid.application.vo.query.RfidAssetForTakeQuery;
import com.hightop.benyin.rfid.application.vo.query.RfidAssetTakeQuery;
import com.hightop.benyin.rfid.application.vo.query.RfidAssetTakeReaderQuery;
import com.hightop.benyin.rfid.application.vo.query.RfidReaderQuery;
import com.hightop.benyin.rfid.domain.event.AssetTakeEvent;
import com.hightop.benyin.rfid.domain.service.*;
import com.hightop.benyin.rfid.infrastructure.entity.*;
import com.hightop.benyin.rfid.infrastructure.enums.*;
import com.hightop.benyin.share.domain.event.ApiLogEvent;
import com.hightop.benyin.share.domain.service.SequenceDomainService;
import com.hightop.benyin.share.infrastructure.enums.MessageType;
import com.hightop.benyin.share.infrastructure.util.DictUtil;
import com.hightop.benyin.share.infrastructure.util.RedisLockUtils;
import com.hightop.benyin.share.socket.enums.CommandType;
import com.hightop.benyin.share.socket.util.ReaderControlTool;
import com.hightop.benyin.share.socket.util.SocketUtil;
import com.hightop.benyin.system.domain.service.DepartmentInfoDomainService;
import com.hightop.benyin.system.domain.service.LocationDomainService;
import com.hightop.benyin.system.domain.service.UserInfoDomainService;
import com.hightop.benyin.system.infrastructure.entity.DepartmentInfo;
import com.hightop.benyin.system.infrastructure.entity.Location;
import com.hightop.benyin.system.infrastructure.entity.UserInfo;
import com.hightop.fario.base.util.CollectionUtils;
import com.hightop.fario.base.util.StringUtils;
import com.hightop.fario.common.core.util.ExecutorUtils;
import com.hightop.fario.common.mybatis.DataGrid;
import com.hightop.fario.common.mybatis.PageQuery;
import com.hightop.fario.common.mybatis.util.PageHelper;
import com.hightop.magina.casual.session.ApplicationSessions;
import com.hightop.magina.core.exception.MaginaException;
import com.hightop.magina.standard.code.dictionary.api.DictItemApiService;
import com.hightop.magina.standard.code.dictionary.api.DictItemVo;
import com.hightop.magina.standard.code.property.PropertyDomainService;
import com.hightop.magina.standard.ums.user.bind.UserEntry;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 资产盘点管理服务
 *
 * <AUTHOR>
 * @date 2024-10-12 16:36:01
 */
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Service
@Slf4j
public class RfidAssetTakeService {

    RfidAssetTakeServiceDomain rfidAssetTakeServiceDomain;
    SequenceDomainService sequenceDomainService;
    RedisTemplate<String, String> redisTemplate;
    RfidAssetTakeDetailServiceDomain rfidAssetTakeDetailServiceDomain;
    RfidAssetService rfidAssetService;
    RfidAssetServiceDomain rfidAssetServiceDomain;
    RfidReaderServiceDomain rfidReaderServiceDomain;
    LocationDomainService locationDomainService;
    RfidAssetTakeReaderServiceDomain rfidAssetTakeReaderServiceDomain;
    ApplicationEventPublisher applicationEventPublisher;
    RfidInfoServiceDomain rfidInfoServiceDomain;
    UserInfoDomainService userInfoDomainService;
    DictItemApiService dictItemApiService;
    DepartmentInfoDomainService departmentInfoDomainService;
    RfidReaderService rfidReaderService;


    /**
     * 资产盘点分页查询
     *
     * @param pageQuery {@link PageQuery}
     * @return {@link DataGrid}
     */
    public DataGrid<RfidAssetTake> page(RfidAssetTakeQuery pageQuery) {
        return PageHelper.startPage(pageQuery, p ->
                this.rfidAssetTakeServiceDomain.findAssetTakeList(pageQuery)
        ).peek(v -> {
            v.setRangeInfoName(getRangeInfo(v.getTakeRange(), v.getRangeInfo()));
        });
    }


    public RfidAssetTake getById(Long id) {
        RfidAssetTake rfidAssetTake = this.rfidAssetTakeServiceDomain.getById(id);
        rfidAssetTake.setRangeInfoName(this.getRangeInfo(rfidAssetTake.getTakeRange(), rfidAssetTake.getRangeInfo()));
        return rfidAssetTake;
    }

    private String getRangeInfo(TakeRangeEnums takeRange, List<Long> ids) {
        switch (takeRange) {
            case DEPARTMENT:
                List<DepartmentInfo> departmentInfos = departmentInfoDomainService.lambdaQuery().in(DepartmentInfo::getId, ids).list();
                return departmentInfos.stream().map(v -> v.getName().getValue()).collect(Collectors.joining(","));
            case LOCATION:
                List<Location> locations = locationDomainService.lambdaQuery().in(Location::getId, ids).list();
                return locations.stream().map(Location::getFullName).collect(Collectors.joining(","));
            case USER:
                List<UserInfo> userInfos = userInfoDomainService.lambdaQuery().in(UserInfo::getId, ids).list();
                return userInfos.stream().map(UserInfo::getName).collect(Collectors.joining(","));
            default:
                return "";
        }
    }

    /**
     * 资产盘点进度分页查询
     *
     * @param query {@link PageQuery}
     * @return {@link DataGrid}
     */
    public List<RfidAssetTakeReader> takeSchedule(RfidAssetTakeReaderQuery query) {
        List<RfidAssetTakeReader> rfidAssetTakeReaders = rfidAssetTakeReaderServiceDomain.selectJoinList(RfidAssetTakeReader.class, MPJWrappers.lambdaJoin()
                .selectAll(RfidAssetTakeReader.class)
                .in(CollectionUtils.isNotEmpty(query.getLocationIds()), RfidAssetTakeReader::getLocationId, query.getLocationIds())
                .in(CollectionUtils.isNotEmpty(query.getStatus()), RfidAssetTakeReader::getStatus, query.getStatus())
                .like(StringUtils.isNotBlank(query.getDeviceId()), RfidAssetTakeReader::getDeviceId, query.getDeviceId())
                .eq(RfidAssetTakeReader::getTakeCode, query.getTakeCode())
        );
        rfidAssetTakeReaders.stream().forEach(v -> {
            if (!v.getStatus().equals(DictUtil.ENABLE)) {
                RfidReader rfidReader = rfidReaderServiceDomain.getById(v.getReaderId());
                ReaderCheck readerCheck = rfidReaderService.checkReader(rfidReader);
                //未开始
                if (readerCheck.getStatus().equals(DictUtil.SUB)) {
                    v.setRemark(readerCheck.getMessage());
                    v.setStatus(readerCheck.getStatus());
                    rfidAssetTakeReaderServiceDomain.updateById(v);
                } else {
                    if (v.getStatus().equals(DictUtil.OUT_STOCK)) {
                        v.setRemark(null);
                        v.setStatus(DictUtil.DISABLE);
                    }
                }
            }
            v.setIsConnect(SocketUtil.isConnect(v.getDeviceId()));
        });

        return rfidAssetTakeReaders;
    }

    public boolean setReaderStatus(Integer status, RfidAssetTakeReader rfidAssetTakeReader) {
        rfidAssetTakeReader.setStatus(status);
        return rfidAssetTakeReaderServiceDomain.updateById(rfidAssetTakeReader);
    }

    /**
     * 资产盘点
     *
     * @param rfidAssetTakeDto
     * @return true/false
     */
    @Transactional(rollbackFor = Exception.class)
    public RfidAssetTake storageTake(RfidAssetTakeDto rfidAssetTakeDto) {
        //查询基站信息 条件
        RfidAssetForTakeQuery rfidAssetForTakeQuery = new RfidAssetForTakeQuery();
        RfidAssetTake rfidAssetTake = new RfidAssetTake();

        if (rfidAssetTakeDto.getTakeRange().equals(TakeRangeEnums.DEPARTMENT)) {
            if (CollectionUtils.isEmpty(rfidAssetTakeDto.getDepartmentIds())) {
                throw new MaginaException("请选择盘点部门！");
            } else {
                rfidAssetForTakeQuery.setDepartmentIds(rfidAssetTakeDto.getDepartmentIds());
                rfidAssetTake.setRangeInfo(rfidAssetTakeDto.getDepartmentIds());
            }
        }
        if (rfidAssetTakeDto.getTakeRange().equals(TakeRangeEnums.LOCATION)) {
            if (CollectionUtils.isEmpty(rfidAssetTakeDto.getLocations())) {
                throw new MaginaException("请选择盘点位置！");
            } else {
                rfidAssetForTakeQuery.setLocations(rfidAssetTakeDto.getLocations());
                rfidAssetTake.setRangeInfo(rfidAssetTakeDto.getLocations());
            }
        }

        if (rfidAssetTakeDto.getTakeRange().equals(TakeRangeEnums.USER)) {
            if (CollectionUtils.isEmpty(rfidAssetTakeDto.getApplyIds())) {
                throw new MaginaException("请选择盘点人！");
            } else {
                //保管人
                rfidAssetForTakeQuery.setApplyIds(rfidAssetTakeDto.getApplyIds());
                rfidAssetTake.setRangeInfo(rfidAssetTakeDto.getApplyIds());
            }
        }
        //查询现有资产 需要盘点和需要盘点统计的资产
        List<RfidAsset> rfidAssets = rfidAssetService.getTakeAssetList(rfidAssetForTakeQuery);
        if (CollectionUtils.isEmpty(rfidAssets)) {
            throw new MaginaException("未找到需要盘点的资产！");
        }
        String code = sequenceDomainService.nextDateSequence(RfidAssetTake.SEQ_PREFIX, 4);
        rfidAssetTake.setCode(code);
        rfidAssetTake.setTakeType(rfidAssetTakeDto.getTakeType());
        rfidAssetTake.setTakeRange(rfidAssetTakeDto.getTakeRange());
        rfidAssetTake.setBeforeNum(rfidAssets.size());
        Long amount = rfidAssets.stream().map(RfidAsset::getPrice).reduce(0L, Long::sum);
        rfidAssetTake.setBeforeAmount(amount);
        rfidAssetTake.setCreatedBy(new UserEntry().setId(ApplicationSessions.id()));
        rfidAssetTake.setStatus(DiscrepancyStatus.EXECUTING);
        rfidAssetTake.setTakeStatus(TakeStatus.EXECUTING);
        rfidAssetTake.setScanStatus(TakeScanStatus.EXECUTING);
        rfidAssetTake.setRangeInfoName(getRangeInfo(rfidAssetTake.getTakeRange(), rfidAssetTake.getRangeInfo()));
        //查询现有资产 需要盘点和需要盘点统计的资产
        List<Long> readerIds = rfidAssets.stream().map(RfidAsset::getReaderId).distinct().collect(Collectors.toList());
        RfidReaderQuery rfidReaderQuery = new RfidReaderQuery();
        rfidReaderQuery.setIds(readerIds);
        List<RfidReader> rfidReaders = rfidReaderServiceDomain.getReaderList(rfidReaderQuery);
        if (CollectionUtils.isEmpty(rfidReaders)) {
            throw new MaginaException("所选范围内资产均未关联基站！");
        }
        //保存本次盘点基站和部门信息
        Map<Long, List<RfidAsset>> readerAssetMap = rfidAssets.stream().filter(v -> v.getReaderId() != null).collect(Collectors.groupingBy(RfidAsset::getReaderId));
        rfidAssetTake.setReaderNum(rfidReaders.size());

        List<RfidAssetTakeReader> rfidAssetTakeReaders = this.setTakeReader(readerAssetMap, rfidReaders, code);
        //无法执行扫描的基站
        List<Long> readerNotList = rfidAssetTakeReaders.stream().filter(v -> v.getStatus().equals(DictUtil.SUB)).map(RfidAssetTakeReader::getReaderId).collect(Collectors.toList());
        if (rfidAssetTakeReaders.size() == readerNotList.size()) {
            rfidAssetTake.setStatus(DiscrepancyStatus.WITHOUT);
            rfidAssetTake.setTakeStatus(TakeStatus.WAIT_DEAL);
            rfidAssetTake.setScanStatus(TakeScanStatus.UNABLE);
        }
        List<RfidAssetTakeDetail> rfidAssetTakeDetails = Lists.newArrayList();
        for (RfidAsset rfidAsset : rfidAssets) {
            RfidAssetTakeDetail rfidAssetTakeDetail = new RfidAssetTakeDetail();
            rfidAssetTakeDetail.setTakeCode(rfidAssetTake.getCode());
            rfidAssetTakeDetail.setReaderId(rfidAsset.getReaderId());
            rfidAssetTakeDetail.setDeviceId(rfidAsset.getReaderDeviceId());
            rfidAssetTakeDetail.setAssetId(rfidAsset.getId());
            rfidAssetTakeDetail.setHasTag(rfidAsset.getHasTag());
            rfidAssetTakeDetail.setIsTake(rfidAsset.getIsTake());
            rfidAssetTakeDetail.setIsTakeStatis(rfidAsset.getIsTakeStatis());
            rfidAssetTakeDetail.setRfidCode(rfidAsset.getRfidCode());
            rfidAssetTakeDetail.setPrice(rfidAsset.getPrice());
            rfidAssetTakeDetail.setManagerDeptId(rfidAsset.getManagerDeptId());
            rfidAssetTakeDetail.setManagerDeptName(rfidAsset.getManagerDeptName());
            rfidAssetTakeDetail.setManagerId(rfidAsset.getManagerId());
            rfidAssetTakeDetail.setManagerName(rfidAsset.getManagerName());
            rfidAssetTakeDetail.setDepartmentId(rfidAsset.getDepartmentId());
            rfidAssetTakeDetail.setDepartmentName(rfidAsset.getDepartmentName());
            rfidAssetTakeDetail.setApplyId(rfidAsset.getApplyId());
            rfidAssetTakeDetail.setApplyName(rfidAsset.getApplyName());
            rfidAssetTakeDetail.setLocation(rfidAsset.getLocation());
            rfidAssetTakeDetail.setLocationId(rfidAsset.getLocationId());
            rfidAssetTakeDetail.setStatus(DiscrepancyStatus.EXECUTING);
            rfidAssetTakeDetail.setTakeStatus(TakeStatus.EXECUTING);
            rfidAssetTakeDetail.setScanStatus(TakeScanStatus.EXECUTING);

            //没有标签默认正常
            if (!rfidAssetTakeDetail.getHasTag()) {
                rfidAssetTakeDetail.setScanStatus(TakeScanStatus.NOT);
                rfidAssetTakeDetail.setTakeStatus(TakeStatus.YES);
                rfidAssetTakeDetail.setStatus(DiscrepancyStatus.NORMAL);
            } else {
                if (StringUtils.isBlank(rfidAsset.getRfidCode()) || rfidAsset.getReaderId() == null) {
                    rfidAssetTakeDetail.setScanStatus(TakeScanStatus.UNABLE);
                    rfidAssetTakeDetail.setTakeStatus(TakeStatus.WAIT_DEAL);
                    rfidAssetTakeDetail.setStatus(DiscrepancyStatus.WITHOUT);
                }
                //盘点不扫描则设定为已盘点
                if (rfidAsset.getIsTake() != null && !rfidAsset.getIsTake()) {
                    rfidAssetTakeDetail.setScanStatus(TakeScanStatus.NOT);
                    rfidAssetTakeDetail.setTakeStatus(TakeStatus.YES);
                    rfidAssetTakeDetail.setStatus(DiscrepancyStatus.NORMAL);
                }
            }
            if (readerNotList.contains(rfidAssetTakeDetail.getReaderId())) {
                if (rfidAsset.getIsTake() != null && rfidAsset.getIsTake()) {
                    rfidAssetTakeDetail.setScanStatus(TakeScanStatus.UNABLE);
                }
//                rfidAssetTakeDetail.setTakeStatus(TakeStatus.WAIT_DEAL);
                rfidAssetTakeDetail.setStatus(DiscrepancyStatus.WITHOUT);
            }

            //检查资产是否正常
            rfidAssetTakeDetail.setRemark(this.checkAsset(rfidAsset));
            if (StringUtils.isNotBlank(rfidAssetTakeDetail.getRemark())) {
                rfidAssetTakeDetail.setScanStatus(TakeScanStatus.ABNORMAL);
            }
            rfidAssetTakeDetails.add(rfidAssetTakeDetail);
        }
        rfidAssetTakeDetailServiceDomain.saveBatch(rfidAssetTakeDetails);
        rfidAssetTakeReaderServiceDomain.saveBatch(rfidAssetTakeReaders);

        rfidAssetTake.setCreatedBy(new UserEntry().setId(ApplicationSessions.id()));
        UserInfo userInfo = userInfoDomainService.getCurrUser();
        rfidAssetTake.setCreateDept(userInfo.getDepartmentId());
        this.rfidAssetTakeServiceDomain.save(rfidAssetTake);
        return rfidAssetTake;
    }

    public String checkAsset(RfidAsset rfidAsset) {
        Boolean hasTag = rfidAsset.getHasTag() == null ? false : rfidAsset.getHasTag();
        Boolean isTake = rfidAsset.getIsTake() == null ? false : rfidAsset.getIsTake();
        StringJoiner joiner = new StringJoiner(",");
        if (hasTag && StringUtils.isBlank(rfidAsset.getRfidCode())) {
            joiner.add("未绑定标签");
        }
        if (hasTag && rfidAsset.getReaderId() == null) {
            joiner.add("未绑定设备");
        }
        if (isTake && !hasTag) {
            joiner.add("配置有误,需要扫描但资产不贴标签");
        }
        if (StringUtils.isNotBlank(rfidAsset.getRfidCode()) && !hasTag) {
            joiner.add("配置无标签但资产贴标签");
        }
        return joiner.toString();
    }

    /**
     * 重新盘点扫描
     *
     * @param id
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean reTakeScan(Long id) {
        RfidAssetTakeReader rfidAssetTakeReader = rfidAssetTakeReaderServiceDomain.getById(id);
        this.deleteReaderCache(rfidAssetTakeReader.getReaderId(), DictUtil.TAKE_SCAN, rfidAssetTakeReader.getTakeCode());
        String code = rfidAssetTakeReader.getTakeCode();
        log.info("重新盘点扫描：{}", code);
        RfidReader rfidReader = rfidReaderServiceDomain.getById(rfidAssetTakeReader.getReaderId());
        if (StringUtils.isBlank(rfidReader.getDeviceId())) {
            throw new MaginaException("基站[" + rfidReader.getCode() + "]尚未绑定设备，请检查！");
        }
        ReaderCheck readerCheck = rfidReaderService.checkReader(rfidReader);
        if (readerCheck.getStatus().equals(DictUtil.SUB)) {
            throw new MaginaException(readerCheck.getMessage());
        }
        this.setReaderStatus(DictUtil.SCANING, rfidAssetTakeReader);

        //发送扫描命令
        ExecutorUtils.doAfterCommit(
                () -> ReaderControlTool.sendScan(redisTemplate,rfidAssetTakeReader.getDeviceId(), rfidReader, DictUtil.TAKE_SCAN, code)
        );
        return Boolean.TRUE;
    }

    public Boolean deleteReaderCache(Long readerId, String keyType, String bindCode) {
        RfidReader rfidReader = rfidReaderServiceDomain.getById(readerId);
        String deviceId = rfidReader.getDeviceId();

        String key = DictUtil.SCAN_CACHE + DictUtil.STATUS + bindCode + ":" + deviceId;
        String scanCodeKey = DictUtil.SCAN_CACHE + DictUtil.SCAN_CODE+deviceId ;
        String scanTypeKey = DictUtil.SCAN_CACHE + DictUtil.SCAN_TYPE +deviceId;
        String resultKey = DictUtil.SCAN_CACHE + DictUtil.RESULT +keyType+":"+ bindCode + ":" + deviceId;
        redisTemplate.delete(resultKey);
        redisTemplate.delete(scanCodeKey);
        redisTemplate.delete(scanTypeKey);
        redisTemplate.delete(key);

        String redisCodeKey = DictUtil.BIND_CACHE + DictUtil.SCAN_CODE+ApplicationSessions.code()+":"+rfidReader.getDeviceId() ;
        if (redisTemplate.hasKey(redisCodeKey)) {
            redisTemplate.delete(redisCodeKey);
        }

        if(keyType.equals(DictUtil.LOOK_SCAN)){
            String redisKey = DictUtil.LOOK_CACHE +DictUtil.LIST+ bindCode;
            String lookReaderRedisKey = DictUtil.LOOK_CACHE + DictUtil.READER + bindCode;
            redisTemplate.delete(redisKey);
            redisTemplate.delete(lookReaderRedisKey);
        }
        return Boolean.TRUE;
    }

    public Boolean stopScan(String takeCode) {
        List<RfidAssetTakeReader> rfidAssetTakeReaders = rfidAssetTakeReaderServiceDomain.lambdaQuery()
                .eq(RfidAssetTakeReader::getTakeCode, takeCode).list();
        rfidAssetTakeReaders.forEach(v -> {
            if (v.getStatus().equals(DictUtil.SCANING)) {
                v.setStatus(0);
                rfidAssetTakeReaderServiceDomain.updateById(v);
                deleteReaderCache(v.getReaderId(), DictUtil.TAKE_SCAN, takeCode);
            }
        });
        return Boolean.TRUE;
    }

    public Boolean clearCache(String takeCode) {
        RfidAssetTake rfidAssetTake = rfidAssetTakeServiceDomain.lambdaQuery()
                .eq(RfidAssetTake::getCode, takeCode).one();

        //清除全局搜索缓存
        String lookReaderRedisKey = DictUtil.LOOK_CACHE + DictUtil.READER + rfidAssetTake.getCode();
        if (redisTemplate.hasKey(lookReaderRedisKey)) {
            LinkedHashSet<String> searchReaderList = (LinkedHashSet<String>) redisTemplate.opsForSet().members(lookReaderRedisKey);
            if (CollectionUtils.isNotEmpty(searchReaderList)) {
                searchReaderList.forEach(item -> {
                    String key = DictUtil.SCAN_CACHE + DictUtil.STATUS + takeCode + ":" + item;
                    String scanCodeKey = DictUtil.SCAN_CACHE + DictUtil.SCAN_CODE + item;
                    String scanTypeKey = DictUtil.SCAN_CACHE + DictUtil.SCAN_TYPE + item;
                    if (redisTemplate.hasKey(key)) {
                        redisTemplate.delete(key);
                    }
                    if (redisTemplate.hasKey(scanCodeKey)) {
                        redisTemplate.delete(scanCodeKey);
                    }
                    if (redisTemplate.hasKey(scanTypeKey)) {
                        redisTemplate.delete(scanTypeKey);
                    }
                });
                redisTemplate.delete(lookReaderRedisKey);
            }
            String lookRfidsRedisKey = DictUtil.LOOK_CACHE + DictUtil.LIST + rfidAssetTake.getCode();
            redisTemplate.delete(lookRfidsRedisKey);
            return true;
        }

        Long creator = ApplicationSessions.id();
        if (!creator.equals(rfidAssetTake.getCreatedBy().getId())) {
            return true;
        }
        //清除本次盘点缓存
        List<RfidAssetTakeReader> rfidAssetTakeReaders = rfidAssetTakeReaderServiceDomain.lambdaQuery()
                .eq(RfidAssetTakeReader::getTakeCode, takeCode).list();
        rfidAssetTakeReaders.forEach(v -> {
            deleteReaderCache(v.getReaderId(), DictUtil.TAKE_SCAN, takeCode);
        });


        return Boolean.TRUE;
    }

    /**
     * 搜索遗失资产
     *
     * @param id
     * @return
     */
    public boolean searchLose(Long id) {
        RfidAssetTake rfidAssetTake = rfidAssetTakeServiceDomain.getById(id);
        String lookRfidsRedisKey = DictUtil.LOOK_CACHE + DictUtil.LIST + rfidAssetTake.getCode();
        //查询当前盘点报损资产
        List<RfidAssetTakeDetail> rfidAssetTakeDetails = rfidAssetTakeDetailServiceDomain.lambdaQuery()
                .eq(RfidAssetTakeDetail::getTakeCode, rfidAssetTake.getCode())
                .eq(RfidAssetTakeDetail::getStatus, DiscrepancyStatus.WITHOUT)
                .eq(RfidAssetTakeDetail::getHasTag, true)
                .isNotNull(RfidAssetTakeDetail::getRfidCode)
                .list();
        if (CollectionUtils.isEmpty(rfidAssetTakeDetails)) {
            throw new MaginaException("当前盘点单未找到异常资产！");
        }
        //缓存要查找的rfid
        rfidAssetTakeDetails.forEach(v -> {
            redisTemplate.opsForSet().add(lookRfidsRedisKey, v.getRfidCode());
        });
        redisTemplate.expire(lookRfidsRedisKey, 30, TimeUnit.MINUTES);
        //查询当前已连接的基站
        List<RfidReader> rfidReaders = rfidReaderServiceDomain.lambdaQuery()
                .eq(RfidReader::getStatus, ReaderStatus.NORMAL)
                .list();
        if (CollectionUtils.isEmpty(rfidReaders)) {
            throw new MaginaException("当前无可用基站！");
        }
        String lookReaderRedisKey = DictUtil.LOOK_CACHE + DictUtil.READER + rfidAssetTake.getCode();
        String[] readerIds = rfidReaders.stream().map(RfidReader::getDeviceId).distinct().toArray(String[]::new);
        redisTemplate.opsForSet().add(lookReaderRedisKey, readerIds);

        for (RfidReader rfidReader : rfidReaders) {
            LinkedHashSet<String> targetRfidList = (LinkedHashSet<String>) redisTemplate.opsForSet().members(lookRfidsRedisKey);
            if (CollectionUtils.isEmpty(targetRfidList)) {
                break;
            }
            //发送扫描指令
            ExecutorUtils.run(()->ReaderControlTool.sendScan(redisTemplate,rfidReader.getDeviceId(), rfidReader, DictUtil.LOOK_SCAN, rfidAssetTake.getCode()));
        }
//        log.info("扫描完成，开始处理数据！");
//        redisTemplate.delete(lookRfidsRedisKey);
        return true;

    }


    /**
     * 全局搜索扫描是否完成
     *
     * @param code
     * @return
     */
    public boolean searchSchedule(String code) {
        String lookReaderRedisKey = DictUtil.LOOK_CACHE + DictUtil.READER + code;
        LinkedHashSet<String> searchReaderList = (LinkedHashSet<String>) redisTemplate.opsForSet().members(lookReaderRedisKey);
        if (CollectionUtils.isEmpty(searchReaderList)) {
            log.info("本次搜索无基站参与！");
            return true;
        }
        List<String> successDevices = new ArrayList<>();
        searchReaderList.forEach(item -> {
            String key = DictUtil.SCAN_CACHE + DictUtil.STATUS + code + ":" + item;
            if (redisTemplate.hasKey(key)) {
                String scanStatus = redisTemplate.opsForValue().get(key);
                if (DictUtil.RECEIVED.equals(scanStatus)) {
                    successDevices.add(item);
                }
            }
        });
        if (searchReaderList.size() == successDevices.size()) {
            return true;
        }
        return false;
    }

    /**
     * 手动完成盘点
     *
     * @param id
     * @return
     */
    public boolean complete(Long id) {
        RfidAssetTake rfidAssetTake = rfidAssetTakeServiceDomain.getById(id);
        if (Objects.isNull(rfidAssetTake)) {
            throw new MaginaException("盘点单不存在！");
        }
        rfidAssetTake.setTakeStatus(TakeStatus.YES);
        rfidAssetTake.setCompletedAt(LocalDateTime.now());
        rfidAssetTake.setUpdatedBy(ApplicationSessions.id());
        rfidAssetTake.setRemark("手动完成盘点");
        //更新明细 盘点中
        rfidAssetTakeDetailServiceDomain.lambdaUpdate()
                .set(RfidAssetTakeDetail::getTakeStatus, TakeStatus.YES)
                .set(RfidAssetTakeDetail::getStatus, DiscrepancyStatus.WITHOUT)
                .set(RfidAssetTakeDetail::getProcessType, ProcessType.IGNORE)
                .set(RfidAssetTakeDetail::getUpdatedBy, ApplicationSessions.id())
                .eq(RfidAssetTakeDetail::getTakeCode, rfidAssetTake.getCode())
                .ne(RfidAssetTakeDetail::getTakeStatus, TakeStatus.YES)
                .eq(RfidAssetTakeDetail::getStatus, DiscrepancyStatus.EXECUTING)
                .update();
        //更新明细
        rfidAssetTakeDetailServiceDomain.lambdaUpdate()
                .set(RfidAssetTakeDetail::getTakeStatus, TakeStatus.YES)
                .set(RfidAssetTakeDetail::getProcessType, ProcessType.IGNORE)
                .set(RfidAssetTakeDetail::getUpdatedBy, ApplicationSessions.id())
                .eq(RfidAssetTakeDetail::getTakeCode, rfidAssetTake.getCode())
                .ne(RfidAssetTakeDetail::getStatus, DiscrepancyStatus.EXECUTING)
                .ne(RfidAssetTakeDetail::getTakeStatus, TakeStatus.YES).update();
        return this.rfidAssetTakeServiceDomain.updateById(rfidAssetTake);
    }

    /**
     * 资产盘点审核
     *
     * @param rfidAssetTakeAuditDto
     * @return
     */
    public boolean approve(RfidAssetTakeAuditDto rfidAssetTakeAuditDto) {
        RfidAssetTake rfidAssetTake = rfidAssetTakeServiceDomain.getById(rfidAssetTakeAuditDto.getId());
        if (Objects.isNull(rfidAssetTake)) {
            throw new MaginaException("盘点单不存在！");
        }
        rfidAssetTake.setStatus(rfidAssetTakeAuditDto.getStatus());
        rfidAssetTake.setAuditAt(LocalDateTime.now());
        rfidAssetTake.setAuditorBy(ApplicationSessions.id());
        rfidAssetTake.setAuditName(ApplicationSessions.name());
        return this.rfidAssetTakeServiceDomain.updateById(rfidAssetTake);
    }

    private List<RfidAssetTakeReader> setTakeReader(Map<Long, List<RfidAsset>> readerAssetMap, List<RfidReader> rfidReaders, String takeCode) {
        rfidReaders = rfidReaders.stream().distinct().collect(Collectors.toList());
        return rfidReaders.stream().map(v -> {
            List<RfidAsset> rfidAssets = readerAssetMap.get(v.getId());
            RfidAssetTakeReader rfidAssetTakeReader = new RfidAssetTakeReader();
            List<Long> departmentIds = v.getDepartmentIds();
            if (CollectionUtil.isNotEmpty(departmentIds)) {
                List<DepartmentInfo> departmentInfos = departmentInfoDomainService.lambdaQuery()
                        .in(DepartmentInfo::getId, departmentIds).list();
                String departmentNames = Joiner.on(",").join(departmentInfos.stream().map(d -> {
                    return d.getName().getValue();
                }).collect(Collectors.toList()));
                rfidAssetTakeReader.setDepartmentNames(departmentNames);
            }
            if (CollectionUtils.isNotEmpty(rfidAssets)) {
                rfidAssetTakeReader.setAssetNum(rfidAssets.size());
                List<RfidAsset> hasTag = rfidAssets.stream().filter(asset -> {
                    return asset.getHasTag() && asset.getIsTake();
                }).collect(Collectors.toList());
                rfidAssetTakeReader.setBindNum(hasTag.size());
            }
            rfidAssetTakeReader.setTakeCode(takeCode);
            rfidAssetTakeReader.setReaderId(v.getId());
            rfidAssetTakeReader.setDeviceId(v.getDeviceId());
            rfidAssetTakeReader.setLocation(v.getLocation());
            rfidAssetTakeReader.setIpAddr(v.getIpAddr());
            rfidAssetTakeReader.setLocationId(v.getLocationId());
            rfidAssetTakeReader.setDepartmentIds(v.getDepartmentIds());
            rfidAssetTakeReader.setStatus(DictUtil.DISABLE);
            //检查基站是否正常
            ReaderCheck readerCheck = rfidReaderService.checkReader(v);
            rfidAssetTakeReader.setStatus(readerCheck.getStatus());
            rfidAssetTakeReader.setRemark(readerCheck.getMessage());
            return rfidAssetTakeReader;
        }).collect(Collectors.toList());
    }

    /**
     * 盘点数据上报
     *
     * @param takeCode
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean takeReport(List<String> rfidList, String deviceId, String takeCode) {

        RfidReader rfidReader = rfidReaderServiceDomain.selectJoinOne(RfidReader.class, MPJWrappers.<RfidReader>lambdaJoin()
                .select(RfidReader::getId).select(RfidReader::getDeviceId).select(RfidReader::getLocationId)
                .selectAs(Location::getFullName, RfidReader::getLocation)
                .leftJoin(Location.class, Location::getId, RfidReader::getLocationId)
                .eq(RfidReader::getDeviceId, deviceId)
        );

        log.info("{}完成盘点扫描，单号{}", rfidReader.getDeviceId(), takeCode);
        //标记基站本次盘点已完成
        RfidAssetTakeReader rfidAssetTakeReader = rfidAssetTakeReaderServiceDomain.lambdaQuery()
                .eq(RfidAssetTakeReader::getTakeCode, takeCode)
                .eq(RfidAssetTakeReader::getReaderId, rfidReader.getId())
                .one();
        if (Objects.isNull(rfidAssetTakeReader)) {
            ApiLogEvent apiLogEvent = new ApiLogEvent(this, MessageType.RECEIVE, CommandType.TAKE_STOCK,
                    deviceId, rfidReader.getCode(), "未匹配到本次盘点记录！", false);
            ApplicationContext applicationContext = SpringContentUtils.getApplicationContext();
            applicationContext.publishEvent(apiLogEvent);
            return false;
        }
        //标记本次盘点已完成
        rfidAssetTakeReader.setStatus(DictUtil.ENABLE);
        rfidAssetTakeReader.setRemark(null);
        rfidAssetTakeReader.setScanNum(rfidList.size());
        List<RfidAssetTakeDetail> rfidAssetTakeDetailList = rfidAssetTakeDetailServiceDomain.selectJoinList(RfidAssetTakeDetail.class, MPJWrappers.lambdaJoin()
                .selectAll(RfidAssetTakeDetail.class)
                .selectAs(RfidAsset::getPrice, RfidAssetTakeDetail::getPrice)
                .leftJoin(RfidAsset.class, RfidAsset::getId, RfidAssetTakeDetail::getAssetId)
                .eq(RfidAssetTakeDetail::getTakeCode, takeCode)
                .eq(RfidAssetTakeDetail::getIsTake, true)
                .isNotNull(RfidAssetTakeDetail::getRfidCode)
        );
        rfidAssetTakeDetailList.forEach(v -> {
            if (rfidList.contains(v.getRfidCode())) {
                List<Long> locationIds = new ArrayList<>();
                List<Long> readerIds = new ArrayList<>();
                String location = v.getScanLocation();
                String readerDevice = v.getScanReaderDevice();
                if (CollectionUtils.isNotEmpty(v.getScanLocationIds())) {
                    locationIds.addAll(v.getScanLocationIds());
                }
                if (CollectionUtils.isNotEmpty(v.getScanReaderIds())) {
                    readerIds.addAll(v.getScanReaderIds());
                }
                if (!locationIds.contains(rfidReader.getLocationId())) {
                    locationIds.add(rfidReader.getLocationId());
                    if (StringUtils.isNotBlank(location)) {
                        location = location + "," + rfidReader.getLocation();
                    } else {
                        location = rfidReader.getLocation();
                    }
                }
                if (!readerIds.contains(rfidReader.getId())) {
                    readerIds.add(rfidReader.getId());
                    if (StringUtils.isNotBlank(readerDevice)) {
                        readerDevice = readerDevice + "," + rfidReader.getDeviceId();
                    } else {
                        readerDevice = rfidReader.getDeviceId();
                    }
                }

                v.setScanLocation(location);
                v.setScanReaderDevice(readerDevice);
                v.setScanLocationIds(locationIds);
                v.setScanReaderIds(readerIds);
                v.setScanStatus(TakeScanStatus.FINISH);
                if (locationIds.contains(v.getLocationId())) {
                    v.setStatus(DiscrepancyStatus.NORMAL);
                    v.setTakeStatus(TakeStatus.YES);
                } else {
                    v.setStatus(DiscrepancyStatus.CHANGE);
                    v.setTakeStatus(TakeStatus.WAIT_DEAL);
                }
            }
        });
        rfidAssetTakeDetailServiceDomain.updateBatchById(rfidAssetTakeDetailList);

        rfidAssetTakeReaderServiceDomain.updateById(rfidAssetTakeReader);
        if (isComplete(takeCode, rfidAssetTakeReader.getReaderId())) {
            applicationEventPublisher.publishEvent(new AssetTakeEvent(takeCode));
        }
        return Boolean.TRUE;
    }

    /**
     * 判断是否完成盘点
     *
     * @param takeCode
     * @param readerId
     * @return
     */
    private boolean isComplete(String takeCode, Long readerId) {
        Long count = rfidAssetTakeReaderServiceDomain.lambdaQuery()
                .eq(RfidAssetTakeReader::getTakeCode, takeCode)
                .ne(RfidAssetTakeReader::getReaderId, readerId)
                .in(RfidAssetTakeReader::getStatus, Lists.newArrayList(DictUtil.DISABLE, DictUtil.SCANING)).count();
        return count == 0L;
    }

    /**
     * 盘点完成-核算
     *
     * @param takeCode
     * @return
     */
    public boolean complete(String takeCode) {
        RfidAssetTake rfidAssetTake = rfidAssetTakeServiceDomain.lambdaQuery().eq(RfidAssetTake::getCode, takeCode).one();
        //扫描到的rfid
        List<RfidInfo> rfidInfoList = rfidInfoServiceDomain.lambdaQuery()
                .eq(RfidInfo::getBindCode, takeCode)
                .eq(RfidInfo::getType, DictUtil.TAKE_SCAN)
                .eq(RfidInfo::getStatus, DictUtil.LOCATION)
                .list();
        //盘点资产 扫描状态需要扫描的
        List<RfidAssetTakeDetail> rfidAssetTakeDetailList = rfidAssetTakeDetailServiceDomain.selectJoinList(RfidAssetTakeDetail.class, MPJWrappers.lambdaJoin()
                .selectAll(RfidAssetTakeDetail.class)
                .selectAs(RfidAsset::getPrice, RfidAssetTakeDetail::getPrice)
                .leftJoin(RfidAsset.class, RfidAsset::getId, RfidAssetTakeDetail::getAssetId)
                .eq(RfidAssetTakeDetail::getTakeCode, takeCode)
        );

        rfidAssetTakeDetailList.forEach(v -> {
            if (!v.getStatus().equals(DiscrepancyStatus.NORMAL)&&!v.getStatus().equals(DiscrepancyStatus.OVERFLOW)) {
                v.setTakeStatus(TakeStatus.WAIT_DEAL);
                v.setStatus(DiscrepancyStatus.WITHOUT);
                v.setScanStatus(TakeScanStatus.FINISH);
                rfidAssetTakeDetailServiceDomain.updateById(v);
            }
        });

        //报溢资产
        List<String> dataRfids = rfidAssetTakeDetailList.stream().map(RfidAssetTakeDetail::getRfidCode).collect(Collectors.toList());
        List<String> rfidList = rfidInfoList.stream().map(RfidInfo::getRfidCode).distinct().collect(Collectors.toList());

        List<String> overList = CollectionUtil.subtractToList(rfidList, dataRfids);


        if (CollectionUtils.isNotEmpty(overList)) {
            List<RfidAssetTakeDetail> rfidAssetTakeDetails = Lists.newArrayList();
            Map<String, List<RfidInfo>> rfidInfoMap = rfidInfoList.stream().filter(v -> StringUtils.isNotBlank(v.getRfidCode())).collect(Collectors.groupingBy(RfidInfo::getRfidCode));
            for (String rfidCode : overList) {
                List<RfidInfo> rfidInfos = rfidInfoMap.get(rfidCode);
                if (CollectionUtils.isEmpty(rfidInfos)) {
                    continue;
                }
                RfidInfo rfidInfo = rfidInfos.get(0);
                //表示非本次盘点资产 无需记录到盘点明细里面
                if (rfidInfo.getAssetId() != null && rfidInfo.getLocationId().equals(rfidInfo.getAssetLocationId())) {
                    continue;
                }

                List<Long> locationIds = rfidInfos.stream().map(RfidInfo::getLocationId).distinct().collect(Collectors.toList());
                List<Long> readerIds = rfidInfos.stream().map(RfidInfo::getReaderId).distinct().collect(Collectors.toList());
                String scanLocation = rfidInfos.stream().map(RfidInfo::getLocation).distinct().collect(Collectors.joining(","));
                String scanReaderDevice = rfidInfos.stream().map(RfidInfo::getDeviceId).distinct().collect(Collectors.joining(","));
                RfidAssetTakeDetail rfidAssetTakeDetail = new RfidAssetTakeDetail();
                rfidAssetTakeDetail.setTakeCode(rfidAssetTake.getCode());
                rfidAssetTakeDetail.setRfidCode(rfidCode);
                rfidAssetTakeDetail.setAssetId(rfidInfo.getAssetId());
                rfidAssetTakeDetail.setScanLocation(scanLocation);
                rfidAssetTakeDetail.setScanLocationIds(locationIds);
                rfidAssetTakeDetail.setScanReaderIds(readerIds);
                rfidAssetTakeDetail.setScanReaderDevice(scanReaderDevice);
                RfidAsset rfidAsset = rfidAssetServiceDomain.selectJoinOne(RfidAsset.class, MPJWrappers.lambdaJoin()
                        .select(RfidAsset::getIsTake, RfidAsset::getIsTakeStatis, RfidAsset::getRfidCode, RfidAsset::getPrice, RfidAsset::getHasTag)
                        .selectAs(RfidReader::getDeviceId, RfidAsset::getReaderDeviceId)
                        .leftJoin(RfidReader.class, RfidReader::getId, RfidAsset::getReaderId)
                        .eq(RfidAsset::getRfidCode, rfidCode));
                if (rfidAsset != null) {
                    //判断是否忽略该标签 盘点范围为部门和人员的时候 可能只需要关注该基站位置是否扫描到当前负责的资产 所以需要忽略其他资产 不算异动
                    //盘人员的时候直接忽略其他资产
                    if (rfidAssetTake.getTakeRange().equals(TakeRangeEnums.USER)) {
                        continue;
                    }
                    //盘部门的时候直接忽略一个位置属于多个部门的非本次盘点部门的资产
                    if (rfidAssetTake.getTakeRange().equals(TakeRangeEnums.DEPARTMENT)) {
                        Location location = locationDomainService.getById(rfidInfo.getLocationId());
                        if (CollectionUtils.isNotEmpty(location.getDepartmentIds())
                                && location.getDepartmentIds().size() > 1) {
                            continue;
                        }
                    }
                    rfidAssetTakeDetail.setReaderId(rfidAsset.getReaderId());
                    rfidAssetTakeDetail.setDeviceId(rfidAsset.getReaderDeviceId());
                    rfidAssetTakeDetail.setHasTag(rfidAsset.getHasTag());
                    rfidAssetTakeDetail.setIsTake(rfidAsset.getIsTake());
                    rfidAssetTakeDetail.setIsTakeStatis(rfidAsset.getIsTakeStatis());
                    rfidAssetTakeDetail.setRfidCode(rfidAsset.getRfidCode());
                    rfidAssetTakeDetail.setPrice(rfidAsset.getPrice());

                    rfidAssetTakeDetail.setStatus(DiscrepancyStatus.CHANGE);
                } else {
                    rfidAssetTakeDetail.setStatus(DiscrepancyStatus.OVERFLOW);
                }
                rfidAssetTakeDetail.setManagerDeptId(rfidInfo.getManagerDeptId());
                rfidAssetTakeDetail.setManagerDeptName(rfidInfo.getManagerDeptName());
                rfidAssetTakeDetail.setManagerId(rfidInfo.getManagerId());
                rfidAssetTakeDetail.setManagerName(rfidInfo.getManagerName());
                rfidAssetTakeDetail.setDepartmentId(rfidInfo.getKeeperDeptId());
                rfidAssetTakeDetail.setDepartmentName(rfidInfo.getKeeperDeptName());
                rfidAssetTakeDetail.setApplyId(rfidInfo.getKeeperId());
                rfidAssetTakeDetail.setApplyName(rfidInfo.getKeeperName());
                rfidAssetTakeDetail.setLocation(rfidInfo.getAssetLocation());
                rfidAssetTakeDetail.setLocationId(rfidInfo.getAssetLocationId());
                rfidAssetTakeDetail.setScanStatus(TakeScanStatus.FINISH);
                rfidAssetTakeDetail.setTakeStatus(TakeStatus.WAIT_DEAL);
                if (CollectionUtils.isNotEmpty(rfidAssetTakeDetail.getScanLocationIds()) &&
                        rfidAssetTakeDetail.getScanLocationIds().contains(rfidAssetTakeDetail.getLocationId())) {

                }
                rfidAssetTakeDetails.add(rfidAssetTakeDetail);
            }
            rfidAssetTakeDetailServiceDomain.saveOrUpdateBatch(rfidAssetTakeDetails);
            //清理 缓存
            ExecutorUtils.doAfterCommit(() -> {
                    this.clearCache(takeCode);
            });
        }

        //盘点不正常资产
        List<RfidAssetTakeDetail> abnormalList = rfidAssetTakeDetailList.stream().filter(v -> {
            return !v.getStatus().equals(DiscrepancyStatus.NORMAL);
        }).collect(Collectors.toList());


        //盘点正常资产
        List<RfidAssetTakeDetail> normalList = rfidAssetTakeDetailList.stream().filter(v -> {
            return v.getStatus().equals(DiscrepancyStatus.NORMAL) && v.getTakeStatus().equals(TakeStatus.YES);
        }).collect(Collectors.toList());

        Long amount = normalList.stream().map(RfidAssetTakeDetail::getPrice).reduce(0L, Long::sum);
        rfidAssetTake.setAfterNum(normalList.size());
        rfidAssetTake.setNormalNum(normalList.size());
        rfidAssetTake.setAfterAmount(amount);
        rfidAssetTake.setSubstractAmount(rfidAssetTake.getBeforeAmount() - amount);
        rfidAssetTake.setSubstractNum(rfidAssetTake.getBeforeNum() - rfidAssetTake.getAfterNum());
        rfidAssetTake.setTakeStatus(TakeStatus.YES);
        rfidAssetTake.setStatus(DiscrepancyStatus.NORMAL);
        rfidAssetTake.setScanStatus(TakeScanStatus.FINISH);
        if (CollectionUtils.isNotEmpty(abnormalList)) {
            rfidAssetTake.setTakeStatus(TakeStatus.WAIT_DEAL);
            boolean result = rfidAssetTake.getBeforeNum().compareTo(rfidAssetTake.getAfterNum()) > 0;
            rfidAssetTake.setStatus(result ? DiscrepancyStatus.WITHOUT : DiscrepancyStatus.OVERFLOW);
        }
        if (TakeStatus.YES.equals(rfidAssetTake.getTakeStatus())) {
            rfidAssetTake.setCompletedAt(LocalDateTime.now());
        }
        return rfidAssetTakeServiceDomain.updateById(rfidAssetTake);
    }

    public void repeatComplete(String takeCode) {
        List<RfidAssetTakeDetail> rfidAssetTakeDetailList = rfidAssetTakeDetailServiceDomain.selectJoinList(RfidAssetTakeDetail.class, MPJWrappers.lambdaJoin()
                .selectAll(RfidAssetTakeDetail.class)
                .selectAs(RfidAsset::getPrice, RfidAssetTakeDetail::getPrice)
                .leftJoin(RfidAsset.class, RfidAsset::getId, RfidAssetTakeDetail::getAssetId)
                .eq(RfidAssetTakeDetail::getTakeCode, takeCode)
        );

        List<RfidAssetTakeReader> rfidAssetTakeReaders = this.rfidAssetTakeReaderServiceDomain.lambdaQuery()
                .eq(RfidAssetTakeReader::getTakeCode, takeCode).list();

        rfidAssetTakeReaders.forEach(takeReader -> {
            List<RfidAssetTakeDetail> rfidAssetTakeDetails = rfidAssetTakeDetailList.stream()
                    .filter(v -> CollectionUtils.isNotEmpty(v.getScanReaderIds()) && v.getScanReaderIds().contains(takeReader.getReaderId())
                            && v.getStatus().equals(DiscrepancyStatus.NORMAL)
                    ).collect(Collectors.toList());
            takeReader.setScanNum(rfidAssetTakeDetails.size());
        });
        rfidAssetTakeReaderServiceDomain.updateBatchById(rfidAssetTakeReaders);
        RfidAssetTake rfidAssetTake = rfidAssetTakeServiceDomain.lambdaQuery().eq(RfidAssetTake::getCode, takeCode).one();

        //正常资产
        List<RfidAssetTakeDetail> normalList = rfidAssetTakeDetailList.stream().filter(v -> {
            return v.getStatus().equals(DiscrepancyStatus.NORMAL);
        }).collect(Collectors.toList());
        //忽略资产数量
        List<RfidAssetTakeDetail> ignoreList = rfidAssetTakeDetailList.stream().filter(v -> {
            return v.getProcessType() != null && v.getProcessType().equals(ProcessType.IGNORE);
        }).collect(Collectors.toList());
        //处理资产数量
        List<RfidAssetTakeDetail> dealList = rfidAssetTakeDetailList.stream().filter(v -> {
            return v.getProcessType() != null && !v.getProcessType().equals(ProcessType.IGNORE);
        }).collect(Collectors.toList());
        rfidAssetTake.setNormalNum(normalList.size());
        rfidAssetTake.setIgnoreNum(ignoreList.size());
        rfidAssetTake.setDealNum(dealList.size());
        Long normalAmount = normalList.stream().map(RfidAssetTakeDetail::getPrice).reduce(0L, Long::sum);
        Long ignoreAmount = ignoreList.stream().map(RfidAssetTakeDetail::getPrice).reduce(0L, Long::sum);
        Long dealAmount = dealList.stream().map(RfidAssetTakeDetail::getPrice).reduce(0L, Long::sum);
        rfidAssetTake.setAfterNum(rfidAssetTake.getNormalNum() + rfidAssetTake.getIgnoreNum() + rfidAssetTake.getDealNum());
        rfidAssetTake.setAfterAmount(normalAmount + ignoreAmount + dealAmount);
        rfidAssetTake.setSubstractAmount(rfidAssetTake.getBeforeAmount() - rfidAssetTake.getAfterAmount());
        rfidAssetTake.setSubstractNum(rfidAssetTake.getBeforeNum() - rfidAssetTake.getAfterNum());
        rfidAssetTake.setTakeStatus(TakeStatus.YES);
        rfidAssetTake.setStatus(DiscrepancyStatus.NORMAL);
        if (!rfidAssetTake.getBeforeNum().equals(rfidAssetTake.getAfterNum())) {
            rfidAssetTake.setTakeStatus(TakeStatus.WAIT_DEAL);
            boolean result = rfidAssetTake.getBeforeNum().compareTo(rfidAssetTake.getAfterNum()) > 0;
            rfidAssetTake.setStatus(result ? DiscrepancyStatus.WITHOUT : DiscrepancyStatus.OVERFLOW);
        }
        if (TakeStatus.YES.equals(rfidAssetTake.getTakeStatus())) {
            rfidAssetTake.setCompletedAt(LocalDateTime.now());
        }
        rfidAssetTake.setScanStatus(TakeScanStatus.FINISH);
        rfidAssetTakeServiceDomain.updateById(rfidAssetTake);
    }

    /**
     * 获取盘点类型
     *
     * @return
     */
    public List<DictItemVo> getTakeType() {
        List<DictItemVo> takeTypes = dictItemApiService.getItemList(DictUtil.DICT_TAKE_TYPE);
        String roleCode = userInfoDomainService.getCurrRoleCode();
        //负责人查看自己负责的部门
        if (DictUtil.MANAGER_ROLE.equals(roleCode)) {
            return takeTypes;
        }
        //员工查看自己部门
        if (DictUtil.STAFF_ROLE.equals(roleCode)) {
            return takeTypes.stream().filter(v -> v.getValue().equals(DictUtil.TAKE_TYPE_USER)).collect(Collectors.toList());
        }
        //部门负责人
        if (DictUtil.LEADER_ROLE.equals(roleCode)) {
            return takeTypes.stream().filter(v -> !v.getValue().equals(DictUtil.TAKE_TYPE_ALL)).collect(Collectors.toList());
        }
        UserInfo userInfo = userInfoDomainService.getCurrUser();
        if (userInfo.getIsBuildIn()) {
            return takeTypes;
        }
        return null;
    }

    public boolean delete(Long id) {
        RfidAssetTake rfidAssetTake = rfidAssetTakeServiceDomain.getById(id);
        if (Objects.isNull(rfidAssetTake)) {
            throw new MaginaException("盘点单不存在！");
        }
        if (rfidAssetTake.getTakeStatus().equals(TakeStatus.YES)) {
            throw new MaginaException("盘点已完成，不能删除！");
        }
        if (!rfidAssetTake.getCreatedBy().getId().equals(ApplicationSessions.id())) {
            throw new MaginaException("只能删除自己创建的数据！");
        }
        rfidAssetTakeReaderServiceDomain.remove(Wrappers.<RfidAssetTakeReader>lambdaQuery()
                .eq(RfidAssetTakeReader::getTakeCode, rfidAssetTake.getCode()));

        rfidAssetTakeDetailServiceDomain.remove(Wrappers.<RfidAssetTakeDetail>lambdaQuery()
                .eq(RfidAssetTakeDetail::getTakeCode, rfidAssetTake.getCode()));

        rfidInfoServiceDomain.remove(Wrappers.<RfidInfo>lambdaQuery()
                .eq(RfidInfo::getBindCode, rfidAssetTake.getCode()));

        return rfidAssetTakeServiceDomain.removeById(id);
    }

    public boolean clear() {
        rfidAssetTakeDetailServiceDomain.clear();
        rfidAssetTakeReaderServiceDomain.clear();
        rfidAssetTakeServiceDomain.clear();
        return true;
    }
}
