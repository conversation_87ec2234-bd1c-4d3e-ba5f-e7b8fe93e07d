package com.hightop.benyin.rfid.application.vo.excel;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelIgnore;
import com.hightop.benyin.configurer.annotation.RecordLogField;
import com.hightop.benyin.configurer.enums.DataType;
import com.hightop.benyin.rfid.infrastructure.entity.RfidReader;
import com.hightop.benyin.share.application.vo.ExcelBaseInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * 资产-导入实体
 *
 * <AUTHOR>
 * @date 2023-11-06 14:53:13
 */
@Data
@ApiModel("基站-导入实体")
public class Readerxcel extends ExcelBaseInfo {


    @Excel(name = "基站编码", width = 30, orderNum = "1")
    @ApiModelProperty("基站编码")
    @NotBlank
    String code;

    @Excel(name = "设备ID", width = 30, orderNum = "2")
    @ApiModelProperty("设备ID")
    @NotBlank
    String deviceId;

    @Excel(name = "所在位置", width = 30, orderNum = "3")
    @ApiModelProperty("所在位置")
    String location;

    @Excel(name = "位置编码", width = 30, orderNum = "4")
    @ApiModelProperty("位置编码")
    @NotBlank
    String locationCode;

    @ApiModelProperty("启停状态")
    @Excel(name = "启停状态", width = 60, orderNum = "5")
    String isEnableStr;

    @ExcelIgnore
    Boolean isEnable;


    @ApiModelProperty("心跳间隔默认20")
    @Excel(name = "心跳间隔", width = 20, orderNum = "6")
    Integer heatInterval;

    @ApiModelProperty("扫描间隔")
    @Excel(name = "扫描间隔", width = 20, orderNum = "7")
    Integer scanInterval;

    @ApiModelProperty("扫描状态")
    @Excel(name = "扫描状态", width = 20, orderNum = "8")
    String scanStatusStr;

    @ApiModelProperty("扫描状态")
    @ExcelIgnore
    Integer scanStatus;


    @ApiModelProperty("蜂鸣器开关1：开启 2：关闭")
    @Excel(name = "蜂鸣器开关", width = 20, orderNum = "9")
    String bellStatusStr;

    @ApiModelProperty("扫描状态")
    @ExcelIgnore
    Integer bellStatus;

    @ApiModelProperty("节能状态1：开启 2：关闭")
    @Excel(name = "节能状态", width = 20, orderNum = "10")
    String energyStatusStr;

    @ApiModelProperty("节能状态")
    @ExcelIgnore
    Integer energyStatus;

    @ApiModelProperty("电压")
    @Excel(name = "电压", width = 20, orderNum = "11")
    Integer voltage;


    @ApiModelProperty("天线功率1")
    @Excel(name = "天线功率1", width = 20, orderNum = "12")
    Integer power1;


    @ApiModelProperty("天线功率2")
    @Excel(name = "天线功率2", width = 20, orderNum = "13")
    Integer power2;


    @ApiModelProperty("天线功率3")
    @Excel(name = "天线功率3", width = 20, orderNum = "14")
    Integer power3;


    @ApiModelProperty("天线功率4")
    @Excel(name = "天线功率4", width = 20, orderNum = "15")
    Integer power4;

    @ApiModelProperty("位置id")
    @ExcelIgnore
    Long locationId;

    @ApiModelProperty("原实体")
    @ExcelIgnore
    RfidReader  oriReader;


}
