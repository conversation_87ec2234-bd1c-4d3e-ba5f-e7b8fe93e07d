package com.hightop.benyin.rfid.application.vo.query;

import com.hightop.fario.common.mybatis.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldDefaults;

import java.util.List;

/**
 * RFID基站查询DTO
 *
 * <AUTHOR>
 * @date 2024-10-12 16:36:01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@FieldDefaults(level = AccessLevel.PRIVATE)
@ApiModel("RFID查询DTO")
public class RfidInfoQuery extends PageQuery {

    @ApiModelProperty("编码")
    String rfidCode;

    @ApiModelProperty("基站编码")
    String readerCode;

    @ApiModelProperty("基站id")
    Long readerId;

    @ApiModelProperty("位置id")
    Long locationId;

    @ApiModelProperty("资产名称")
    String assetName;


    @ApiModelProperty("资产编码")
    String assetCode;

    @ApiModelProperty("资产型号")
    String model;

    @ApiModelProperty("所属单位")
    String departmentName;

    @ApiModelProperty("位置")
    List<String> locations;

    @ApiModelProperty("状态")
    List<Integer> status;

    @ApiModelProperty("排除类型")
    String neType;

    @ApiModelProperty("设备id")
    String deviceId;

    @ApiModelProperty("扫描单号")
    String bindCode;

    @ApiModelProperty("标签绑定")
    Boolean bindTag;

    @ApiModelProperty("上报类型")
    String type;

    @ApiModelProperty("上报类型")
    List<String> types;

    @ApiModelProperty("开始日期")
    String startDate;
    @ApiModelProperty("结束日期")
    String endDate;
    @ApiModelProperty("上报次数")
    Integer startCounts;
    @ApiModelProperty("上报次数")
    Integer endCounts;

}
