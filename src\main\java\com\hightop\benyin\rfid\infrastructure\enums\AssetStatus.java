package com.hightop.benyin.rfid.infrastructure.enums;

import com.hightop.magina.core.custom.entry.EnumEntry;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

/**
 * 资产状态
 *
 * <AUTHOR>
 * @date 2024/5/15 13:42
 */
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Getter
public enum AssetStatus implements EnumEntry<String> {
    /**
     * 申请
     */
    WAIT_IN("待入库"),

    /**
     * 登记入库
     */
    INSTORE("登记入库"),

    /**
     * 领用申请中
     */
    GET_APPLY("申请领用中"),

    /**
     * 领用中
     */
    APPLY("已领用"),

    /**
     * 申请退还中
     */
    RETURN_APPLY("申请退还中"),


    /**
     * 申请调拨入库
     */
    ALLOT_IN("申请调拨入库"),

    /**
     * 已调拨
     */
    ALLOT("已调拨"),

    /**
     * 归还
     */
    ALLOT_OUT("申请调拨出库"),


    /**
     * 维修中
     */
    REPAIR("维修中"),

    /**
     * 报损
     */
    BREAKAGE("报损");

    /**
     * 状态名称
     */
    String name;

    @Override
    public String getCode() {
        // 以枚举名称作为标识
        return super.name();
    }

    @Override
    public String getName() {
        return this.name;
    }

}
