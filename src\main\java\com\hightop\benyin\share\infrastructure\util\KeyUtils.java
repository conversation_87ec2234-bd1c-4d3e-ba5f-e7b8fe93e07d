package com.hightop.benyin.share.infrastructure.util;

import com.hightop.fario.base.Pair;
import com.hightop.fario.common.crypto.cipher.asymmetric.Sm2Cipher;
import com.hightop.fario.common.crypto.codec.CipherCodec;
import com.hightop.magina.casual.key.DecryptUnit;
import com.hightop.magina.core.exception.MaginaException;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;

/**
 * 临时密钥领域服务
 *
 * <AUTHOR>
 * @date 2022/07/08 11:18
 * @since 2.0.0
 */
@Slf4j
public class KeyUtils {

    public static String decrypt(String privateKey, String cipher) {
        String[] result = new String[1];
        KeyUtils.decryptStr(privateKey, new DecryptUnit(cipher, s -> result[0] = s));
        return result[0];
    }

    /**
     * 多个密文解密
     *
     * @param privateKey 令牌
     * @param units      解密对
     */
    public static void decryptStr(String privateKey, DecryptUnit... units) {
        Objects.requireNonNull(units);

        if (Objects.isNull(privateKey)) {
            throw new MaginaException("私钥已过期");
        }

        for (DecryptUnit pair : units) {
            Pair<Boolean, String> decrypt =
                    Sm2Cipher.of()
                            .decrypt(privateKey, CipherCodec.LOWER_HEX, pair.getCipher(), CipherCodec.LOWER_HEX)
                            .pair();

            if (!decrypt.getFirst()) {
                log.error("密文{}，密钥{}，解密失败{}", pair.getCipher(), privateKey, decrypt.getSecond());
                throw new MaginaException("解密失败");
            }
            // 考虑加密时添加一位对齐位 避免加密内容为空字符串 解密忽略对齐位
            pair.getConsumer().accept(decrypt.getSecond().substring(1));
        }
    }

}
