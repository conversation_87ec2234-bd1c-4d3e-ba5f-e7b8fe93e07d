package com.hightop.benyin.rfid.infrastructure.enums;

import com.hightop.magina.core.custom.entry.EnumEntry;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

/**
 * 资产变更来源
 * <AUTHOR>
 * @date 2024/5/15 13:42
 */
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Getter
public enum AssetChangeSource implements EnumEntry<String> {

    /**
     * 数据修改
     */
    DATA("数据修改"),
    /**
     * 数据修改
     */
    FLOW("领借申请"),
    /**
     * 数据修改
     */
    USER("人员信息"),
    /**
     * 位置数据
     */
    LOCATION("位置信息"),
    /**
     * 部门位置
     */
    DEPARTMENT("部门信息"),
    /**
     * 基站位置
     */
    READER("基站信息"),
    /**
     *变更单
     */
    TRANSFER("变更单"),
    /**
     * 异动上报
     */
    VARIATION("异动上报");

    /**
     * 状态名称
     */
    String name;

    @Override
    public String getCode() {
        // 以枚举名称作为标识
        return super.name();
    }

    @Override
    public String getName() {
        return this.name;
    }

}
