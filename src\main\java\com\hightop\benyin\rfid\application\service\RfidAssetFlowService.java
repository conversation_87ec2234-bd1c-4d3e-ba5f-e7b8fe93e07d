package com.hightop.benyin.rfid.application.service;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.yulichang.toolkit.MPJWrappers;
import com.google.common.collect.Lists;
import com.hightop.benyin.rfid.application.vo.dto.AssetApplyDto;
import com.hightop.benyin.rfid.application.vo.dto.AssetChangeLogDto;
import com.hightop.benyin.rfid.application.vo.dto.AssetFlowAuditDto;
import com.hightop.benyin.rfid.application.vo.dto.AssetSubmitDto;
import com.hightop.benyin.rfid.application.vo.query.RfidAssetFlowQuery;
import com.hightop.benyin.rfid.application.vo.query.RfidAssetRecordQuery;
import com.hightop.benyin.rfid.domain.service.*;
import com.hightop.benyin.rfid.infrastructure.entity.*;
import com.hightop.benyin.rfid.infrastructure.enums.*;
import com.hightop.benyin.share.domain.service.SequenceDomainService;
import com.hightop.benyin.share.infrastructure.util.DictUtil;
import com.hightop.benyin.share.infrastructure.util.DownloadResponseUtil;
import com.hightop.benyin.system.domain.service.LocationDomainService;
import com.hightop.benyin.system.domain.service.UserInfoDomainService;
import com.hightop.benyin.system.infrastructure.entity.DepartmentInfo;
import com.hightop.benyin.system.infrastructure.entity.Location;
import com.hightop.benyin.system.infrastructure.entity.UserInfo;
import com.hightop.fario.base.util.CollectionUtils;
import com.hightop.fario.base.util.StringUtils;
import com.hightop.fario.common.core.util.ExecutorUtils;
import com.hightop.fario.common.mybatis.DataGrid;
import com.hightop.fario.common.mybatis.PageQuery;
import com.hightop.fario.common.mybatis.util.PageHelper;
import com.hightop.magina.casual.session.ApplicationSessions;
import com.hightop.magina.core.exception.MaginaException;
import com.hightop.magina.standard.code.dictionary.bind.DictItemEntry;
import com.hightop.magina.standard.ums.department.Department;
import com.hightop.magina.standard.ums.department.DepartmentDomainService;
import com.hightop.magina.standard.ums.user.basic.UserBasic;
import com.hightop.magina.standard.ums.user.bind.UserEntry;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 资产流水服务
 *
 * <AUTHOR>
 * @date 2024-10-12 16:36:01
 */
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Service
@Slf4j
public class RfidAssetFlowService {

    RfidAssetFlowServiceDomain rfidAssetFlowServiceDomain;
    RfidAssetServiceDomain rfidAssetServiceDomain;
    RfidAssetService rfidAssetService;
    RfidAssetRecordServiceDomain rfidAssetRecordServiceDomain;
    RfidReaderServiceDomain rfidReaderServiceDomain;
    SequenceDomainService sequenceDomainService;
    UserInfoDomainService userInfoDomainService;
    DepartmentDomainService departmentDomainService;
    RfidAssetApplyServiceDomain rfidAssetApplyServiceDomain;
    LocationDomainService locationDomainService;
    RfidReaderService rfidReaderService;

    /**
     * 资产流水分页查询
     *
     * @param pageQuery {@link PageQuery}
     * @return {@link DataGrid}
     */
    public DataGrid<RfidAssetFlow> page(RfidAssetFlowQuery pageQuery) {

        String role = userInfoDomainService.getCurrRoleCode();
        //负责人查看自己负责的部门
        if (!ApplicationSessions.code().equals(DictUtil.ADMIN)) {

            if (DictUtil.LEADER_ROLE.equals(role)) {
                List<Long> departmentInfos = userInfoDomainService.getCurrDeptIds();
                pageQuery.setDeptIds(departmentInfos);
            }
            //员工查看自己部门
            if (DictUtil.STAFF_ROLE.equals(role)) {
                pageQuery.setUserId(ApplicationSessions.id());
            }
        }


        return PageHelper.startPage(pageQuery, p ->
                this.searchList(pageQuery)).peek(v -> {
                    if (v.getOperateType()!=null&&v.getOperateType().equals(AssetBusinessType.BIND)) {

                        List<RfidAsset> rfidAssetList = rfidAssetServiceDomain.selectJoinList(RfidAsset.class, MPJWrappers.lambdaJoin()
                                .select(RfidAsset::getId, RfidAsset::getHasTag, RfidAsset::getRfidCode, RfidAsset::getReaderId, RfidAsset::getLocationId)
                                .eq(RfidAsset::getLocationId, v.getLocationId()));

                        v.setTotalNum(rfidAssetList.size());
                        v.setNeedBindNum(rfidAssetList.stream().filter(c -> c.getHasTag()).count());
                    }
                }
        );
    }

    private List<RfidAssetFlow> searchList(RfidAssetFlowQuery pageQuery) {
        return this.rfidAssetFlowServiceDomain.selectJoinList(RfidAssetFlow.class, MPJWrappers.lambdaJoin()
                .selectAll(RfidAssetFlow.class)
                .selectAs(Department::getName, RfidAssetFlow::getCreateDeptName)
                .leftJoin(Department.class, Department::getId, RfidAssetFlow::getCreateDept)
                .leftJoin(UserBasic.class, UserBasic::getId, RfidAssetFlow::getCreatedBy)
                .ne(CollectionUtils.isEmpty(pageQuery.getOperateTypes()), RfidAssetFlow::getOperateType, AssetBusinessType.BIND.getCode())
                .like(StringUtils.isNotBlank(pageQuery.getCode()), RfidAssetFlow::getCode, pageQuery.getCode())
                .like(StringUtils.isNotBlank(pageQuery.getLocation()), RfidAssetFlow::getLocation, pageQuery.getLocation())
                .like(StringUtils.isNotBlank(pageQuery.getDeviceId()), RfidAssetFlow::getDeviceId, pageQuery.getLocation())

                .in(CollectionUtils.isNotEmpty(pageQuery.getOperateTypes()), RfidAssetFlow::getOperateType, pageQuery.getOperateTypes())
                .in(CollectionUtils.isNotEmpty(pageQuery.getDepartmentIds()), RfidAssetFlow::getDepartmentId, pageQuery.getDepartmentIds())
                .in(CollectionUtils.isNotEmpty(pageQuery.getStatus()), RfidAssetFlow::getStatus, pageQuery.getStatus())
                .in(CollectionUtils.isNotEmpty(pageQuery.getDeptIds()), RfidAssetFlow::getCreateDept, pageQuery.getDeptIds())
                .like(StringUtils.isNotBlank(pageQuery.getDepartmentName()), Department::getName, pageQuery.getDepartmentName())
                .like(StringUtils.isNotBlank(pageQuery.getApproveName()), RfidAssetFlow::getApproveName, pageQuery.getApproveName())
                .like(StringUtils.isNotBlank(pageQuery.getApplyName()), RfidAssetFlow::getApplyName, pageQuery.getApplyName())
                .like(StringUtils.isNotBlank(pageQuery.getCreatedBy()), UserBasic::getName, pageQuery.getCreatedBy())
                .eq(pageQuery.getUserId() != null, UserBasic::getId, pageQuery.getUserId())
                .ge(StringUtils.isNotBlank(pageQuery.getStartDate()), RfidAssetFlow::getCreatedAt, pageQuery.getStartDate() + " 00:00:00")
                .le(StringUtils.isNotBlank(pageQuery.getEndDate()), RfidAssetFlow::getCreatedAt, pageQuery.getEndDate() + " 23:59:59")
                .ge(StringUtils.isNotBlank(pageQuery.getStartApproveDate()), RfidAssetFlow::getApproveAt, pageQuery.getStartApproveDate() + " 00:00:00")
                .le(StringUtils.isNotBlank(pageQuery.getEndApproveDate()), RfidAssetFlow::getApproveAt, pageQuery.getEndApproveDate() + " 23:59:59")
                .ge(StringUtils.isNotBlank(pageQuery.getStartApplyDate()), RfidAssetFlow::getApplyAt, pageQuery.getStartApplyDate() + " 00:00:00")
                .le(StringUtils.isNotBlank(pageQuery.getEndApplyDate()), RfidAssetFlow::getApplyAt, pageQuery.getEndApplyDate() + " 23:59:59")
                .orderByDesc(RfidAssetFlow::getCreatedAt)
        );
    }


    /**
     * 资产流水分页查询
     *
     * @param pageQuery {@link PageQuery}
     * @return {@link DataGrid}
     */
    public DataGrid<RfidAssetRecord> recordPage(RfidAssetRecordQuery pageQuery) {
        return PageHelper.startPage(pageQuery, p -> rfidAssetRecordServiceDomain.lambdaQuery()
                .eq(StringUtils.isNotBlank(pageQuery.getStatus()), RfidAssetRecord::getStatus, pageQuery.getStatus())
                .eq(pageQuery.getFlowId() != null, RfidAssetRecord::getFlowId, pageQuery.getFlowId())
                .orderByDesc(RfidAssetRecord::getCreatedAt)
                .list()
        );
    }

    public boolean remove(Long id) {
        RfidAssetFlow rfidAssetFlow = rfidAssetFlowServiceDomain.getById(id);
        if (rfidAssetFlow.getStatus().equals(AssetFlowStatus.PASS)) {
            throw new MaginaException("当前业务状态已通过审核");
        }
        if (rfidAssetFlow.getOperateType().equals(AssetBusinessType.PURCHASE)
                || rfidAssetFlow.getOperateType().equals(AssetBusinessType.DONATE_IN)
        ) {
            //删除资产基本信息
            rfidAssetServiceDomain.remove(Wrappers.<RfidAsset>lambdaQuery()
                    .eq(RfidAsset::getSignCode, rfidAssetFlow.getCode()));
        } else {
            rfidAssetApplyServiceDomain.remove(
                    Wrappers.<RfidAssetApply>lambdaQuery().eq(RfidAssetApply::getFlowCode, rfidAssetFlow.getCode()));
        }
        rfidAssetRecordServiceDomain.remove(Wrappers.<RfidAssetRecord>lambdaQuery()
                .eq(RfidAssetRecord::getFlowId, id));
        return this.rfidAssetFlowServiceDomain.removeById(id);
    }

    /**
     * 资产登记流程添加
     *
     * @param assetSubmitDto {@link RfidAssetFlow}
     * @return true/false
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean submit(AssetSubmitDto assetSubmitDto, Integer number, Long price) {
        if (assetSubmitDto.getRfidAsset() != null) {
            rfidAssetService.stash(assetSubmitDto.getRfidAsset());
        }
        RfidAssetFlow rfidAssetFlow = new RfidAssetFlow();
        rfidAssetFlow.setCreatedBy(new UserEntry().setId(ApplicationSessions.id()));
        rfidAssetFlow.setCode(assetSubmitDto.getCode());
        rfidAssetFlow.setOperateType(assetSubmitDto.getOperateType());
        rfidAssetFlow.setExternalUnit(assetSubmitDto.getExternalUnit());
        rfidAssetFlow.setExternalDept(assetSubmitDto.getExternalDept());
        rfidAssetFlow.setExternalPerson(assetSubmitDto.getExternalPerson());
        rfidAssetFlow.setApplyId(assetSubmitDto.getApplyId());
        rfidAssetFlow.setApplyAt(assetSubmitDto.getApplyAt());

        UserInfo userInfo = userInfoDomainService.getCurrUser();
        rfidAssetFlow.setCreateDept(userInfo.getDepartmentId());
        if (userInfo.getDepartmentId() != null) {
            Department department = departmentDomainService.getById(userInfo.getDepartmentId());
            rfidAssetFlow.setDepartmentName(department != null ? department.getName().getValue() : null);
        }
        rfidAssetFlow.setDepartmentId(userInfo.getDepartmentId());
        rfidAssetFlow.setStatus(AssetFlowStatus.WAIT_APPROVE);
        if (number == null) {
            List<RfidAsset> rfidAssets = rfidAssetServiceDomain.lambdaQuery().eq(RfidAsset::getSignCode, assetSubmitDto.getCode()).list();
            if (CollectionUtils.isEmpty(rfidAssets)) {
                throw new MaginaException("未找到登记资产！");
            }
            Long amount = rfidAssets.stream().filter(v -> v.getPrice() != null).mapToLong(RfidAsset::getPrice).sum();
            rfidAssetFlow.setAmount(amount);
            rfidAssetFlow.setNumber(rfidAssets.size());
        } else {
            rfidAssetFlow.setNumber(number);
            rfidAssetFlow.setAmount(price);
        }
        this.rfidAssetFlowServiceDomain.saveOrUpdate(rfidAssetFlow);

        //变更资产状态
        rfidAssetServiceDomain.lambdaUpdate()
                .set(RfidAsset::getSignCode, rfidAssetFlow.getCode())
                .set(RfidAsset::getStatus, AssetApplyStatus.WAIT_APPROVE)
                .eq(RfidAsset::getSignCode, assetSubmitDto.getCode()).update();

        RfidAssetRecord rfidAssetRecord = new RfidAssetRecord();
        rfidAssetRecord.setStatus(rfidAssetFlow.getStatus());
        rfidAssetRecord.setFlowId(rfidAssetFlow.getId());
        rfidAssetRecord.setContent(AssetRecord.ADD.getName());
        rfidAssetRecord.setCreatedBy(rfidAssetFlow.getCreatedBy());
        rfidAssetRecordServiceDomain.save(rfidAssetRecord);

        ExecutorUtils.doAfterCommit(() -> {
            List<RfidAsset> assets = rfidAssetServiceDomain.lambdaQuery().eq(RfidAsset::getSignCode, rfidAssetFlow.getCode()).list();
            List<RfidAssetApply> rfidAssetApplies = Lists.newArrayList();
            for (RfidAsset asset : assets) {
                RfidAssetApply assetApply = new RfidAssetApply();
                assetApply.setAssetId(asset.getId());
                assetApply.setRfidCode(asset.getRfidCode());
                assetApply.setFlowCode(rfidAssetFlow.getCode());
                assetApply.setCreatedBy(asset.getCreatedBy());
                rfidAssetApplies.add(assetApply);
            }
            rfidAssetApplyServiceDomain.saveBatch(rfidAssetApplies);
        });
        return Boolean.TRUE;
    }

    public Boolean inStock(RfidAsset rfidasset) {
        String code = rfidAssetService.stash(rfidasset);
        AssetSubmitDto assetSubmitDto = new AssetSubmitDto();
        assetSubmitDto.setCode(code);
        assetSubmitDto.setOperateType(rfidasset.getOperateType());
        assetSubmitDto.setExternalUnit(rfidasset.getExternalUnit());
        assetSubmitDto.setExternalDept(rfidasset.getExternalDept());
        assetSubmitDto.setExternalPerson(rfidasset.getExternalPerson());
        assetSubmitDto.setApplyId(rfidasset.getApplyId());
        assetSubmitDto.setApplyAt(rfidasset.getApplyAt());
        return this.submit(assetSubmitDto, 1, rfidasset.getPrice());
    }


    /**
     * 资产领借用流程添加
     *
     * @param assetApplyDto {@link RfidAssetFlow}
     * @return true/false
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean apply(AssetApplyDto assetApplyDto) {
        assetApplyDto.setStatus(AssetApplyStatus.PROCESS);
        RfidAssetFlow rfidAssetFlow = null;
        if (assetApplyDto.getId() != null) {
            rfidAssetFlow = rfidAssetFlowServiceDomain.getById(assetApplyDto.getId());
            //删除明细数据
            rfidAssetApplyServiceDomain.remove(
                    Wrappers.<RfidAssetApply>lambdaQuery().eq(RfidAssetApply::getFlowCode, rfidAssetFlow.getCode()));
        } else {
            rfidAssetFlow = new RfidAssetFlow();
            rfidAssetFlow.setCreatedBy(new UserEntry().setId(assetApplyDto.getApproveId() == null ? ApplicationSessions.id() : assetApplyDto.getApproveId()));
            String code = sequenceDomainService.nextDateSequence(DictUtil.SEQ_ASSET_APPLY, 4);
            rfidAssetFlow.setCode(code);
        }
        rfidAssetFlow.setOperateType(assetApplyDto.getOperateType());
        rfidAssetFlow.setExternalUnit(assetApplyDto.getExternalUnit());
        rfidAssetFlow.setExternalDept(assetApplyDto.getExternalDept());
        rfidAssetFlow.setExternalPerson(assetApplyDto.getExternalPerson());
        rfidAssetFlow.setApplyId(assetApplyDto.getApplyId());
        rfidAssetFlow.setApplyAt(assetApplyDto.getApplyAt());
        rfidAssetFlow.setOperateType(assetApplyDto.getOperateType());
        rfidAssetFlow.setStatus(AssetFlowStatus.WAIT_APPROVE);
        rfidAssetFlow.setRemark(assetApplyDto.getRemark());
        if (assetApplyDto.getChangeLocationId() != null) {
            Location location = locationDomainService.getById(assetApplyDto.getChangeLocationId());
            rfidAssetFlow.setChangeLocationId(assetApplyDto.getChangeLocationId());
            rfidAssetFlow.setChangeLocation(location.getFullName());
        }
        if (assetApplyDto.getChangeReaderId() != null) {
            RfidReader rfidReader = rfidReaderServiceDomain.getById(assetApplyDto.getChangeReaderId());
            rfidAssetFlow.setChangeReaderId(assetApplyDto.getChangeReaderId());
            rfidAssetFlow.setChangeReader(rfidReader.getDeviceId());
        }

        UserInfo applyInfo = userInfoDomainService.getById(assetApplyDto.getApplyId());
        rfidAssetFlow.setDepartmentId(applyInfo.getDepartmentId());
        Department department = departmentDomainService.getById(applyInfo.getDepartmentId());
        if (department != null) {
            rfidAssetFlow.setDepartmentName(department.getName().getValue());
        }
        rfidAssetFlow.setApplyId(applyInfo.getId());
        rfidAssetFlow.setApplyName(applyInfo.getName());
        //原信息
        if(assetApplyDto.getOriApplyId()!=null){
            UserInfo oriApplyInfo = userInfoDomainService.getById(assetApplyDto.getOriApplyId());
            rfidAssetFlow.setOriDeptId(oriApplyInfo.getDepartmentId());
            rfidAssetFlow.setOriApplyName(oriApplyInfo.getName());
            Department oriDepartment = departmentDomainService.getById(oriApplyInfo.getDepartmentId());
            if (oriDepartment != null) {
                rfidAssetFlow.setOriDeptName(oriDepartment.getName().getValue());
            }
        }

        List<RfidAsset> rfidAssets = rfidAssetServiceDomain.lambdaQuery().in(RfidAsset::getId, assetApplyDto.getIds()).list();
        Long amount = rfidAssets.stream().filter(v -> v.getPrice() != null).mapToLong(RfidAsset::getPrice).sum();
        rfidAssetFlow.setAmount(amount);
        rfidAssetFlow.setNumber(assetApplyDto.getIds().size());

        UserInfo userInfo = userInfoDomainService.getById(rfidAssetFlow.getCreatedBy().getId());
        rfidAssetFlow.setCreateDept(userInfo.getDepartmentId());
        List<RfidAssetApply> rfidAssetApplies = Lists.newArrayList();
        for (RfidAsset rfidAsset : rfidAssets) {
            //领用资产
            if (assetApplyDto.getOperateType().equals(AssetBusinessType.APPLY) &&
                    (!rfidAsset.getUseState().getValue().equals(DictUtil.UNUSE) && !rfidAsset.getUseState().getValue().equals(DictUtil.STANDBY))) {
                throw new MaginaException("资产" + rfidAsset.getName() + "状态"+rfidAsset.getUseState().getLabel() + "，无法申请！");
            }

            RfidAssetApply rfidAssetApply = new RfidAssetApply();
            rfidAssetApply.setAssetId(rfidAsset.getId());
            rfidAssetApply.setRfidCode(rfidAsset.getRfidCode());
            rfidAssetApply.setFlowCode(rfidAssetFlow.getCode());
            rfidAssetApply.setCreatedAt(rfidAssetFlow.getCreatedAt());
            rfidAssetApply.setCreatedBy(rfidAssetFlow.getCreatedBy());
            rfidAssetApply.setUseState(rfidAsset.getUseState());
            rfidAssetApplies.add(rfidAssetApply);
        }
        rfidAssetApplyServiceDomain.saveBatch(rfidAssetApplies);

        //更新资产状态
        rfidAssetServiceDomain.updateBatchById(rfidAssets);
        this.rfidAssetFlowServiceDomain.saveOrUpdate(rfidAssetFlow);
        Long flowId = rfidAssetFlow.getId();
        RfidAssetRecord rfidAssetRecord = new RfidAssetRecord();
        rfidAssetRecord.setStatus(rfidAssetFlow.getStatus());
        rfidAssetRecord.setFlowId(flowId);
        rfidAssetRecord.setContent("发起"+assetApplyDto.getOperateType().getName()+"申请");
        rfidAssetRecord.setCreatedBy(rfidAssetFlow.getCreatedBy());
        rfidAssetRecordServiceDomain.save(rfidAssetRecord);
        ExecutorUtils.doAfterCommit(() -> {
            if (assetApplyDto.getAutoAudit()) {
                AssetFlowAuditDto auditDto = new AssetFlowAuditDto();
                auditDto.setId(flowId);
                auditDto.setStatus(AssetFlowStatus.PASS);
                auditDto.setApproveId(assetApplyDto.getApproveId());
                auditDto.setApproveName(assetApplyDto.getApproveName());
                this.approve(auditDto);
            }
        });
        return Boolean.TRUE;
    }

    /**
     * 关闭
     *
     * @param id
     * @return
     */
    public boolean close(Long id) {
        RfidAssetFlow rfidAssetFlow = this.rfidAssetFlowServiceDomain.getById(id);
        if (rfidAssetFlow == null) {
            throw new MaginaException("参数错误，数据不存在！");
        }
        if (!rfidAssetFlow.getStatus().equals(AssetFlowStatus.REJECT)) {
            throw new MaginaException("当前状态不允许关闭！");
        }
        rfidAssetFlow.setUpdatedAt(LocalDateTime.now());
        rfidAssetFlow.setUpdatedBy(ApplicationSessions.id());
        rfidAssetFlowServiceDomain.updateById(rfidAssetFlow);
        //更新领用状态已完成
        return rfidAssetApplyServiceDomain.lambdaUpdate()
                .set(RfidAssetApply::getUpdatedAt, LocalDateTime.now())
                .set(RfidAssetApply::getUpdatedBy, ApplicationSessions.id())
                .eq(RfidAssetApply::getFlowCode, rfidAssetFlow.getCode())
                .update();
    }

    /**
     * 资产流水审批
     *
     * @param assetFlowAuditDto
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean approve(AssetFlowAuditDto assetFlowAuditDto) {
        if (assetFlowAuditDto.getId() == null
                || assetFlowAuditDto.getStatus() == null) {
            throw new MaginaException("参数错误，请联系管理员！");
        }
        RfidAssetFlow rfidAssetFlow = this.rfidAssetFlowServiceDomain.getById(assetFlowAuditDto.getId());
        if (rfidAssetFlow == null) {
            throw new MaginaException("参数错误，数据不存在！");
        }
        rfidAssetFlow.setStatus(assetFlowAuditDto.getStatus());
        rfidAssetFlow.setApproveAt(LocalDateTime.now());
        rfidAssetFlow.setApproveId(assetFlowAuditDto.getApproveId() == null ? ApplicationSessions.id() : assetFlowAuditDto.getApproveId());
        rfidAssetFlow.setApproveName(assetFlowAuditDto.getApproveId() == null ? ApplicationSessions.name() : assetFlowAuditDto.getApproveName());
        if(assetFlowAuditDto.getChangeReaderId()!=null){
            RfidReader rfidReader = rfidReaderServiceDomain.getById(assetFlowAuditDto.getChangeReaderId());
            rfidAssetFlow.setChangeReaderId(assetFlowAuditDto.getChangeReaderId());
            rfidAssetFlow.setChangeReader(rfidReader.getDeviceId());
        }
        if(assetFlowAuditDto.getChangeLocationId()!=null){
            Location location = locationDomainService.getById(assetFlowAuditDto.getChangeLocationId());
            rfidAssetFlow.setChangeLocationId(assetFlowAuditDto.getChangeLocationId());
            rfidAssetFlow.setChangeLocation(location.getFullName());
        }

        //通过
        List<Long> readerIds = Lists.newArrayList();
        if (assetFlowAuditDto.getStatus().equals(AssetFlowStatus.PASS)) {
            //资产领用
            List<RfidAssetApply> rfidAssetApplyList = rfidAssetApplyServiceDomain.lambdaQuery()
                    .eq(RfidAssetApply::getFlowCode, rfidAssetFlow.getCode()).list();
            List<Long> assetIds = rfidAssetApplyList.stream().map(RfidAssetApply::getAssetId).collect(Collectors.toList());
            List<RfidAsset> rfidAssetList = rfidAssetServiceDomain.selectJoinList(RfidAsset.class, MPJWrappers.lambdaJoin()
                    .selectAll(RfidAsset.class)
                    .selectAs(RfidReader::getCode, RfidAsset::getReaderCode)
                    .leftJoin(RfidReader.class, RfidReader::getId, RfidAsset::getReaderId)
                    .in(RfidAsset::getId, assetIds)
            );

            if (rfidAssetFlow.getOperateType().equals(AssetBusinessType.PURCHASE)
                    || rfidAssetFlow.getOperateType().equals(AssetBusinessType.DONATE_IN)
                    || rfidAssetFlow.getOperateType().equals(AssetBusinessType.ALLOT_IN) ||
                    rfidAssetFlow.getOperateType().equals(AssetBusinessType.OVERFLOW)) {
                //更新资产状态 入库
                rfidAssetList.forEach(v -> {
                    v.setInStatus(true);
                    v.setUseState(new DictItemEntry().setValue(DictUtil.STANDBY));
                    v.setSignCode(rfidAssetFlow.getCode());
                    v.setEnterDate(DateUtil.formatDate(new Date()));
                    v.setUpdatedBy(assetFlowAuditDto.getApproveId() == null ? ApplicationSessions.id() : assetFlowAuditDto.getApproveId());
                    if (rfidAssetFlow.getChangeReaderId() != null) {
                        v.setReaderId(rfidAssetFlow.getChangeReaderId());
                        readerIds.add(rfidAssetFlow.getChangeReaderId());
                    }
                });
                rfidAssetServiceDomain.updateBatchById(rfidAssetList);

            } else if (rfidAssetFlow.getOperateType().equals(AssetBusinessType.APPLY)) {
                //资产领用
                rfidAssetList.forEach(v -> {
                    v.setUpdatedBy(assetFlowAuditDto.getApproveId() == null ? ApplicationSessions.id() : assetFlowAuditDto.getApproveId());
                    v.setApplyId(rfidAssetFlow.getApplyId());
                    v.setApplyName(rfidAssetFlow.getApplyName());
                    v.setDepartmentId(rfidAssetFlow.getDepartmentId());
                    v.setUseState(new DictItemEntry().setValue(DictUtil.USE));
                    if (rfidAssetFlow.getChangeReaderId() != null) {
                        v.setReaderId(rfidAssetFlow.getChangeReaderId());
                        v.setLocationId(rfidAssetFlow.getChangeLocationId());
                        readerIds.add(rfidAssetFlow.getChangeReaderId());
                    }
                });
                rfidAssetServiceDomain.updateBatchById(rfidAssetList);

            }else if (rfidAssetFlow.getOperateType().equals(AssetBusinessType.RETURN)) {
                //更新资产状态 领用退还
                rfidAssetList.forEach(v -> {
                    v.setUpdatedBy(assetFlowAuditDto.getApproveId() == null ? ApplicationSessions.id() : assetFlowAuditDto.getApproveId());
                    v.setApplyId(null);
                    v.setInStatus(true);
                    v.setUseState(new DictItemEntry().setValue(DictUtil.STANDBY));
                    v.setApplyName(null);
                    v.setDepartmentId(null);
                    if (v.getReaderId() != null) {
                        readerIds.add(v.getReaderId());
                    }
                    if (assetFlowAuditDto.getChangeReaderId() != null) {
                        v.setLocationId(assetFlowAuditDto.getChangeLocationId());
                        v.setReaderId(assetFlowAuditDto.getChangeReaderId());
                        readerIds.add(assetFlowAuditDto.getChangeReaderId());
                    }
                });
                rfidAssetServiceDomain.updateBatchById(rfidAssetList);
            } else if (rfidAssetFlow.getOperateType().equals(AssetBusinessType.ALLOT_OUT)) {
                //调拨出库
                rfidAssetList.forEach(v -> {
                    v.setUpdatedBy(assetFlowAuditDto.getApproveId() == null ? ApplicationSessions.id() : assetFlowAuditDto.getApproveId());
//                    v.setApplyId(rfidAssetFlow.getCreatedBy().getId());
//                    v.setApplyName(rfidAssetFlow.getCreatedBy().getName());
//                    v.setReaderId(rfidAssetFlow.getChangeReaderId());
                    v.setUseState(new DictItemEntry().setValue(DictUtil.BORROW));
//                    v.setLocationId(rfidAssetFlow.getChangeLocationId());
//                    v.setDepartmentId(rfidAssetFlow.getDepartmentId());
//                    if (rfidAssetFlow.getChangeReaderId() != null) {
//                        readerIds.add(rfidAssetFlow.getChangeReaderId());
//                    }
                });
                rfidAssetServiceDomain.updateBatchById(rfidAssetList);
            } else if (rfidAssetFlow.getOperateType().equals(AssetBusinessType.REPAIR)) {
                //维修登记
                rfidAssetList.forEach(v -> {
                    v.setUpdatedBy(assetFlowAuditDto.getApproveId() == null ? ApplicationSessions.id() : assetFlowAuditDto.getApproveId());
                    v.setUseState(new DictItemEntry().setValue(DictUtil.REPAIR));
                });
                rfidAssetServiceDomain.updateBatchById(rfidAssetList);
            } else if (rfidAssetFlow.getOperateType().equals(AssetBusinessType.BREAKAGE)) {
                //资产报损
                rfidAssetList.forEach(v -> {
                    v.setUpdatedBy(assetFlowAuditDto.getApproveId() == null ? ApplicationSessions.id() : assetFlowAuditDto.getApproveId());
                    v.setApplyId(null);
                    v.setApplyName(null);
                    v.setReaderId(null);
                    v.setUseState(new DictItemEntry().setValue(DictUtil.DAMAGE));
                    v.setLocationId(null);
                    v.setDepartmentId(rfidAssetFlow.getDepartmentId());
                    if (v.getReaderId() != null) {
                        readerIds.add(v.getReaderId());
                    }
                });
                rfidAssetServiceDomain.updateBatchById(rfidAssetList);

            } else if (rfidAssetFlow.getOperateType().equals(AssetBusinessType.DONATE_OUT)) {
                //更新资产状态 报废出库
                rfidAssetList.forEach(v -> {
                    v.setUpdatedBy(assetFlowAuditDto.getApproveId() == null ? ApplicationSessions.id() : assetFlowAuditDto.getApproveId());
                    v.setApplyId(null);
                    v.setApplyName(null);
                    v.setReaderId(null);
                    v.setUseState(new DictItemEntry().setValue(DictUtil.BORROW));
                    v.setLocationId(null);
                    v.setDepartmentId(rfidAssetFlow.getDepartmentId());
                    if (v.getReaderId() != null) {
                        readerIds.add(v.getReaderId());
                    }
                });
                rfidAssetServiceDomain.updateBatchById(rfidAssetList);

            } else if (rfidAssetFlow.getOperateType().equals(AssetBusinessType.BREAKDOWN)) {
                //更新资产状态 捐出
                rfidAssetList.forEach(v -> {
                    v.setUpdatedBy(assetFlowAuditDto.getApproveId() == null ? ApplicationSessions.id() : assetFlowAuditDto.getApproveId());
                    v.setApplyId(null);
                    v.setApplyName(null);
                    v.setReaderId(null);
                    v.setUseState(new DictItemEntry().setValue(DictUtil.BREAKAGE));
                    v.setLocationId(null);
                    v.setDepartmentId(rfidAssetFlow.getDepartmentId());
                    if (v.getReaderId() != null) {
                        readerIds.add(v.getReaderId());
                    }
                });
                rfidAssetServiceDomain.updateBatchById(rfidAssetList);

            } else if (rfidAssetFlow.getOperateType().equals(AssetBusinessType.DISPOSE)) {
                //更新资产状态 处置
                rfidAssetList.forEach(v -> {
                    v.setUpdatedBy(assetFlowAuditDto.getApproveId() == null ? ApplicationSessions.id() : assetFlowAuditDto.getApproveId());
                    v.setApplyId(null);
                    v.setApplyName(null);
                    v.setReaderId(null);
                    v.setUseState(new DictItemEntry().setValue(DictUtil.BORROW));
                    v.setLocationId(null);
                    v.setDepartmentId(rfidAssetFlow.getDepartmentId());
                    if (v.getReaderId() != null) {
                        readerIds.add(v.getReaderId());
                    }
                });
                rfidAssetServiceDomain.updateBatchById(rfidAssetList);
            } else if (rfidAssetFlow.getOperateType().equals(AssetBusinessType.HANDOVER)) {
                if (rfidAssetFlow.getReaderId() != null) {
                    readerIds.add(rfidAssetFlow.getReaderId());
                }
                //更新资产状态  交接
                rfidAssetList.forEach(v -> {
                    v.setUpdatedBy(assetFlowAuditDto.getApproveId() == null ? ApplicationSessions.id() : assetFlowAuditDto.getApproveId());
                    v.setApplyId(rfidAssetFlow.getApplyId());
                    v.setApplyName(rfidAssetFlow.getApplyName());
                    v.setUseState(new DictItemEntry().setValue(DictUtil.USE));
                    v.setLocationId(rfidAssetFlow.getLocationId());
                    v.setDepartmentId(rfidAssetFlow.getDepartmentId());
                    if (v.getReaderId() != null) {
                        readerIds.add(v.getReaderId());
                    }
                    v.setReaderId(rfidAssetFlow.getReaderId());
                });
                rfidAssetServiceDomain.updateBatchById(rfidAssetList);
            }

        } else {
            List<RfidAssetApply> rfidAssetApplyList = rfidAssetApplyServiceDomain.lambdaQuery()
                    .eq(RfidAssetApply::getFlowCode, rfidAssetFlow.getCode()).list();
            List<Long> assetIds = rfidAssetApplyList.stream().map(RfidAssetApply::getAssetId).collect(Collectors.toList());
            rfidAssetApplyList.forEach(v -> {
                rfidAssetServiceDomain.lambdaUpdate()
                        .set(RfidAsset::getUseState, v.getUseState())
                        .set(rfidAssetFlow.getOperateType().equals(AssetBusinessType.PURCHASE)
                                || rfidAssetFlow.getOperateType().equals(AssetBusinessType.DONATE_IN),RfidAsset::getStatus, AssetApplyStatus.REJECT)
                        .set(RfidAsset::getUpdatedBy, ApplicationSessions.id())
                        .eq(RfidAsset::getId, v.getAssetId())
                        .update();
            });
        }
        RfidAssetRecord rfidAssetRecord = new RfidAssetRecord();
        rfidAssetRecord.setStatus(assetFlowAuditDto.getStatus());
        rfidAssetRecord.setFlowId(rfidAssetFlow.getId());
        rfidAssetRecord.setContent(assetFlowAuditDto.getStatus().getName()+rfidAssetFlow.getOperateType().getName()+"申请");
        this.rfidAssetFlowServiceDomain.updateById(rfidAssetFlow);
        rfidAssetRecord.setCreatedBy(rfidAssetFlow.getCreatedBy());
        rfidAssetRecordServiceDomain.save(rfidAssetRecord);

        ExecutorUtils.doAfterCommit(() -> {
            if (CollectionUtils.isNotEmpty(readerIds)) {
                rfidReaderService.sendAssetBatch(readerIds);
            }
        });

        return Boolean.TRUE;
    }

    /**
     * 资产日志
     *
     * @param rfidAssets
     * @param rfidAssetFlow
     * @return
     */
    public void buildAssetLog(List<RfidAsset> rfidAssets, RfidAssetFlow rfidAssetFlow, List<AssetChangeLogDto> assetChangeLogDtoList) {
        rfidAssets.forEach(rfidAssetOld -> {
            AssetChangeLogDto assetChangeLogDto = AssetChangeLogDto.builder()
                    .assetId(rfidAssetOld.getId())
                    .changeCode(rfidAssetFlow.getCode())
                    .createdBy(ApplicationSessions.id())
                    .source(AssetChangeSource.FLOW)
                    .operatType(AssetOperatType.UPDATE)
                    .build();
            String before = "";     //变更前
            String after = "";     //变更后

            if (StringUtils.isEmpty(rfidAssetFlow.getApplyName())
                    || StringUtils.isEmpty(rfidAssetOld.getApplyName()) || !rfidAssetFlow.getApplyName().equals(rfidAssetOld.getApplyName())) {
                if (StringUtils.isNotBlank(rfidAssetOld.getApplyName())) {
                    before += " 保管人：" + rfidAssetOld.getApplyName();
                }
                if (StringUtils.isNotBlank(rfidAssetFlow.getApplyName())) {
                    after += " 保管人：" + rfidAssetFlow.getApplyName();
                }
            }

            if (rfidAssetFlow.getDepartmentId() != null
                    || rfidAssetOld.getDepartmentId() == null || !rfidAssetFlow.getDepartmentId().equals(rfidAssetOld.getDepartmentId())) {
                if (rfidAssetOld.getDepartmentId() != null) {
                    Department department = departmentDomainService.getById(rfidAssetOld.getManagerDeptId());
                    before += " 所属部门：" + department.getName();
                }
                if (StringUtils.isNotBlank(rfidAssetFlow.getDepartmentName())) {
                    after += " 所属部门：" + rfidAssetFlow.getDepartmentName();
                }
            }

            if (rfidAssetFlow.getReaderId() != null
                    || rfidAssetOld.getReaderId() == null || !rfidAssetFlow.getReaderId().equals(rfidAssetOld.getReaderId())) {
                if (Objects.nonNull(rfidAssetOld.getReaderId())) {
                    before += " 基站设备：" + rfidAssetOld.getReaderCode();
                }
                if (Objects.nonNull(rfidAssetFlow.getReaderId())) {
                    RfidReader rfidReader = rfidReaderServiceDomain.getById(rfidAssetFlow.getReaderId());
                    after += " 基站设备：" + rfidReader.getCode();
                }
            }
            if (StringUtils.isEmpty(rfidAssetFlow.getLocation())
                    || StringUtils.isEmpty(rfidAssetOld.getLocation()) || !rfidAssetFlow.getLocation().equals(rfidAssetOld.getLocation())) {
                if (StringUtils.isNotBlank(rfidAssetOld.getLocation())) {
                    before += " 位置：" + rfidAssetOld.getLocation();
                }
                if (Objects.nonNull(rfidAssetFlow.getLocationId())) {
                    after += " 位置：" + rfidAssetFlow.getLocation();
                }
            }
            assetChangeLogDtoList.add(assetChangeLogDto);
        });
    }

    /**
     * 导出资产流程
     *
     * @param response
     * @return
     */
    public void downloadData(HttpServletResponse response, RfidAssetFlowQuery pageQuery) throws IOException {
        //查询数据
        List<RfidAssetFlow> excelList = this.searchList(pageQuery);
        //页面下载设置
        DownloadResponseUtil.addDownLoadHeader(response, "资产登记记录.xlsx");
        //将并添加信息，实体类结构，类的数据做成excel表格对象workbook
        Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams(), RfidAsset.class, excelList);
        workbook.write(response.getOutputStream());
    }


}
