package com.hightop.benyin.share.infrastructure.util;

import java.util.HashMap;
import java.util.Map;

public class ThreadLocalUtil {

    /** 线程容器 **/
    private static ThreadLocal<Map<String, Object>> threadContext = new ThreadLocal<>();

    private ThreadLocalUtil() {}

    /**
     * 从ThreadLocal里获取值
     *
     * @param key 要获取的数据的KEY
     * @return 要获取的值
     */
    public static Object get(String key) {
        Map<String, Object> map = threadContext.get();
        Object value = null;
        if(!isThreadContextIsNull() && map.containsKey(key)) {
            value = map.get(key);
        }
        return value;
    }

    /**
     * 向ThreadLocal存放值
     *
     * @param key 要缓存的KEY
     * @param value 要缓存的VALUE
     */
    public static void put(String key, Object value) {
        if(!isThreadContextIsNull()){
            getContextMap().put(key, value);
        }
        else{
            Map<String, Object> map = new HashMap<>();
            map.put(key, value);
            threadContext.set(map);
        }
    }

    /**
     * 根据KEY移除缓存里的数据
     *
     * @param key
     * @return
     */
    public static void remove(String key) {
        if(!isThreadContextIsNull()) {
            getContextMap().remove(key);
        }
    }

    /**
     * 清理线程
     * 用于释放当前线程ThreadLocal资源
     */
    public static void clear() {
        threadContext.remove();
    }

    /**
     * 取得实例
     */
    private static Map<String, Object> getContextMap() {
        return (Map<String, Object>) threadContext.get();
    }

    /**
     * 判断线程容器中的Map是否为null
     */
    private static boolean isThreadContextIsNull(){
        return threadContext.get() == null;
    }

}

