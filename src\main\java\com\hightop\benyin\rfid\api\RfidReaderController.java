package com.hightop.benyin.rfid.api;

import com.alibaba.fastjson.JSONObject;
import com.hightop.benyin.rfid.application.service.RfidReaderService;
import com.hightop.benyin.rfid.application.vo.dto.ReaderSeScanStatusDto;
import com.hightop.benyin.rfid.application.vo.dto.ReaderSetHeartBeatDto;
import com.hightop.benyin.rfid.application.vo.dto.ReaderSettingDto;
import com.hightop.benyin.rfid.application.vo.po.AssetReaderTotalVo;
import com.hightop.benyin.rfid.application.vo.query.RfidReaderQuery;
import com.hightop.benyin.rfid.infrastructure.entity.RfidReader;
import com.hightop.fario.base.web.Operation;
import com.hightop.fario.base.web.RestResponse;
import com.hightop.fario.common.mybatis.DataGrid;
import com.hightop.magina.standard.behavior.operation.IgnoreOperationLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * RFID基站管理rest接口
 *
 * <AUTHOR>
 * @date 2024-10-12 16:36:01
 */
@RequestMapping("/reader")
@RestController
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Api(tags = "RFID基站管理")
public class RfidReaderController {
    RfidReaderService rfidReaderService;


    @PostMapping("/page")
    @ApiOperation("分页查询")
    @IgnoreOperationLog
    public RestResponse<DataGrid<RfidReader>> page(@RequestBody RfidReaderQuery pageQuery) {
        return RestResponse.ok(this.rfidReaderService.page(pageQuery));
    }

    @PostMapping("/total")
    @ApiOperation("统计列表汇总查询")
    @IgnoreOperationLog
    public RestResponse<AssetReaderTotalVo> total(@RequestBody RfidReaderQuery pageQuery) {
        return RestResponse.ok(this.rfidReaderService.getTotal(pageQuery));
    }


    @GetMapping("/{locationId}")
    @ApiOperation("根据位置id查询基站列表")
    @IgnoreOperationLog
    public RestResponse<List<RfidReader>> getByLocationId(@PathVariable("locationId") Long locationId) {
        return RestResponse.ok(this.rfidReaderService.getByLocationId(locationId));
    }

    @PostMapping("/auth")
    @ApiOperation("带权限分页查询")
    @IgnoreOperationLog

    public RestResponse<DataGrid<RfidReader>> authPage(@RequestBody RfidReaderQuery pageQuery) {
        return RestResponse.ok(this.rfidReaderService.authPage(pageQuery));
    }

    @PostMapping
    @ApiOperation("添加")
    public RestResponse<Void> add(@Validated @RequestBody RfidReader rfidReder) {
        return Operation.ADD.response(this.rfidReaderService.save(rfidReder));
    }

    @PutMapping
    @ApiOperation("修改")
    public RestResponse<Void> update(@Validated @RequestBody RfidReader rfidReder) {
        return Operation.UPDATE.response(this.rfidReaderService.updateById(rfidReder));
    }

    @PutMapping("switch/{id}")
    @ApiOperation("设备更换")
    public RestResponse<Void> switchDevice(@PathVariable @ApiParam("id") Long id, @RequestParam @ApiParam("deviceId") String deviceId) {
        return Operation.UPDATE.response(this.rfidReaderService.switchDevice(id, deviceId));
    }

    @PutMapping("/down/{id}")
    @ApiOperation("同步下发资产")
    public RestResponse<Void> down(@PathVariable("id") Long id) {
        return Operation.UPDATE.response(rfidReaderService.sendAsset(id));
    }


    @DeleteMapping("/{id}")
    @ApiOperation("删除")
    public RestResponse<Void> delete(@PathVariable @ApiParam("id") Long id) {
        return Operation.DELETE.response(this.rfidReaderService.removeById(id));
    }

    @PostMapping("/setting")
    @ApiOperation(value = "基站功能设置")
    public RestResponse<Void> setting(@Validated @RequestBody ReaderSettingDto readerSettingDto) throws Exception {
        return Operation.UPDATE.response(this.rfidReaderService.setting(readerSettingDto));
    }

    @PutMapping("/enable/{id}/{enable}")
    @ApiOperation("启停")
    public RestResponse<Void> enable(@ApiParam(value = "位置id", required = true) @PathVariable Long id,
                                     @ApiParam(value = "启停标识", required = true) @PathVariable Boolean enable) {
        return Operation.UPDATE.response(this.rfidReaderService.updateEnable(id, enable));
    }

    @PostMapping("/scan-setting")
    @ApiOperation(value = "基站扫描设置")
    public RestResponse<Void> scanSetting(@Validated @RequestBody ReaderSeScanStatusDto readerSeScanStatusDto) {
        return Operation.UPDATE.response(this.rfidReaderService.scanSetting(readerSeScanStatusDto));
    }

    @PostMapping("/heat-setting")
    @ApiOperation(value = "基站心跳间隔设置")
    public RestResponse<Void> heatSetting(@Validated @RequestBody ReaderSetHeartBeatDto readerSetHeartBeatDto) {
        return Operation.UPDATE.response(this.rfidReaderService.heatSetting(readerSetHeartBeatDto));
    }

    @DeleteMapping("clear")
    @ApiOperation("清理基站数据")
    public RestResponse<Void> delete() {
        return Operation.UPDATE.response(this.rfidReaderService.clearReader());
    }


    @GetMapping("/getInfo/{deviceId}")
    @ApiOperation("查询基站设备信息")
    @IgnoreOperationLog
    public RestResponse<JSONObject> getDeviceInfo(@PathVariable("deviceId") String deviceId) {
        return RestResponse.ok(this.rfidReaderService.getDeviceInfo(deviceId));
    }

    @GetMapping("/result/{deviceId}")
    @ApiOperation("查询操作结果")
    @IgnoreOperationLog
    public RestResponse<Boolean> getResult(@PathVariable("deviceId") String deviceId) {
        return RestResponse.ok(this.rfidReaderService.getResult(deviceId));
    }

    /**
     * 导入基站数据
     *
     * @param file
     * @return
     * @throws Exception
     */
    @PostMapping("/import")
    @ApiOperation(value = "导入基站数据")
    public RestResponse<Void> impor(@RequestParam("file") MultipartFile file) throws Exception {
        boolean b = this.rfidReaderService.importData(file);
        if (!b) {
            return new RestResponse(500, "解析失败", null, null);
        }
        return RestResponse.message("导入成功");
    }

    /**
     * 下载导入基站模板
     *
     * @param response
     * @return
     */
    @ApiOperation("下载导入基站模板")
    @GetMapping("/downTemplate")
    public RestResponse<Void> downTemplate(HttpServletResponse response) {
        Boolean b = rfidReaderService.downTemplate(response);
        if (!b) {
            return new RestResponse(500, "下载失败", null, null);
        }
        return RestResponse.message("下载成功");
    }

    /**
     * 导出基站数据
     *
     * @param response
     * @param pageQuery
     * @return
     */
    @ApiOperation("/导出基站数据")
    @GetMapping("/export")
    public RestResponse<Void> export(HttpServletResponse response, RfidReaderQuery pageQuery) throws IOException {
        Boolean b = rfidReaderService.download(response, pageQuery);
        if (!b) {
            return new RestResponse(500, "导出失败，请联系技术人员！", null, null);
        }
        return RestResponse.message("导出成功");
    }


}
