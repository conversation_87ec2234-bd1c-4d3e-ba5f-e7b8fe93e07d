package com.hightop.benyin.rfid.infrastructure.entity;

import cn.afterturn.easypoi.excel.annotation.ExcelIgnore;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.hightop.benyin.rfid.infrastructure.enums.*;
import com.hightop.fario.common.jackson.annotation.JsonAmount;
import com.hightop.magina.standard.ums.user.bind.UserBind;
import com.hightop.magina.standard.ums.user.bind.UserEntry;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 仓储管理-资产盘点表
 *
 * <AUTHOR>
 * @date 2024/07/29 10:29
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@TableName(value = "b_rfid_asset_take", autoResultMap = true)
@ApiModel
public class RfidAssetTake {

    public static final String SEQ_PREFIX = "PD";

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @ApiModelProperty("id")
    Long id;

    @TableField("code")
    @ApiModelProperty("盘点单编号")
    String code;


    @TableField("take_type")
    @ApiModelProperty("盘点类型")
    TakeTypeEnums takeType;

    @TableField("take_range")
    @ApiModelProperty("盘点范围")
    TakeRangeEnums takeRange;

    @TableField(value = "range_info", typeHandler = JacksonTypeHandler.class)
    @ApiModelProperty("盘点范围信息id")
    List<Long> rangeInfo;

    @TableField(exist = false)
    @ApiModelProperty("盘点范围信息")
    String rangeInfoName;

    @TableField("before_num")
    @ApiModelProperty("资产数量")
    Integer beforeNum;

    @TableField("before_amount")
    @ApiModelProperty("资产金额")
    @JsonAmount
    Long beforeAmount;

    @TableField("after_num")
    @ApiModelProperty("盘点数量")
    Integer afterNum;

    @TableField("after_amount")
    @ApiModelProperty("盘点金额")
    @JsonAmount
    Long afterAmount;

    @TableField("normal_num")
    @ApiModelProperty("正常数量")
    Integer normalNum;

    @TableField("deal_num")
    @ApiModelProperty("处理数量")
    Integer dealNum;

    @TableField("ignore_num")
    @ApiModelProperty("忽略数量")
    Integer ignoreNum;

    @TableField("substract_num")
    @ApiModelProperty("数量差值")
    Integer substractNum;

    @TableField("substract_amount")
    @ApiModelProperty("金额差值")
    @JsonAmount
    Long substractAmount;

    @TableField("reader_num")
    @ApiModelProperty("基站数量")
    Integer readerNum;

    @TableField("report_num")
    @ApiModelProperty("已上报基站数量")
    Integer reportNum;


    @TableField("scan_status")
    @ApiModelProperty("扫描状态")
    TakeScanStatus scanStatus;


    @TableField("take_status")
    @ApiModelProperty("盘点业务状态")
    TakeStatus takeStatus;

    /**
     * 结果状态
     */
    @TableField("status")
    @ApiModelProperty("结果状态")
    DiscrepancyStatus status;


    @TableField("remark")
    @ApiModelProperty("备注")
    String remark;


    @TableField("audit_at")
    @ApiModelProperty("审核时间")
    LocalDateTime auditAt;

    @TableField("audit_by")
    @ApiModelProperty("审核人")
    Long auditorBy;

    @TableField("audit_name")
    @ApiModelProperty("审核人")
    String auditName;


    @TableField(value = "completed_at")
    @ApiModelProperty("完成时间")
    LocalDateTime completedAt;


    @TableField("created_by")
    @ApiModelProperty("创建人")
    @UserBind
    UserEntry createdBy;

    @TableField(value = "create_dept")
    @ApiModelProperty("创建人部门")
    @ExcelIgnore
    Long createDept;

    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @ApiModelProperty("创建时间")
    LocalDateTime createdAt;

    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty("更新时间")
    LocalDateTime updatedAt;

    @TableField(value = "updated_by")
    @ApiModelProperty("更新人")
    @ExcelIgnore
    Long updatedBy;

    @TableField("deleted")
    @ApiModelProperty("是否删除  0未删除  1删除")
    @TableLogic
    Integer deleted;

    @TableField(exist = false)
    @ApiModelProperty("盘点明细")
    List<RfidAssetTakeDetail> storageTakeDetails;


}
