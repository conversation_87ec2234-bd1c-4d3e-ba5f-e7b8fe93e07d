package com.hightop.benyin.rfid.infrastructure.enums;

import com.hightop.magina.core.custom.entry.EnumEntry;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

/**
 * 资产状态
 * <AUTHOR>
 * @date 2024/5/15 13:42
 */
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Getter
public enum TransferStatus implements EnumEntry<String> {
    /**
     * 待审核
     */
    WAIT_APPROVE("待审核"),
    /**
     * 驳回
     */
    REJECT("驳回"),
    /**
     * 完成
     */
    PASS("完成"),
    /**
     * 关闭
     */
    CLOSE("关闭");

    /**
     * 状态名称
     */
    String name;

    @Override
    public String getCode() {
        // 以枚举名称作为标识
        return super.name();
    }

    @Override
    public String getName() {
        return this.name;
    }

}
