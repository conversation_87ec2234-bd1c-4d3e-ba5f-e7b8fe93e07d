package com.hightop.benyin.share.api.controller;

import com.hightop.benyin.share.application.service.RegionService;
import com.hightop.benyin.share.application.vo.RegionTreeVo;
import com.hightop.benyin.share.infrastructure.entity.Region;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 省市区rest接口
 * @Author: X.S
 * @date 2023/10/20 18:15
 */
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@RestController
@Api(tags = "省市区")
@RequestMapping("/region")
public class RegionController {
    RegionService regionService;

    @ApiOperation("省市区树查询")
    @GetMapping("/tree")
    public List<RegionTreeVo> tree() {
        return this.regionService.tree();
    }

    @ApiOperation("省份列表查询")
    @GetMapping("/provinces")
    public List<Region> provinces() {
        return this.regionService.provinces();
    }

    @ApiOperation("根据上级编码查询下级地区列表(当hasChildren为true时才调用)")
    @GetMapping("/{code}/children")
    public List<Region> children(@ApiParam("上级编码") @PathVariable Integer code) {
        return this.regionService.children(code);
    }
}
