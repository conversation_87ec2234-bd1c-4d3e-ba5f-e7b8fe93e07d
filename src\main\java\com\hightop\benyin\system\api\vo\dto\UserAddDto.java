package com.hightop.benyin.system.api.vo.dto;

import com.hightop.benyin.system.infrastructure.entity.UserInfo;
import com.hightop.benyin.system.infrastructure.entity.UserPrivacyInfo;
import com.hightop.magina.casual.key.DecryptUnit;
import com.hightop.magina.standard.ums.user.privacy.UserPrivacy;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.ArrayList;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@FieldDefaults(level = AccessLevel.PRIVATE)
@ApiModel
public class UserAddDto extends UserUpdateDto {

    @ApiModelProperty("密码密文")
    @NotBlank(message = "密码不能为空")
    String password;

    @Override
    public UserInfo toBasic(Long id) {
        return super.toBasic(id).setCode(super.getCode());
    }

    @Override
    protected List<DecryptUnit> decryptUnits(UserPrivacyInfo userPrivacy) {
        List<DecryptUnit> list = new ArrayList<>(super.decryptUnits(userPrivacy));
        list.add(new DecryptUnit(this.password, userPrivacy::setPassword));
        return list;
    }

}
