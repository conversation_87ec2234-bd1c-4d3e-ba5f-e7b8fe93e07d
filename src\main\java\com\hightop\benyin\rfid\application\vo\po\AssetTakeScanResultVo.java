package com.hightop.benyin.rfid.application.vo.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.util.List;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@ApiModel("资产盘点扫描结果DTO")
public class AssetTakeScanResultVo {

    @ApiModelProperty("是否完成扫描")
    Boolean allCompleted=false;

    @ApiModelProperty("已完成扫描基站")
    List<String> completeDevices;

}
