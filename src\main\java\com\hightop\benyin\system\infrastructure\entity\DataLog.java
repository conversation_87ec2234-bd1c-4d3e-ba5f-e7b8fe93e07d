package com.hightop.benyin.system.infrastructure.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.hightop.benyin.configurer.enums.DataType;
import com.hightop.benyin.rfid.infrastructure.enums.AssetChangeSource;
import com.hightop.benyin.system.infrastructure.enums.BusinessType;
import com.hightop.magina.standard.ums.user.bind.UserBind;
import com.hightop.magina.standard.ums.user.bind.UserEntry;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@Accessors(chain = true)
@TableName("st_data_log")
@ApiModel
public class DataLog implements Serializable {


    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @ApiModelProperty("id")
    Long id;

    @TableField(exist = false)
    @ApiModelProperty("编码")
    String code;

    @TableField(exist = false)
    @ApiModelProperty("名称")
    String name;

    @TableField("business_type")
    @ApiModelProperty("业务类型")
    BusinessType businessType;

    @TableField("operation_type")
    @ApiModelProperty("操作类型")
    String operationType;

    @TableField("source")
    @ApiModelProperty("变更来源")
    AssetChangeSource source;

    @TableField("business_id")
    @ApiModelProperty("业务id")
    Long businessId;

    @TableField("business_code")
    @ApiModelProperty("业务编码")
    String businessCode;

    @TableField(value = "ori_data")
    @ApiModelProperty("原数据")
    String oriData;

    @TableField(value = "new_data")
    @ApiModelProperty("新数据")
    String newData;

    @TableField("created_by")
    @ApiModelProperty("创建人")
    Long createdBy;

    @TableField(exist = false)
    @ApiModelProperty("创建人")
    String createdByName;

    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @ApiModelProperty("创建时间")
    LocalDateTime createdAt;

    @TableField(exist = false)
    @ApiModelProperty("数据类型")
    DataType dataType;

    @TableField(exist = false)
    @ApiModelProperty("完整id路径")
    String fullIdPath;

    @TableField(exist = false)
    @ApiModelProperty("位置")
    String location;

    @TableField(exist = false)
    @ApiModelProperty("字典编码")
    String dictCode;

}
