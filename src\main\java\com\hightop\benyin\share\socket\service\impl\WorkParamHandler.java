package com.hightop.benyin.share.socket.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.hightop.benyin.share.infrastructure.util.DictUtil;
import com.hightop.benyin.share.socket.service.CommandHandler;
import com.hightop.benyin.share.socket.util.MsgUtil;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

/**
 * 工作参数回执
 */
@Service
@Slf4j
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class WorkParamHandler implements CommandHandler {

    RedisTemplate<String, String> redisTemplate;
    @Override
    public void handle(String clientName, String deviceId, Integer length, String params) {
        log.info("工作参数回执, device : {}, params: {}", deviceId, params);
        //deviceid	保存标签个数	心跳间隔	 日常扫描间隔	日常扫描时长	集中扫描间隔	集中扫描时长	日常上报扫描方式	透传状态
        // 远程更新状态	正执行的扫描类型	心跳状态	信号阈值	设置自动空闲状态	重复过滤时长	设置的空闲时间	设置的扫描时间
        // 心跳的当前处理状态	日常扫描的当前状态	集中扫描的当前状态	电压	硬件版本号	固件版本号	基站协议版本号	RFID协议版本号	天线个数N	第1个天线功率	。。。	第1N个天线N功率

        String[] paramsArray = new String[params.length() / 2];
        for (int i = 0; i < paramsArray.length; i++) {
            // 提取每两个字符组成的子字符串
            String substring = params.substring(i * 2, i * 2 + 2);
            paramsArray[i] = substring;
        }

        int tagNum = paramsArray.length > 8 ? MsgUtil.stringToInteger(paramsArray[8]) : 0;
        Integer heatInterval = paramsArray.length > 9 ? MsgUtil.stringToInteger(paramsArray[9]) : null;
        Integer scanInterval = paramsArray.length > 10 ? MsgUtil.stringToInteger(paramsArray[10]) : null;
        Integer scanDuration = paramsArray.length > 11 ? MsgUtil.stringToInteger(paramsArray[11]) : null;
        Integer focusInterval = paramsArray.length > 12 ? MsgUtil.stringToInteger(paramsArray[12]) : null;
        Integer focusDuration = paramsArray.length > 13 ? MsgUtil.stringToInteger(paramsArray[13]) : null;
        Integer scanMode = paramsArray.length > 14 ? MsgUtil.stringToInteger(paramsArray[14]) : null;
        Integer directStatus = paramsArray.length > 15 ? MsgUtil.stringToInteger(paramsArray[15]) : null;
        Integer otaStatus = paramsArray.length > 16 ? MsgUtil.stringToInteger(paramsArray[16]) : null;
        Integer scanType = paramsArray.length > 17 ? MsgUtil.stringToInteger(paramsArray[17]) : null;
        Integer heatStatus = paramsArray.length > 18 ? MsgUtil.stringToInteger(paramsArray[18]) : null;
        Integer threshold  = paramsArray.length > 19 ? MsgUtil.stringToInteger(paramsArray[19]) : null;
        Integer autoFree  = paramsArray.length > 20 ? MsgUtil.stringToInteger(paramsArray[20]) : null;
        Integer repeateFilterTime  = paramsArray.length > 21 ? MsgUtil.stringToInteger(paramsArray[21]) : null;
        Integer freeTime  = paramsArray.length > 22 ? MsgUtil.stringToInteger(paramsArray[22]) : null;
        Integer scanTime  = paramsArray.length > 23 ? MsgUtil.stringToInteger(paramsArray[23]) : null;
        Integer heatCurrStatus  = paramsArray.length > 24 ? MsgUtil.stringToInteger(paramsArray[24]) : null;
        Integer scanCurrStatus   = paramsArray.length > 25 ? MsgUtil.stringToInteger(paramsArray[25]) : null;
        Integer scanFocusStatus  = paramsArray.length > 26 ? MsgUtil.stringToInteger(paramsArray[26]) : null;
        Integer voltage  = paramsArray.length > 27 ? MsgUtil.stringToInteger(paramsArray[27]) : null;
        Integer deviceVersion  = paramsArray.length > 28 ? MsgUtil.stringToInteger(paramsArray[28]) : null;
        Integer sofrwareVersion  = paramsArray.length > 29 ? MsgUtil.stringToInteger(paramsArray[29]) : null;
        Integer protocolVersion  = paramsArray.length > 30 ? MsgUtil.stringToInteger(paramsArray[30]) : null;
        Integer rfidProtocolVersion  = paramsArray.length > 31 ? MsgUtil.stringToInteger(paramsArray[31]) : null;
        Integer antennaNum  = paramsArray.length > 32 ? MsgUtil.stringToInteger(paramsArray[32]) : null;
        Integer antenna1Power  = paramsArray.length > 33 ? MsgUtil.stringToInteger(paramsArray[33]) : null;
        Integer antenna2Power  = paramsArray.length > 34 ? MsgUtil.stringToInteger(paramsArray[34]) : null;
        Integer antenna3Power  = paramsArray.length > 35 ? MsgUtil.stringToInteger(paramsArray[35]) : null;
        Integer antenna4Power  = paramsArray.length > 36 ? MsgUtil.stringToInteger(paramsArray[36]) : null;
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("tagNum", tagNum);
        jsonObject.put("heatInterval", heatInterval);
        jsonObject.put("scanInterval", scanInterval);
        jsonObject.put("scanDuration", scanDuration);
        jsonObject.put("focusInterval", focusInterval);
        jsonObject.put("focusDuration", focusDuration);
        jsonObject.put("scanMode", scanMode);
        jsonObject.put("directStatus", directStatus);
        jsonObject.put("otaStatus", otaStatus);
        jsonObject.put("scanType", scanType);
        jsonObject.put("heatStatus", heatStatus);
        jsonObject.put("threshold", threshold);
        jsonObject.put("autoFree", autoFree);
        jsonObject.put("repeateFilterTime", repeateFilterTime);
        jsonObject.put("freeTime", freeTime);
        jsonObject.put("scanTime", scanTime);
        jsonObject.put("heatCurrStatus", heatCurrStatus);
        jsonObject.put("scanCurrStatus", scanCurrStatus);
        jsonObject.put("scanFocusStatus", scanFocusStatus);
        jsonObject.put("voltage", voltage);
        jsonObject.put("deviceVersion", deviceVersion);
        jsonObject.put("sofrwareVersion", sofrwareVersion);
        jsonObject.put("protocolVersion", protocolVersion);
        jsonObject.put("rfidProtocolVersion", rfidProtocolVersion);
        jsonObject.put("antennaNum", antennaNum);
        jsonObject.put("antenna1Power", antenna1Power);
        jsonObject.put("antenna2Power", antenna2Power);
        jsonObject.put("antenna3Power", antenna3Power);
        jsonObject.put("antenna4Power", antenna4Power);
        redisTemplate.opsForValue().set(DictUtil.READER_INFO+ deviceId , jsonObject.toJSONString());
        redisTemplate.expire(DictUtil.READER_INFO+ deviceId, 30, TimeUnit.MINUTES);
    }
}
