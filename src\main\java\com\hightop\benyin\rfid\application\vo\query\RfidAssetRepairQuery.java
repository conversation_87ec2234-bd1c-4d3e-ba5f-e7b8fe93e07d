package com.hightop.benyin.rfid.application.vo.query;

import com.hightop.benyin.rfid.infrastructure.enums.AssetFlowStatus;
import com.hightop.fario.common.mybatis.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldDefaults;

import java.util.List;

/**
 * 资产查询DTO
 * <AUTHOR>
 * @date 2024-10-12 16:36:01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@FieldDefaults(level = AccessLevel.PRIVATE)
@ApiModel("资产查询DTO")
public class RfidAssetRepairQuery extends PageQuery {

    @ApiModelProperty("id")
    Long id;

    @ApiModelProperty("流程编码")
    String code;

    @ApiModelProperty("rfid编码")
    String rfidCode;

    @ApiModelProperty("原rfid编码")
    String oriRfidCode;

    @ApiModelProperty("上级编码")
    String parentCode;

    @ApiModelProperty("基站编码")
    String readerCode;
    @ApiModelProperty("资产编码")
    String assetCode;
    @ApiModelProperty("位置编码")
    String locationCode;

    @ApiModelProperty("资产类型")
    List<String> assetType;

    @ApiModelProperty("资产类型1父类")
    String type;

    @ApiModelProperty("标签绑定")
    Boolean bindTag;

    @ApiModelProperty("基站绑定")
    Boolean bindReader;

    @ApiModelProperty("状态")
    AssetFlowStatus status;

    @ApiModelProperty("使用状态")
    List<String> useState;

    @ApiModelProperty("状态多选")
    List<AssetFlowStatus> statusList;

    @ApiModelProperty("不含状态")
    String neStatus;

    @ApiModelProperty("入库状态")
    Integer inStatus;

    @ApiModelProperty("资产名称")
    String name;

    @ApiModelProperty("所属单位")
    String departmentName;

    @ApiModelProperty("仓库负责人")
    String managerName;

    @ApiModelProperty("领用人姓名")
    String applyName;


    @ApiModelProperty("领用人id")
    String applyId;

    @ApiModelProperty("登记单号")
    String signCode;

    @ApiModelProperty("创建人姓名")
    String createdByName;

    @ApiModelProperty("位置")
    List<String> locations;

    @ApiModelProperty("储藏位置(选填)")
    String storageLocation;

    @ApiModelProperty("登记/领用")
    Integer inOutType;

    @ApiModelProperty("登记起始时间")
    String startDate;

    @ApiModelProperty("登记截止时间")
    String endDate;

    @ApiModelProperty("领用起始时间")
    String startApplyDate;

    @ApiModelProperty("领用截止时间")
    String endApplyDate;
}
