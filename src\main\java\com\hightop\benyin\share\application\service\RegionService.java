package com.hightop.benyin.share.application.service;

import com.github.yulichang.toolkit.MPJWrappers;
import com.hightop.benyin.share.application.vo.RegionTreeVo;
import com.hightop.benyin.share.domain.service.RegionDomainService;
import com.hightop.benyin.share.infrastructure.entity.Region;
import com.hightop.magina.core.custom.entry.TreeEntry;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Comparator;
import java.util.List;

/**
 * 地区应用服务
 * @Author: X.S
 * @date 2023/10/20 17:46
 */
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Service
@Transactional(propagation = Propagation.SUPPORTS, rollbackFor = Throwable.class)
public class RegionService {
    RegionDomainService regionDomainService;

    /**
     * 地区树查询
     * @return {@link List}
     */
    public List<RegionTreeVo> tree() {
        List<RegionTreeVo> regions =
            this.regionDomainService.selectJoinList(
                RegionTreeVo.class,
                MPJWrappers.lambdaJoin().selectAll(Region.class)
            );

        return TreeEntry.generate(regions, Comparator.comparing(RegionTreeVo::getCode), Region.TOP);
    }

    /**
     * 查询省份列表 用于异步查询
     * @return {@link List}
     */
    public List<Region> provinces() {
        return this.children(Region.TOP);
    }

    /**
     * 根据编码查询子地区列表 用于异步查询
     * @param code 地区编码
     * @return {@link List}
     */
    public List<Region> children(Integer code) {
        return
            this.regionDomainService.lambdaQuery()
                .eq(Region::getParentCode, code)
                .orderByAsc(Region::getCode)
                .list();
    }
}
