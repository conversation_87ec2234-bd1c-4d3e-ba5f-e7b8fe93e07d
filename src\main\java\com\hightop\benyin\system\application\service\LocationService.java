package com.hightop.benyin.system.application.service;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.google.common.collect.Lists;
import com.hightop.benyin.share.domain.service.SequenceDomainService;
import com.hightop.benyin.share.infrastructure.util.DictUtil;
import com.hightop.benyin.share.infrastructure.util.DownloadResponseUtil;
import com.hightop.benyin.system.api.vo.LocationVo;
import com.hightop.benyin.system.api.vo.dto.LocationAddDto;
import com.hightop.benyin.system.api.vo.dto.LocationFastAddDto;
import com.hightop.benyin.system.api.vo.excel.LocationExcel;
import com.hightop.benyin.system.api.vo.query.LocationQuery;
import com.hightop.benyin.system.application.handler.LocationExcelVerifyHandler;
import com.hightop.benyin.system.domain.service.DepartmentInfoDomainService;
import com.hightop.benyin.system.domain.service.LocationDomainService;
import com.hightop.benyin.system.domain.service.UserInfoDomainService;
import com.hightop.benyin.system.infrastructure.entity.DepartmentInfo;
import com.hightop.benyin.system.infrastructure.entity.Location;
import com.hightop.benyin.system.infrastructure.entity.UserInfo;
import com.hightop.fario.base.constant.StringConstants;
import com.hightop.fario.base.util.CollectionUtils;
import com.hightop.fario.base.util.StringUtils;
import com.hightop.fario.common.core.util.ExecutorUtils;
import com.hightop.magina.casual.session.ApplicationSessions;
import com.hightop.magina.core.custom.entry.TreeEntry;
import com.hightop.magina.core.exception.MaginaException;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Service
public class LocationService {
    LocationDomainService locationDomainService;
    DepartmentInfoDomainService departmentInfoDomainService;
    UserInfoDomainService userInfoDomainService;
    SequenceDomainService sequenceDomainService;

    /**
     * 获得位置树
     *
     * @return {@link List}
     */
    public List<LocationVo> getLocationTree(LocationQuery pageQuery) {
        List<LocationVo> items = this.locationDomainService.getLocationTreeList(pageQuery);
        return this.buildTree(items);
    }

    /**
     * 获得位置树
     *
     * @return {@link List}
     */
    public List<LocationVo> getLocationAuthTree(LocationQuery pageQuery) {
        if (!ApplicationSessions.code().equals(DictUtil.ADMIN)) {
            //获取当前用户所属公司编码
            pageQuery.setCompanyCode(userInfoDomainService.getCurrCompany().getCode().getValue());
        }

        List<LocationVo> items = this.locationDomainService.getLocationTreeList(pageQuery);
        return buildTree(items, pageQuery.getCompanyCode());
    }

    /**
     * 构建位置树
     *
     * @param items
     * @return
     */
    public List<LocationVo> buildTree(List<LocationVo> items, String rootCode) {
        if (CollectionUtils.isNotEmpty(items)&&StringUtils.isNotBlank(rootCode)) {
            List<LocationVo> locationTreeVos = items.stream().filter(item -> item.getCode().equals(rootCode)).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(locationTreeVos)) {
                for (LocationVo locationTreeVo : items) {
                    while (!locationTreeVo.getCode().equals(rootCode)) {
                        Location parent = this.locationDomainService.getById(locationTreeVo.getParentId());
                        LocationVo parentVo = BeanUtil.copyProperties(parent, LocationVo.class);
                        locationTreeVos.add(parentVo);
                        locationTreeVo = parentVo;
                    }
                }
                locationTreeVos.addAll(items);
                items = locationTreeVos.stream().distinct().collect(Collectors.toList());
            }
        }
        //补充部门全路径信息用于界面反写
        for (LocationVo locationTreeVo : items) {
            //如果有公司则把公司这一层级设置为TOP层
            if (locationTreeVo.getCode().equals(rootCode)) {
                locationTreeVo.setParentId(0L);
            }
            if (CollectionUtils.isNotEmpty(locationTreeVo.getDepartIds())) {
                locationTreeVo.setDepartmentIds(locationTreeVo.getDepartIds());
                List<DepartmentInfo> departmentInfos = departmentInfoDomainService.lambdaQuery().in(DepartmentInfo::getId, locationTreeVo.getDepartIds()).list();
                String departmentNames = departmentInfos.stream().map(v -> v.getName().getValue()).collect(Collectors.joining(","));
                locationTreeVo.setDepartmentNames(departmentNames);
                locationTreeVo.setDepartIds(null);
                locationTreeVo.setDepartmentFullPaths(departmentInfos.stream().map(DepartmentInfo::getFullIdPath).collect(Collectors.toList()));
            }
        }
        return TreeEntry.generate(items, Location.COMPARATOR, Location.TOP);
    }

    /**
     * 构建位置树
     *
     * @param items
     * @return
     */
    public List<LocationVo> buildTree(List<LocationVo> items) {
        if (CollectionUtils.isNotEmpty(items)) {
            List<LocationVo> locationTreeVos = items.stream().filter(item -> item.getParentId().equals(LocationVo.TOP)).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(locationTreeVos)) {
                for (LocationVo locationTreeVo : items) {
                    while (!locationTreeVo.getParentId().equals(LocationVo.TOP)) {
                        Location parent = this.locationDomainService.getById(locationTreeVo.getParentId());
                        LocationVo parentVo = BeanUtil.copyProperties(parent, LocationVo.class);
                        locationTreeVos.add(parentVo);
                        locationTreeVo = parentVo;
                    }
                }
                locationTreeVos.addAll(items);
                items = locationTreeVos.stream().distinct().collect(Collectors.toList());
            }
        }
        //补充部门全路径信息用于界面反写 由于是多选
        for (LocationVo locationTreeVo : items) {
            if (CollectionUtils.isNotEmpty(locationTreeVo.getDepartIds())) {
                locationTreeVo.setDepartmentIds(locationTreeVo.getDepartIds());
                List<DepartmentInfo> departmentInfos = departmentInfoDomainService.lambdaQuery()
                        .in(DepartmentInfo::getId, locationTreeVo.getDepartIds()).list();
                String departmentNames = departmentInfos.stream().map(v -> v.getName().getValue()).collect(Collectors.joining(","));
                locationTreeVo.setDepartmentNames(departmentNames);
                locationTreeVo.setDepartIds(null);
                List<String> fullIdPaths = departmentInfos.stream().map(DepartmentInfo::getFullIdPath).collect(Collectors.toList());
                List<String> paths = new ArrayList<>();
                for (String fullIdPath : fullIdPaths) {
                    long row = fullIdPath.chars().filter(ch -> ch == '/').count();
                    if (row > 1) {
                        String path = fullIdPath.substring(1, fullIdPath.length());
                        int index = path.indexOf("/");
                        path = path.substring(index, path.length());
                        paths.add(path);
                    } else {
                        paths.add(fullIdPath);
                    }
                }
                locationTreeVo.setDepartmentFullPaths(paths);
            }
        }
        return TreeEntry.generate(items, Location.COMPARATOR, Location.TOP);
    }

    /**
     * 批量保存
     *
     * @param locationFastAddDto
     * @return
     */
    public boolean batchSave(LocationFastAddDto locationFastAddDto) {
        if (locationFastAddDto.getType() == 2 || locationFastAddDto.getType() == 3) {
            if (locationFastAddDto.getParentId() == null) {
                throw new MaginaException("请选择上级位置！");
            }
        }
        if (locationFastAddDto.getParentId() == null) {
            DepartmentInfo departmentInfo = userInfoDomainService.getCurrCompany();
            LocationQuery pageQuery = new LocationQuery();
            pageQuery.setDepartmentId(departmentInfo.getId());
            List<LocationVo> items = this.locationDomainService.getLocationTreeList(pageQuery);
            if (CollectionUtils.isEmpty(items)) {
                throw new MaginaException("未找到当前单位的默认位置！");
            }
            locationFastAddDto.setParentId(items.get(0).getId());
        }
        for (LocationAddDto locationAddDto : locationFastAddDto.getLocations()) {
            Location location = locationAddDto.toLocation();
            location.setParentId(locationFastAddDto.getParentId());
            location.setType(locationFastAddDto.getType());
            if (location.getId() != null) {
                this.updateById(location);

            } else {
                this.save(location);
            }
        }
        return true;
    }

    /**
     * 位置保存
     *
     * @param location {@link Location}
     * @return true/false
     */
    public boolean save(Location location) {
        if (Objects.isNull(location.getParentId())) {
            location.setParentId(Location.TOP);
            location.setIsLeaf(false);
        }

        if (location.getParentId() != null && !location.getParentId().equals(Location.TOP)) {
            Long count = locationDomainService.lambdaQuery()
                    .eq(Location::getParentId, location.getParentId())
                    .eq(Location::getFullName, location.getName())
                    .eq(Location::getIsAvailable, true).count();
            if (count > 0) {
                throw new MaginaException("同层级已存在相同位置名称");
            }
            //设置上级为非叶子节点
            Location parentLocation = locationDomainService.getById(location.getParentId());
            parentLocation.setIsLeaf(false);
            locationDomainService.updateById(parentLocation);
            location.setType(parentLocation.getType() + 1);
            location.setIsLeaf(true);
        }

        this.checkLocation(location);
        location.setId(IdWorker.getId());
        this.setFullIdPath(location);
        this.setFullName(location);
        if (StringUtils.isBlank(location.getCode())) {
            location.setCode(this.getChildCode(location.getParentId(), location.getId(), location.getType()));
        }
        return this.locationDomainService.save(location);
    }

    private String getChildCode(Long parentId, Long id, Integer level) {
        if (parentId.equals(Location.TOP)) {
            throw new MaginaException("请直接添加部门顶层目录，数据将会同步到位置数据！");
        }
        //如果上级是顶层  前缀加F
        Location parent = this.locationDomainService.getById(parentId);
        String prefx = "";
        if (parent.getParentId().equals(Location.TOP)) {
            prefx = "F";
        }
        Integer length = 3;

        if (level > 3) {
            length = 3;
        }
        String code = sequenceDomainService.nextSequence(parent.getCode() + prefx, length);
        Long codeCount = this.locationDomainService.lambdaQuery()
                .eq(Location::getCode, code)
                .eq(Location::getIsAvailable, true)
                .ne(Objects.nonNull(id), Location::getId, id)
                .count();
        while (codeCount > 0L) {
            code = sequenceDomainService.nextSequence(parent.getCode() + prefx, length);
            codeCount = this.locationDomainService.lambdaQuery()
                    .eq(Location::getCode, code)
                    .eq(Location::getIsAvailable, true)
                    .ne(Objects.nonNull(id), Location::getId, id)
                    .count();
        }
        return code;
    }

    /**
     * 位置更新
     *
     * @param location {@link Location}
     * @return true/false
     */
    public boolean updateById(Location location) {
        this.checkLocation(location);
        // 原始记录
        Location origin = this.locationDomainService.getById(location.getId());
        location.setName(origin.getName());
        location.setType(origin.getType());
        // 仅当修改了父位置id时修改位置全路径
        if (!Objects.equals(location.getParentId(), origin.getParentId())) {
            // 按照父位置id设置id全路径
            this.setFullIdPath(location);

            location.setUpdatedBy(ApplicationSessions.id());

            // 更新子位置id全路径
            List<Location> children = new ArrayList<>();
            BiConsumer<List<Location>, String> recursion = new BiConsumer<List<Location>, String>() {
                @Override
                public void accept(List<Location> list, String prefix) {
                    if (CollectionUtils.isEmpty(list)) {
                        return;
                    }

                    list.forEach(it -> {
                        it.setFullIdPath(prefix + section(it.getId()));
                        it.setUpdatedBy(ApplicationSessions.id());
                        // 待更新数据
                        children.add(it);

                        // 递归子节点
                        Optional.ofNullable(
                                        locationDomainService.lambdaQuery()
                                                .eq(Location::getParentId, it.getId())
                                                .eq(Location::getIsAvailable, true)
                                                .list()
                                )
                                .filter(CollectionUtils::isNotEmpty)
                                .ifPresent(v -> this.accept(v, it.getFullIdPath()));
                    });
                }
            };

            recursion.accept(Collections.singletonList(location), location.getFullIdPath());

            return this.locationDomainService.updateBatchById(children);
        }
        if (location.getFullIdPath() == null) {
            location.setFullIdPath(origin.getFullIdPath());
        }
        this.setFullName(location);
        location.setUpdatedBy(ApplicationSessions.id());
        return this.locationDomainService.updateById(location);
    }

    public Location getById(Long id) {
        Location location = this.locationDomainService.getById(id);
        location.setLocation(locationDomainService.getLocalFullPath(location.getFullIdPath()));
        return location;
    }

    /**
     * 导出位置信息
     *
     * @param pageQuery
     * @return
     */
    public Workbook downloadData(LocationQuery pageQuery) {
        //查询数据
        List<LocationVo> excelList = this.locationDomainService.getLocationTreeList(pageQuery);
        excelList.forEach(locationTreeVo -> {
            if (CollectionUtils.isNotEmpty(locationTreeVo.getDepartIds())) {
                List<DepartmentInfo> departmentInfos = departmentInfoDomainService.lambdaQuery().in(DepartmentInfo::getId, locationTreeVo.getDepartIds()).list();
                String departmentNames = departmentInfos.stream().map(v -> v.getName().getValue()).collect(Collectors.joining(","));
                String departmentCodes = departmentInfos.stream().map(v -> v.getCode().getValue()).collect(Collectors.joining(","));
                locationTreeVo.setDepartmentNames(departmentNames);
                locationTreeVo.setDepartmentCodes(departmentCodes);
            }
            // 添加处理上级单位编码（修改过）
            if (locationTreeVo.getParentId() != null && !locationTreeVo.getParentId().equals(Location.TOP)) {
                Location parentLocation = locationDomainService.getById(locationTreeVo.getParentId());
                if (parentLocation != null) {
                    locationTreeVo.setParentName(parentLocation.getName());
                    locationTreeVo.setParentCode(parentLocation.getCode());
                }
            }
        });
        //将并添加信息，实体类结构，类的数据做成excel表格对象workbook
        return ExcelExportUtil.exportExcel(new ExportParams(), LocationVo.class, excelList);
    }


    /**
     * 位置删除 逻辑删除
     *
     * @param id 位置id
     * @return 是否更新成功
     */
    public boolean removeById(Long id) {
        // 检查是否有下级
        long count =
                this.locationDomainService.lambdaQuery()
                        .eq(Location::getParentId, id)
                        .eq(Location::getIsAvailable, true)
                        .count();
        if (count > 0) {
            throw new MaginaException("存在子位置不允许删除");
        }
        Location location = this.locationDomainService.getById(id);
        location.setIsAvailable(false);
        // 更新位置为不可用
        return locationDomainService.updateById(location);
    }

    /**
     * 位置启停
     *
     * @param id   位置id
     * @param flag 启停标识
     * @return 是否更新成功
     */
    public boolean updateEnable(Long id, Boolean flag) {
        Location location = this.locationDomainService.getById(id);
        location.setIsEnable(flag);
        if (!flag) {
            location.setExpireAt(LocalDateTime.now());
        } else {
            location.setExpireAt(null);
        }
        location.setUpdatedBy(ApplicationSessions.id());
        return locationDomainService.updateById(location);
    }

    public List<LocationVo> getLocationList(LocationQuery pageQuery) {
//        if (CollectionUtils.isEmpty(pageQuery.getDepartmentIds())) {
//            //获取当前用户所属公司的部门列表
//            pageQuery.setDepartmentIds(userInfoDomainService.getCurrCompanyDeptList());
//        }
        String role = userInfoDomainService.getCurrRoleCode();
        //负责人查看自己负责的部门位置
        if (!ApplicationSessions.code().equals(DictUtil.ADMIN)&&!pageQuery.getIsAll()) {

            if (DictUtil.LEADER_ROLE.equals(role)) {
                List<Long> departmentInfos = userInfoDomainService.getCurrDeptIds();

                pageQuery.setDepartmentIds(departmentInfos);
            }

            //员工查看自己位置
            if (DictUtil.STAFF_ROLE.equals(role)) {
                UserInfo userInfo = userInfoDomainService.getCurrUser();
                pageQuery.setDepartmentIds(Lists.newArrayList(userInfo.getDepartmentId()));
            }
        }
        if(pageQuery.getUserId() != null){
            UserInfo userInfo = userInfoDomainService.getById(pageQuery.getUserId());
            pageQuery.setDepartmentIds(Lists.newArrayList(userInfo.getDepartmentId()));
        }
        List<LocationVo> locationTreeVos =  this.locationDomainService.getLocationTreeList(pageQuery);
        return locationTreeVos;
    }

    public List<Location> getByType(Integer type) {
        List<Location> locations = locationDomainService.lambdaQuery()
                .eq(Location::getType, type)
                .eq(Location::getIsEnable, true)
                .list();
        List<Long> parentIds = locations.stream().filter(v -> !v.getParentId().equals(Location.TOP)).map(Location::getParentId).collect(Collectors.toList());
        List<Location> parentLocations = locationDomainService.lambdaQuery().in(Location::getId, parentIds).list();
        Map<Long, Location> parentMap = parentLocations.stream().collect(Collectors.toMap(Location::getId, it -> it));
        locations.forEach(location -> {
            if (type == 1) {
                location.setName(this.getFullName(location));
            }
            if (type == 2) {
                if (parentMap.containsKey(location.getParentId())) {
                    Location parentLocation = parentMap.get(location.getParentId());
                    location.setName(this.getFullName(parentLocation) + "/" + getFullName(location));

                }
            }
        });
        return locations;
    }

    private String getFullName(Location location) {
        if (StringUtils.isNotBlank(location.getAnotherName())
                && !location.getType().equals(2)) {
            return location.getName() + "(" + location.getAnotherName() + ")";
        }
        return location.getName();
    }


    public List<Location> getByParentId(Long parentId) {
        List<Location> rootLocation = locationDomainService.lambdaQuery().eq(Location::getParentId, Location.TOP).list();
        List<Location> locations = locationDomainService.lambdaQuery()
                .eq(parentId != 0L, Location::getParentId, parentId)
                .eq(parentId == 0L, Location::getParentId, rootLocation.get(0).getId())
                .eq(Location::getIsEnable, true)
                .list();
        return locations;
    }


    /**
     * 下载模板
     *
     * @param response
     * @return
     */
    public Boolean downTemplate(HttpServletResponse response) {
        List<LocationExcel> excelList = new ArrayList<>();
        try {
            //页面下载设置
            DownloadResponseUtil.addDownLoadHeader(response, "位置信息导入模板.xlsx");
            //将并添加信息，实体类结构，类的数据做成excel表格对象workbook
            Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams(),
                    LocationExcel.class, excelList
            );
            workbook.write(response.getOutputStream());
        } catch (IOException e) {
            e.printStackTrace();
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    /**
     * 导入用户
     *
     * @param file
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean importData(MultipartFile file) {
        List<LocationExcel> excels;
        try {
            InputStream in = file.getInputStream();
            ImportParams params = new ImportParams();
            params.setNeedVerify(true);
            params.setVerifyHandler(new LocationExcelVerifyHandler());
            ExcelImportResult<LocationExcel> excelImportResult = ExcelImportUtil.importExcelMore(in, LocationExcel.class, params);
            if (excelImportResult.isVerifyFail()) {
                String errorMsg = excelImportResult.getFailList().stream().map(v -> {
                    return "第" + v.getRowNum() + "行，" + v.getErrorMsg();
                }).collect(Collectors.joining("！"));
                throw new MaginaException(errorMsg);
            }
            excels = excelImportResult.getList();
            Map<String, List<LocationExcel>> stringListMap = excels.stream().collect(Collectors.groupingBy(LocationExcel::getCode));
            for (Map.Entry<String, List<LocationExcel>> entry : stringListMap.entrySet()) {
                List<LocationExcel> value = entry.getValue();
                if (value.size() > 1) {
                    throw new MaginaException("位置编码" + entry.getKey() + "本次导入存在重复数据");
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new MaginaException("导入失败！原因：" + e.getMessage());
        }

        if (CollectionUtils.isEmpty(excels)) {
            throw new MaginaException("导入失败！解析数据为空！");
        }
        Map<String, Long> parentMap = excels.stream().collect(Collectors.toMap(LocationExcel::getCode, LocationExcel::getId));
        List<Location> locationTrees = excels.stream().map(v -> {
            Location location = new Location();
            location.setId(v.getId());
            location.setName(v.getName());
            location.setAnotherName(v.getAnotherName());
            location.setCode(v.getCode());
            location.setDepartmentIds(v.getDepartmentIds());
            location.setSort(v.getSort());
            if (StringUtils.isNotBlank(v.getParentCode())) {
                if (v.getParentId() == null) {
                    location.setParentId(parentMap.get(v.getParentCode()));
                } else {
                    location.setParentId(v.getParentId());
                }
            } else {
                location.setParentId(Location.TOP);
                location.setType(0);
                location.setIsLeaf(false);
            }
            location.setIsEnable(true);
            location.setIsAvailable(true);
            location.setUpdatedAt(LocalDateTime.now());
            location.setUpdatedBy(ApplicationSessions.id());
            return location;
        }).collect(Collectors.toList());
        this.locationDomainService.saveOrUpdateBatch(locationTrees);
        ExecutorUtils.doAfterCommit(() -> {
            this.setFullIdPaths(locationTrees);

            this.setFullIdNames(locationTrees);


            //设置上级为非叶子节点
            List<Long> parentIds = locationTrees.stream().filter(v -> v.getParentId() != Location.TOP)
                    .map(Location::getParentId).distinct().collect(Collectors.toList());
            locationDomainService.lambdaUpdate().set(Location::getIsLeaf, false)
                    .in(Location::getId, parentIds).update();
        });
        return Boolean.TRUE;
    }

    public void setFullIdPaths(List<Location> locationTrees) {
        for (Location location : locationTrees) {
            setFullIdPath(location);
            if (location.getType() == null) {
                List<String> locationIds = Arrays.stream(location.getFullIdPath().split(StringConstants.SLASH))
                        // 去掉头
                        .skip(1).collect(Collectors.toList());
                location.setType(locationIds.size()-1);
            }
            locationDomainService.updateById(location);
        }
    }

    public void setFullIdNames(List<Location> locationTrees) {
        locationTrees.forEach(this::setFullName);
        this.locationDomainService.updateBatchById(locationTrees);
    }

    /**
     * 位置编码重复性校验
     *
     * @param location {@link Location}
     */
    protected void checkLocation(Location location) {
        Long codeCount =
                this.locationDomainService.lambdaQuery()
                        .eq(Location::getCode, location.getCode())
                        .eq(Location::getIsAvailable, true)
                        .ne(Objects.nonNull(location.getId()), Location::getId, location.getId())
                        .count();

        if (codeCount > 0) {
            throw new MaginaException("位置编码已存在");
        }

        Long levelCount =
                this.locationDomainService.lambdaQuery()
                        .eq(Location::getParentId, location.getParentId())
                        .eq(Location::getFullName, location.getName())
                        .eq(Location::getIsAvailable, true)
                        .ne(Objects.nonNull(location.getId()), Location::getId, location.getId())
                        .count();

        if (levelCount > 0) {
            throw new MaginaException("同层级已存在相同位置名称");
        }
    }

    /**
     * 设置id全路径
     *
     * @param location {@link Location}
     */
    protected void setFullIdPath(Location location) {
        // 设置全路径id
        location.setFullIdPath(
                Optional.ofNullable(location.getParentId())
                        .map(this.locationDomainService::getById)
                        .map(Location::getFullIdPath)
                        .orElse(StringConstants.EMPTY)
                        + section(location.getId())
        );
    }


    /**
     * 获得位置全路径
     * 如：/1/2/3/4
     *
     * @param currLocation 位置全路径
     * @return 位置全路径
     */
    public void setFullName(Location currLocation) {
        List<String> locationIds = Arrays.stream(currLocation.getFullIdPath().split(StringConstants.SLASH))
                // 去掉头
                .skip(1).collect(Collectors.toList());
        List<Location> locationTrees = locationDomainService.lambdaQuery()
                .in(Location::getId, locationIds)
                .ne(currLocation != null, Location::getId, currLocation.getId())
                .list();
        StringJoiner joiner = new StringJoiner(StringConstants.SLASH);

        for (Location location : locationTrees) {
            if (!location.getParentId().equals(Location.TOP)) {
                joiner.add(this.getFullName(location));
            }
        }
        joiner.add(this.getFullName(currLocation));
        currLocation.setFullName(joiner.toString());
    }

    /**
     * 路径分段
     *
     * @param id 位置id
     * @return 路径段
     */
    static String section(Long id) {
        return StringConstants.SLASH + id;
    }

    public Boolean clearLocation() {
        locationDomainService.clearLocation();
        return Boolean.TRUE;
    }

}
