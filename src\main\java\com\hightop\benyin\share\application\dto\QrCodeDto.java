package com.hightop.benyin.share.application.dto;

import lombok.AccessLevel;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.time.LocalDateTime;

/**
 * 二维码生成dto
 * @Author: X.S
 * @date 2024/09/09 10:39
 */
@FieldDefaults(level = AccessLevel.PRIVATE)
@Data
@Builder
public class QrCodeDto<T> {
    /**
     * 二维码类型
     */
    String type;
    /**
     * 业务数据
     */
    T data;
    /**
     * 创建时间
     */
    @Builder.Default
    LocalDateTime createdAt = LocalDateTime.now();
}
