package com.hightop.benyin.rfid.infrastructure.mapper;

import com.github.yulichang.base.MPJBaseMapper;
import com.hightop.benyin.rfid.application.vo.po.RfidAssetTakeDetailVo;
import com.hightop.benyin.rfid.application.vo.query.RfidAssetTakeDetailQuery;
import com.hightop.benyin.rfid.application.vo.query.RfidAssetTakeQuery;
import com.hightop.benyin.rfid.application.vo.query.RfidTakeDetailQuery;
import com.hightop.benyin.rfid.infrastructure.entity.RfidAssetTake;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * RFID资产盘点表mapper
 *
 * <AUTHOR>
 * @date 2024-09-15 14:53:13
 */
public interface RfidAssetTakeMapper extends MPJBaseMapper<RfidAssetTake> {
    /**
     * 查询资产盘点列表
     * @param query
     * @return
     */
    public List<RfidAssetTake> findAssetTakeList(@Param("qo") RfidAssetTakeQuery query);

   /**
     * 查询资产盘点明细列表
     * @param query
     * @return
     */
    public List<RfidAssetTakeDetailVo> findAssetTakeDetailList(@Param("qo") RfidAssetTakeDetailQuery query);

   /**
     * 查询资产盘点明细扫描结果
     * @param rfidTakeDetailQuery
     * @return
     */
    public List<RfidAssetTakeDetailVo> findAssetTakeScanList(@Param("qo") RfidTakeDetailQuery rfidTakeDetailQuery);

    @Delete("TRUNCATE TABLE b_rfid_asset_take")
    void clear();
}
