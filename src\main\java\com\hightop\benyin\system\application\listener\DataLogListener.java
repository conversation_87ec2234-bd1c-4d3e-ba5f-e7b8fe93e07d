package com.hightop.benyin.system.application.listener;


import com.alibaba.fastjson.JSONArray;
import com.hightop.benyin.configurer.enums.DataType;
import com.hightop.benyin.rfid.domain.service.RfidReaderServiceDomain;
import com.hightop.benyin.rfid.infrastructure.entity.RfidReader;
import com.hightop.benyin.share.infrastructure.util.DictUtil;
import com.hightop.benyin.system.application.service.DepartmentExtendService;
import com.hightop.benyin.system.application.service.LocationService;
import com.hightop.benyin.system.domain.event.DataLogEvent;
import com.hightop.benyin.system.domain.service.DataLogDomainService;
import com.hightop.benyin.system.domain.service.DepartmentInfoDomainService;
import com.hightop.benyin.system.domain.service.UserInfoDomainService;
import com.hightop.benyin.system.infrastructure.entity.DataLog;
import com.hightop.benyin.system.infrastructure.entity.DepartmentInfo;
import com.hightop.benyin.system.infrastructure.entity.Location;
import com.hightop.benyin.system.infrastructure.entity.UserInfo;
import com.hightop.fario.base.util.CollectionUtils;
import com.hightop.fario.base.util.StringUtils;
import com.hightop.magina.standard.code.dictionary.item.DictItem;
import com.hightop.magina.standard.code.dictionary.item.DictItemDomainService;
import com.hightop.magina.standard.ums.user.privacy.UserSex;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 基础数据变更日志监听器
 */
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Service
@Slf4j
public class DataLogListener {
    DataLogDomainService dataLogDomainService;
    UserInfoDomainService userInfoDomainService;
    DepartmentInfoDomainService departmentInfoDomainService;
    DepartmentExtendService departmentExtendService;
    LocationService locationTreeService;
    DictItemDomainService dictItemDomainService;
    RfidReaderServiceDomain rfidReaderServiceDomain;


    @EventListener
    public void onDataChanged(DataLogEvent event) {
        List<DataLog> dataLogs = event.getDataLogs();
        if (CollectionUtils.isEmpty(dataLogs)) {
            log.error("dataLogs is null");
            return;
        }
        for (DataLog dataLog : dataLogs) {
            if (dataLog.getDataType().equals(DataType.TXT)) {
                continue;
            }
            dataLog.setOriData(convertDataContent(dataLog.getOriData(), dataLog.getDataType(), dataLog.getDictCode()));
            dataLog.setNewData(convertDataContent(dataLog.getNewData(), dataLog.getDataType(), dataLog.getDictCode()));
        }
        dataLogDomainService.saveBatch(dataLogs);
    }

    /**
     * 转换数据内容
     *
     * @param data
     * @param dataType
     * @return
     */
    public String convertDataContent(String data, DataType dataType, String dictCode) {
        if (StringUtils.isNotBlank(data)) {
            switch (dataType) {
                case USER:
                    UserInfo userInfo = userInfoDomainService.getById(data);
                    return userInfo.getName();
                case ENABLE:
                    // 启用状态
                    return Boolean.parseBoolean(data) ? "启用" : "禁用";
                case IF:
                    // 是否
                    return Boolean.parseBoolean(data) ? "是" : "否";
                case DEPT:
                    DepartmentInfo department = departmentInfoDomainService.getById(Long.parseLong(data));
                    return department != null ? department.getName().getValue() : data;
                case ON_OFF:
                    return Integer.parseInt(data) == (DictUtil.ON) ? "打开" : "关闭";
                case DEPTS:
                    List<String> departmentIds = JSONArray.parseArray(data, String.class);
                    List<DepartmentInfo> departments = departmentInfoDomainService
                            .lambdaQuery().in(DepartmentInfo::getId, departmentIds).list();
                    return departments.stream().map(v -> {
                        return v.getName().getValue();
                    }).collect(Collectors.joining(","));
                case LOCATION:
                    Location location = locationTreeService.getById(Long.parseLong(data));
                    return location != null ? location.getLocation() : data;
                case READER:
                    RfidReader rfidReader = rfidReaderServiceDomain.getById(Long.parseLong(data));
                    return rfidReader != null ? rfidReader.getDeviceId() : data;
                case DICT:
                    DictItem dictItem = dictItemDomainService.getByDictCodeAndValue(dictCode, data);
                    return dictItem != null ? dictItem.getLabel() : data;
                case SEX:
                    for (UserSex e : UserSex.values()) {
                        if (e.getCode().equals(data)) {
                            return e.getName();
                        }
                    }
                default:
                    break;
            }
        }
        return data;
    }
}
