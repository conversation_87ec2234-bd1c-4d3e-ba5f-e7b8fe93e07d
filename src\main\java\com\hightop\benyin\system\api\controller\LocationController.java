package com.hightop.benyin.system.api.controller;

import com.hightop.benyin.share.infrastructure.util.DownloadResponseUtil;
import com.hightop.benyin.system.api.vo.LocationVo;
import com.hightop.benyin.system.api.vo.dto.LocationAddDto;
import com.hightop.benyin.system.api.vo.dto.LocationFastAddDto;
import com.hightop.benyin.system.api.vo.dto.LocationUpdateDto;
import com.hightop.benyin.system.api.vo.query.LocationQuery;
import com.hightop.benyin.system.application.service.LocationService;
import com.hightop.benyin.system.infrastructure.entity.Location;
import com.hightop.fario.base.web.Operation;
import com.hightop.fario.base.web.RestResponse;
import com.hightop.magina.standard.behavior.operation.IgnoreOperationLog;
import com.hightop.magina.standard.behavior.operation.OperationLogType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * 位置管理rest接口
 *
 * @Author: X.S
 * @date 2022/09/13 21:25
 * @since 1.0.0
 */
@RestController
@RequestMapping("/location")
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Api(tags = "位置树管理")
@OperationLogType
public class LocationController {
    LocationService locationService;

    @PostMapping("/tree")
    @ApiOperation("位置树查询")
    public List<LocationVo> tree(@RequestBody LocationQuery query) {
        return this.locationService.getLocationTree(query);
    }

    @PostMapping("/auth")
    @ApiOperation("带权限位置树查询")
    public List<LocationVo> authTree(@RequestBody LocationQuery query) {
        return this.locationService.getLocationAuthTree(query);
    }

    @GetMapping("/list")
    @ApiOperation("条件查询位置列表")
    public List<LocationVo> getByDepartmentId(LocationQuery query) {
        query.setIsEnable(true);
        query.setType(3);
        return this.locationService.getLocationList(query);
    }

    @GetMapping("/getByType/{type}")
    @ApiOperation("根据类型获取列表")
    public List<Location> getByType(@ApiParam("类型") @PathVariable Integer type) {
        return this.locationService.getByType(type);
    }

    @GetMapping("/getByParent/{parentId}")
    @ApiOperation("根据上级获取列表")
    public List<Location> getByType(@ApiParam("上级id") @PathVariable Long parentId) {
        return this.locationService.getByParentId(parentId);
    }

    @PostMapping
    @ApiOperation("位置树添加")
    public RestResponse<Void> add(@Validated @RequestBody LocationAddDto locationAddDto) {
        return Operation.ADD.response(this.locationService.save(locationAddDto.toLocation()));
    }

    @PostMapping("add")
    @ApiOperation("快速添加位置")
    public RestResponse<Void> add(@Validated @RequestBody LocationFastAddDto locationAddDto) {
        return Operation.ADD.response(this.locationService.batchSave(locationAddDto));
    }

    @PutMapping
    @ApiOperation("位置树修改")
    public RestResponse<Void> update(@Validated @RequestBody LocationUpdateDto locationUpdateDto) {
        return Operation.UPDATE.response(this.locationService.updateById(locationUpdateDto.toLocation()));
    }

    @DeleteMapping("/{id}")
    @ApiOperation("位置树删除")
    public RestResponse<Void> delete(@ApiParam("位置id") @PathVariable Long id) {
        return Operation.DELETE.response(this.locationService.removeById(id));
    }

    @PutMapping("/enable/{id}/{enable}")
    @ApiOperation("位置树启停")
    public RestResponse<Void> enable(@ApiParam(value = "位置id", required = true) @PathVariable Long id,
                                     @ApiParam(value = "启停标识", required = true) @PathVariable Boolean enable) {
        return Operation.UPDATE.response(this.locationService.updateEnable(id, enable));
    }

    @DeleteMapping("clear")
    @ApiOperation("清理位置数据")
    public RestResponse<Void> delete() {
        return Operation.UPDATE.response(this.locationService.clearLocation());
    }


    /**
     * 导入位置
     **/
    @PostMapping("/import")
    @ApiOperation(value = "导入位置数据")
    public RestResponse<Void> impor(@RequestParam("file") MultipartFile file) throws Exception {
        boolean b = this.locationService.importData(file);
        if (!b) {
            return new RestResponse(500, "解析失败", null, null);
        }
        return RestResponse.message("导入成功");
    }

    /**
     * 下载位置模板
     *
     * @param response
     * @return
     */
    @ApiOperation("下载导入位置模板")
    @GetMapping("/downTemplate")
    public RestResponse<Void> downTemplate(HttpServletResponse response) {
        Boolean b = locationService.downTemplate(response);
        if (!b) {
            return new RestResponse(500, "下载失败", null, null);
        }
        return RestResponse.message("下载成功");
    }

    /**
     * 导出位置信息
     *
     * @param response
     * @param pageQuery
     * @return
     */
    @ApiOperation("/导出位置信息")
    @PostMapping("/export")
    @IgnoreOperationLog
    public RestResponse<Void> downOrderData(HttpServletResponse response, @RequestBody LocationQuery pageQuery) {
        try {
            //页面下载设置
            Workbook workbook = locationService.downloadData(pageQuery);
            DownloadResponseUtil.addDownLoadHeader(response, "位置信息.xlsx");
            workbook.write(response.getOutputStream());
        } catch (IOException e) {
            e.printStackTrace();
            return new RestResponse(500, "导出失败", null, null);
        }
        return RestResponse.message("导出成功");
    }

}
