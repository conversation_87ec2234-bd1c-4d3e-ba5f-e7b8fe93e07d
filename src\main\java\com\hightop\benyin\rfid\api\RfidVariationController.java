package com.hightop.benyin.rfid.api;

import com.hightop.benyin.rfid.application.service.RfidVariationService;
import com.hightop.benyin.rfid.application.vo.dto.AssetVariationDealDto;
import com.hightop.benyin.rfid.application.vo.query.RfidInfoQuery;
import com.hightop.benyin.rfid.application.vo.query.RfidVariationQuery;
import com.hightop.benyin.rfid.infrastructure.entity.RfidAsset;
import com.hightop.benyin.rfid.infrastructure.entity.RfidVariation;
import com.hightop.benyin.share.application.dto.MailSendDto;
import com.hightop.benyin.share.infrastructure.entity.MailRecord;
import com.hightop.benyin.system.infrastructure.entity.DepartmentInfo;
import com.hightop.fario.base.web.Operation;
import com.hightop.fario.base.web.RestResponse;
import com.hightop.fario.common.mybatis.DataGrid;
import com.hightop.magina.standard.behavior.operation.IgnoreOperationLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * Rfid异动数据管理rest接口
 *
 * <AUTHOR>
 * @date 2024-10-12 16:36:01
 */
@RequestMapping("/variation")
@RestController
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Api(tags = "Rfid异动数据管理")
public class RfidVariationController {
    RfidVariationService rfidVariationService;


    @PostMapping("/page")
    @ApiOperation("分页查询")
    @IgnoreOperationLog
    public RestResponse<DataGrid<RfidVariation>> page(@RequestBody RfidVariationQuery pageQuery) {
        return RestResponse.ok(this.rfidVariationService.page(pageQuery));
    }

    @GetMapping("/noticeInfo/{id}")
    @ApiOperation("异动资产通知详情查询")
    @IgnoreOperationLog
    public RestResponse<MailRecord> getNoticeInfo(@PathVariable("id") Long id) {
        return RestResponse.ok(this.rfidVariationService.noticeInfo(id));
    }

    @PutMapping("notice")
    @ApiOperation("通知异动资产")
    public RestResponse<Void> process(@Validated @RequestBody MailSendDto mailSendDto) {
        return Operation.UPDATE.response(this.rfidVariationService.notice(mailSendDto));
    }

    @PutMapping("/process")
    @ApiOperation("处理异动")
    public RestResponse<Void> process(@RequestBody AssetVariationDealDto assetVariationDealDto) {
        return Operation.UPDATE.response(this.rfidVariationService.deal(assetVariationDealDto));
    }


    @PutMapping("complete/{id}")
    @ApiOperation("处理完成异动资产")
    public RestResponse<Void> complete(@PathVariable("id") Long id) {
        return Operation.UPDATE.response(this.rfidVariationService.complete(id));
    }

    @GetMapping("/lastPosition")
    @ApiOperation("最后位置-分页查询")
    public RestResponse<DataGrid<RfidVariation>> lastPosition(RfidVariationQuery pageQuery) {
        return RestResponse.ok(this.rfidVariationService.lastRfidRvariationPage(pageQuery));
    }


    @GetMapping("/getChangeDept/{infoId}")
    @ApiOperation("获取异动部门查询")
    public RestResponse<List<DepartmentInfo>> getChangeDept(@PathVariable("infoId") Long infoId) {
        return RestResponse.ok(this.rfidVariationService.getChangeDept(infoId));
    }

    @PostMapping("/instore/{id}")
    @ApiOperation("登记报溢资产")
    public RestResponse<Void> stash(@Validated @RequestBody RfidAsset rfidasset, @PathVariable("id") Long id) {
        return Operation.UPDATE.response(this.rfidVariationService.instore(rfidasset, id));
    }

    /**
     * 导出基站数据
     *
     * @param response
     * @param pageQuery
     * @return
     */
    @ApiOperation("/导出异动数据")
    @GetMapping("/export")
    public RestResponse<Void> export(HttpServletResponse response, RfidVariationQuery pageQuery) throws IOException {
        Boolean b = rfidVariationService.downloadData(response, pageQuery);
        if (!b) {
            return new RestResponse(500, "导出失败，请联系技术人员！", null, null);
        }
        return RestResponse.message("导出成功");
    }

    @DeleteMapping("clear")
    @ApiOperation("清理异动数据")
    public RestResponse<Void> delete() {
        return Operation.UPDATE.response(this.rfidVariationService.clear());
    }

}
