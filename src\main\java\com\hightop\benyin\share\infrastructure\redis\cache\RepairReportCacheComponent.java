package com.hightop.benyin.share.infrastructure.redis.cache;

import com.hightop.fario.common.redis.cache.string.AbstractValueOperations;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2024/08/5 15:40
 */
@Component
public class RepairReportCacheComponent extends AbstractValueOperations<String, String> {
    public RepairReportCacheComponent() {
        super(SystemCacheNaming.REPAIR_REPORT);
    }

    public boolean setAndExpire(String keyword, String value, long timeout){
        return super.setAndExpire(keyword, value, timeout, TimeUnit.HOURS);
    }

}
