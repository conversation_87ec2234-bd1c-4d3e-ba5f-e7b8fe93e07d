package com.hightop.benyin.rfid.application.service;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import com.alibaba.fastjson.JSONObject;
import com.github.yulichang.toolkit.MPJWrappers;
import com.hightop.benyin.rfid.application.handler.ReaderExcelVerifyHandler;
import com.hightop.benyin.rfid.application.vo.dto.ReaderSeScanStatusDto;
import com.hightop.benyin.rfid.application.vo.dto.ReaderSetHeartBeatDto;
import com.hightop.benyin.rfid.application.vo.dto.ReaderSettingDto;
import com.hightop.benyin.rfid.application.vo.excel.Readerxcel;
import com.hightop.benyin.rfid.application.vo.po.AssetReaderTotalVo;
import com.hightop.benyin.rfid.application.vo.po.ReaderCheck;
import com.hightop.benyin.rfid.application.vo.query.RfidReaderQuery;
import com.hightop.benyin.rfid.domain.service.RfidAssetServiceDomain;
import com.hightop.benyin.rfid.domain.service.RfidReaderServiceDomain;
import com.hightop.benyin.rfid.infrastructure.entity.RfidAsset;
import com.hightop.benyin.rfid.infrastructure.entity.RfidReader;
import com.hightop.benyin.rfid.infrastructure.enums.ReaderStatus;
import com.hightop.benyin.share.infrastructure.util.DictUtil;
import com.hightop.benyin.share.infrastructure.util.DownloadResponseUtil;
import com.hightop.benyin.share.socket.common.SocketMessage;
import com.hightop.benyin.share.socket.enums.CommandType;
import com.hightop.benyin.share.socket.util.MsgUtil;
import com.hightop.benyin.share.socket.util.ReaderControlTool;
import com.hightop.benyin.share.socket.util.SocketUtil;
import com.hightop.benyin.system.domain.service.UserInfoDomainService;
import com.hightop.fario.base.util.CollectionUtils;
import com.hightop.fario.base.util.StringUtils;
import com.hightop.fario.common.core.util.ExecutorUtils;
import com.hightop.fario.common.mybatis.DataGrid;
import com.hightop.fario.common.mybatis.PageQuery;
import com.hightop.fario.common.mybatis.util.PageHelper;
import com.hightop.magina.casual.session.ApplicationSessions;
import com.hightop.magina.core.exception.MaginaException;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Rfid基站管理服务
 *
 * <AUTHOR>
 * @date 2024-10-12 16:36:01
 */
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Service
@Slf4j
public class RfidReaderService {

    RfidReaderServiceDomain rfidReaderServiceDomain;
    RfidAssetServiceDomain rfidAssetServiceDomain;
    UserInfoDomainService userInfoDomainService;
    RedisTemplate<String, String> redisTemplate;

    /**
     * Rfid基站管理分页查询
     *
     * @param pageQuery {@link PageQuery}
     * @return {@link DataGrid}
     */
    public DataGrid<RfidReader> page(RfidReaderQuery pageQuery) {
        return PageHelper.startPage(pageQuery, p -> this.rfidReaderServiceDomain.getReaderList(pageQuery));
    }

    /**
     * Rfid基站管理分页查询
     *
     * @param pageQuery {@link PageQuery}
     * @return {@link DataGrid}
     */
    public AssetReaderTotalVo getTotal(RfidReaderQuery pageQuery) {
        List<RfidReader> rfidReaders = this.rfidReaderServiceDomain.getReaderList(pageQuery);
        AssetReaderTotalVo assetReaderTotalVo = new AssetReaderTotalVo();
        assetReaderTotalVo.setNumber(rfidReaders.size());
        assetReaderTotalVo.setNormalNum(rfidReaders.stream().filter(reader -> ReaderStatus.NORMAL.equals(reader.getStatus())).count());
        assetReaderTotalVo.setBreakNum(rfidReaders.stream().filter(reader -> ReaderStatus.BREAKDOWN.equals(reader.getStatus())).count());
        return assetReaderTotalVo;
    }

    /**
     * 根据位置查询有效基站
     *
     * @param locationId
     * @return
     */
    public List<RfidReader> getByLocationId(Long locationId) {
        return this.rfidReaderServiceDomain.lambdaQuery()
                .eq(RfidReader::getIsEnable, true)
                .isNotNull(RfidReader::getDeviceId)
                .eq(RfidReader::getLocationId, locationId).list();
    }


    public ReaderCheck checkReader(RfidReader reader) {
        ReaderCheck readerCheck = new ReaderCheck();
        readerCheck.setStatus(DictUtil.DISABLE);
        if (reader.getLocationId() == null) {
            readerCheck.setStatus(DictUtil.SUB);
            readerCheck.setMessage("基站未绑定位置！");
        }
        if (StringUtils.isBlank(reader.getDeviceId())) {
            readerCheck.setStatus(DictUtil.SUB);
            readerCheck.setMessage("基站未绑定设备！");
        }
        if (!reader.getStatus().equals(ReaderStatus.NORMAL)) {
            readerCheck.setStatus(DictUtil.SUB);
            readerCheck.setMessage("基站已断线！");
        }
        if (reader.getScanStatus().equals(DictUtil.OFF)) {
            readerCheck.setStatus(DictUtil.SUB);
            readerCheck.setMessage("基站未开启扫描功能！");
        }
        if (reader.getEnergyStatus().equals(DictUtil.OFF)) {
            readerCheck.setStatus(DictUtil.SUB);
            readerCheck.setMessage("基站打开节能模式！");
        }
        return readerCheck;
    }

    /**
     * Rfid基站管理分页查询
     *
     * @param pageQuery {@link PageQuery}
     * @return {@link DataGrid}
     */
    public DataGrid<RfidReader> authPage(RfidReaderQuery pageQuery) {
        if (CollectionUtils.isEmpty(pageQuery.getDepartmentIds())) {
            //获取当前用户所属公司的部门列表
            pageQuery.setDepartmentIds(userInfoDomainService.getCurrCompanyDeptList());
        }
        return PageHelper.startPage(pageQuery, p -> this.rfidReaderServiceDomain.getReaderList(pageQuery));
    }

    private List<RfidReader> queryList(RfidReaderQuery pageQuery) {
        List<RfidReader> rfidReaders = this.rfidReaderServiceDomain.getReaderList(pageQuery);
        //部门 位置全路径
//        rfidReaders.forEach(reader -> {
//            if(reader.getLocationId()!=null){
//                reader.setLocation(locationDomainService.getLocalFullPath(reader.getFullIdPath()));
//            }
//            if(CollectionUtils.isNotEmpty(reader.getDepartmentIds())) {
//                List<DepartmentInfo> departmentInfos = departmentInfoDomainService.lambdaQuery().in(DepartmentInfo::getId, reader.getDepartmentIds()).list();
//                reader.setDepartmentNames(departmentExtendService.getLocalFullPaths(departmentInfos));
//            }
//        });
        return rfidReaders;
    }

    /**
     * Rfid基站管理添加
     *
     * @param rfidReader {@link RfidReader}
     * @return true/false
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean save(RfidReader rfidReader) {
        Long count = rfidReaderServiceDomain.lambdaQuery()
                .eq(RfidReader::getCode, rfidReader.getCode())
                .eq(RfidReader::getLocationId, rfidReader.getLocationId())
                .count();
        if (count > 0L) {
            throw new MaginaException("该基站编码与位置数据已存在！");
        }
        Long rfidCount = rfidReaderServiceDomain.lambdaQuery().eq(RfidReader::getCode, rfidReader.getCode()).count();
        if (rfidCount > 0L) {
            throw new MaginaException("基站编码已存在");
        }
        if (StringUtils.isNotBlank(rfidReader.getDeviceId())) {
            Long ipCount = rfidReaderServiceDomain.lambdaQuery().eq(RfidReader::getDeviceId, rfidReader.getDeviceId()).count();
            if (ipCount > 0L) {
                throw new MaginaException("设备ID已存在");
            }
        }

        rfidReader.setDefaultInterval(rfidReader.getScanInterval());
        rfidReader.setDefaultDuration(rfidReader.getScanDuration());
        rfidReader.setCreatedBy(ApplicationSessions.id());
        rfidReader.setStatus(ReaderStatus.BREAKDOWN);
        rfidReader.setUpdatedBy(ApplicationSessions.id());
        return this.rfidReaderServiceDomain.save(rfidReader);
    }

    /**
     * 设置连接状态
     *
     * @param deviceId
     * @param status
     * @return
     */
    public boolean setCollectState(String deviceId, ReaderStatus status) {
        RfidReader rfidReader = rfidReaderServiceDomain.lambdaQuery().eq(RfidReader::getDeviceId, deviceId).one();
        if (rfidReader == null) {
            log.error("该基站不存在，{}！", deviceId);
        }
        rfidReader.setStatus(status);
        return this.rfidReaderServiceDomain.updateById(rfidReader);
    }

    /**
     * 设置全部关闭连接状态
     *
     * @return
     */
    public boolean setAllClose() {
        return rfidReaderServiceDomain.lambdaUpdate()
                .set(RfidReader::getStatus, ReaderStatus.BREAKDOWN)
                .eq(RfidReader::getStatus, ReaderStatus.NORMAL).update();
    }

    public boolean setting(ReaderSettingDto readerSettingDto) {
        RfidReader rfidReader = this.rfidReaderServiceDomain.getById(readerSettingDto.getId());
        if (rfidReader == null) {
            throw new MaginaException("该基站不存在！");
        }
        if (!ReaderStatus.NORMAL.equals(rfidReader.getStatus())) {
            throw new MaginaException("当前基站已离线，请在基站状态显示已连接时，再进行尝试！");
        }
        boolean sendResult = false;
        switch (readerSettingDto.getCommand()) {
            case REST:
                sendResult = ReaderControlTool.reset(rfidReader.getDeviceId());
                break;
            case SET_DORMANCY:
                rfidReader.setEnergyStatus(readerSettingDto.getStatus());
                sendResult = ReaderControlTool.dormancy(rfidReader.getDeviceId(), readerSettingDto.getStatus());
                break;
            case SET_BELL:
                rfidReader.setBellStatus(readerSettingDto.getStatus());
                sendResult = ReaderControlTool.bell(rfidReader.getDeviceId(), readerSettingDto.getStatus());
                break;
            default:
                break;
        }
        if (!sendResult) {
            throw new MaginaException("下发命令失败，请检查基站状态或联系管理员！");
        }
        rfidReader.setUpdatedBy(ApplicationSessions.id());
        return rfidReaderServiceDomain.updateById(rfidReader);
    }

    public boolean scanSetting(ReaderSeScanStatusDto readerSeScanStatusDto) {
        RfidReader rfidReader = this.rfidReaderServiceDomain.getById(readerSeScanStatusDto.getId());
        if (rfidReader == null) {
            throw new MaginaException("该基站不存在！");
        }
        if (!ReaderStatus.NORMAL.equals(rfidReader.getStatus())) {
            throw new MaginaException("当前基站已离线，请在基站状态显示已连接时，再进行尝试！");
        }
        rfidReader.setScanStatus(readerSeScanStatusDto.getStatus());
        rfidReader.setScanInterval(readerSeScanStatusDto.getInterval());
        rfidReader.setScanDuration(readerSeScanStatusDto.getDuration());
        rfidReader.setUpdatedBy(ApplicationSessions.id());
        boolean sendResult = false;
        if (readerSeScanStatusDto.getStatus() == DictUtil.ON) {
            sendResult = ReaderControlTool.openScan(rfidReader.getDeviceId(), readerSeScanStatusDto.getInterval(), readerSeScanStatusDto.getDuration());
        } else {
            sendResult = ReaderControlTool.closeScan(rfidReader.getDeviceId(), readerSeScanStatusDto.getInterval(), readerSeScanStatusDto.getDuration());
        }
        if (!sendResult) {
            throw new MaginaException("下发命令失败，请检查基站状态或联系管理员！");
        }
        return rfidReaderServiceDomain.updateById(rfidReader);
    }

    public boolean heatSetting(ReaderSetHeartBeatDto readerSetHeartBeatDto) {
        RfidReader rfidReader = this.rfidReaderServiceDomain.getById(readerSetHeartBeatDto.getId());
        if (rfidReader == null) {
            throw new MaginaException("该基站不存在！");
        }
        if (!ReaderStatus.NORMAL.equals(rfidReader.getStatus())) {
            throw new MaginaException("当前基站已离线，请在基站状态显示已连接时，再进行尝试！");
        }
        rfidReader.setHeatInterval(readerSetHeartBeatDto.getInterval());
        rfidReader.setUpdatedBy(ApplicationSessions.id());
        Boolean sendResult = ReaderControlTool.setHeatbeat(rfidReader.getDeviceId(), readerSetHeartBeatDto.getInterval());
        if (!sendResult) {
            throw new MaginaException("下发命令失败，请检查基站状态或联系管理员！");
        }
        return rfidReaderServiceDomain.updateById(rfidReader);
    }

    public JSONObject getDeviceInfo(String deviceId) {
        String key = DictUtil.READER_INFO + deviceId;
        String jsonStr = redisTemplate.opsForValue().get(key);
        if(StringUtils.isNotBlank(jsonStr)){
            return JSONObject.parseObject(jsonStr);
        }
        return null;
    }

    public Boolean getResult(String deviceId) {
        String key = DictUtil.READER_SETTING + deviceId;
        String jsonStr = redisTemplate.opsForValue().get(key);

        return Boolean.TRUE;
    }


    /**
     * 位置启停
     *
     * @param id   位置id
     * @param flag 启停标识
     * @return 是否更新成功
     */
    public boolean updateEnable(Long id, Boolean flag) {
        RfidReader rfidReader = this.rfidReaderServiceDomain.getById(id);
        rfidReader.setIsEnable(flag);
        if (!flag) {
            rfidReader.setExpireAt(LocalDateTime.now());
        } else {
            rfidReader.setExpireAt(null);
        }
        rfidReader.setUpdatedBy(ApplicationSessions.id());
        return rfidReaderServiceDomain.updateById(rfidReader);
    }

    /**
     * 发送数据批量
     *
     * @param readerIds
     * @return
     */
    public boolean sendAssetBatch(List<Long> readerIds) {
        readerIds = readerIds.stream().distinct().collect(Collectors.toList());
        for (Long id : readerIds) {
            boolean sendResult = this.sendAsset(id);
            if (!sendResult) {
                return sendResult;
            }
        }
        return Boolean.TRUE;
    }

    /**
     * 发送数据
     *
     * @param readerId
     * @return
     */
    public boolean sendAsset(Long readerId) {
        RfidReader rfidReader = rfidReaderServiceDomain.getById(readerId);
        if (rfidReader == null) {
            log.error("该基站不存在，{}！", readerId);
            return Boolean.FALSE;
        }
        List<RfidAsset> rfidAssets = rfidAssetServiceDomain.selectJoinList(RfidAsset.class, MPJWrappers.lambdaJoin()
                .selectAll(RfidAsset.class)
                .eq(RfidAsset::getReaderId, rfidReader.getId())
                .isNotNull(RfidAsset::getRfidCode)
        );
        List<String> assetCodes = rfidAssets.stream().filter(v -> StringUtils.isNotBlank(v.getRfidCode())).map(RfidAsset::getRfidCode).collect(Collectors.toList());
        boolean sendResult = false;
        if (CollectionUtils.isNotEmpty(assetCodes)) {
            String data = String.join("", assetCodes);
            sendResult = SocketUtil.send(rfidReader.getDeviceId(), new SocketMessage(CommandType.DOWNLOAD_TAG, assetCodes.size() * MsgUtil.RFID_HEX_LENGTH, data));
            if (sendResult) {
                log.info("{}下发标签数据成功", rfidReader.getDeviceId());
                rfidAssets.forEach(v -> {
                    v.setSyncState(true);
                });
                sendResult = rfidAssetServiceDomain.updateBatchById(rfidAssets);
            }
        } else {
            log.error("{}无标签数据下发", readerId);
        }
        return sendResult;
    }

    /**
     * Rfid基站管理修改
     *
     * @param rfidReader {@link RfidReader}
     * @return true/false
     */
    public boolean updateById(RfidReader rfidReader) {
        RfidReader original = this.rfidReaderServiceDomain.getById(rfidReader.getId());

        Long rfidCount = rfidReaderServiceDomain.lambdaQuery()
                .eq(RfidReader::getCode, rfidReader.getCode())
                .ne(RfidReader::getId, rfidReader.getId())
                .count();
        if (rfidCount > 0L) {
            throw new MaginaException("基站编码已存在");
        }
        if (StringUtils.isNotBlank(rfidReader.getDeviceId())) {
            Long ipCount = rfidReaderServiceDomain.lambdaQuery()
                    .ne(RfidReader::getId, rfidReader.getId())
                    .eq(RfidReader::getDeviceId, rfidReader.getDeviceId()).count();
            if (ipCount > 0L) {
                throw new MaginaException("设备ID已存在");
            }
        }
        original.setUpdatedBy(ApplicationSessions.id());
        original.setIsEnable(rfidReader.getIsEnable());
        original.setIsReport(rfidReader.getIsReport());
        original.setLocationId(rfidReader.getLocationId());
        original.setDeviceId(rfidReader.getDeviceId());
        original.setBellStatus(rfidReader.getBellStatus());
        original.setHeatInterval(rfidReader.getHeatInterval());
        original.setEnergyStatus(rfidReader.getEnergyStatus());
        original.setFocusInterval(rfidReader.getFocusInterval());
        original.setFocusDuration(rfidReader.getFocusDuration());
        original.setScanInterval(rfidReader.getScanInterval());
        original.setScanDuration(rfidReader.getScanDuration());
        original.setDefaultInterval(rfidReader.getScanInterval());
        original.setDefaultDuration(rfidReader.getScanDuration());
        this.rfidReaderServiceDomain.updateById(original);
        //如果位置变更，则同步更新资产表
//        if (!original.getLocationId().equals(rfidReader.getLocationId())) {
//            List<RfidAsset> rfidAssets = rfidAssetServiceDomain.lambdaQuery()
//                    .in(RfidAsset::getRfidCode, original.getCode())
//                    .list();
//            this.rfidAssetServiceDomain.lambdaUpdate()
//                    .eq(RfidAsset::getReaderId, original.getId())
//                    .set(RfidAsset::getLocationId, rfidReader.getLocationId())
//                    .update();
//            LocationTree location = this.locationDomainService.getById(original.getId());
//            LocationTree newLocationTree = this.locationDomainService.getById(rfidReader.getId());
//
//            if (CollectionUtils.isNotEmpty(rfidAssets)) {
//                List<AssetChangeLogDto> assetChangeLogDtos = rfidAssets.stream().map(v -> {
//                    return AssetChangeLogDto.builder()
//                            .assetId(v.getId())
//                            .changeCode(original.getCode())
//                            .createdBy(ApplicationSessions.id())
//                            .source(AssetChangeSource.READER)
//                            .operatType(AssetOperatType.UPDATE)
//                            .before(location.getLocation())
//                            .after(newLocationTree.getName())
//                            .build();
//                }).collect(Collectors.toList());
//                ExecutorUtils.doAfterCommit(
//                        () -> applicationEventPublisher.publishEvent(new AssetChangeEvent(assetChangeLogDtos))
//                );
//            }
//        }
        ExecutorUtils.doAfterCommit(() -> {
            if (rfidReader.getScanStatus() == DictUtil.ON) {
                ReaderControlTool.openScan(rfidReader.getDeviceId(), rfidReader.getScanInterval(), rfidReader.getScanDuration());
            } else {
                ReaderControlTool.closeScan(rfidReader.getDeviceId(), rfidReader.getScanInterval(), rfidReader.getScanDuration());
            }
            ReaderControlTool.openFocusScan(rfidReader.getDeviceId(), rfidReader.getFocusInterval(), rfidReader.getFocusDuration());
            ReaderControlTool.bell(rfidReader.getDeviceId(), rfidReader.getBellStatus());
            ReaderControlTool.setDisrect(rfidReader.getDeviceId(), rfidReader.getDirectStatus());
           // ReaderControlTool.openFocusScan(rfidReader.getDeviceId(), rfidReader.getFocusInterval(), rfidReader.getFocusDuration());
            ReaderControlTool.setHeatbeat(rfidReader.getDeviceId(), rfidReader.getHeatInterval());
        });
        return Boolean.TRUE;
    }

    public boolean switchDevice(Long id, String deviceId) {
        RfidReader rfidReader = this.rfidReaderServiceDomain.getById(id);
        if (rfidReader == null) {
            throw new MaginaException("基站不存在！");
        }
        if (rfidReader.getStatus().equals(ReaderStatus.NORMAL)) {
            throw new MaginaException("请关闭基站确认状态已断线再进行此操作！");
        }
        Long rfidCount = rfidReaderServiceDomain.lambdaQuery()
                .eq(RfidReader::getDeviceId, rfidReader.getDeviceId())
                .ne(RfidReader::getId, rfidReader.getId())
                .count();
        if (rfidCount > 0L) {
            throw new MaginaException("基站设备ID已存在");
        }
        rfidReader.setDeviceId(deviceId);
        rfidReader.setUpdatedBy(ApplicationSessions.id());
        return rfidReaderServiceDomain.updateById(rfidReader);
    }

    /**
     * Rfid基站管理删除
     *
     * @param id id
     * @return true/false
     */
    public boolean removeById(Long id) {
        return this.rfidReaderServiceDomain.removeById(id);
    }

    /**
     * 导入基站信息
     *
     * @param file
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean importData(MultipartFile file) {
        List<Readerxcel> excels;
        try {
            InputStream in = file.getInputStream();
            ImportParams params = new ImportParams();
            params.setNeedVerify(true);
            params.setVerifyHandler(new ReaderExcelVerifyHandler());
            ExcelImportResult<Readerxcel> excelImportResult = ExcelImportUtil.importExcelMore(in, Readerxcel.class, params);
            if (excelImportResult.isVerifyFail()) {
                String errorMsg = excelImportResult.getFailList().stream().map(v -> {
                    return "第" + v.getRowNum() + "行，" + v.getErrorMsg();
                }).collect(Collectors.joining("！"));
                throw new MaginaException(errorMsg);
            }
            excels = excelImportResult.getList();
            Map<String, List<Readerxcel>> codeListMap = excels.stream().collect(Collectors.groupingBy(Readerxcel::getCode));
            Map<String, List<Readerxcel>> deviceIdListMap = excels.stream().collect(Collectors.groupingBy(Readerxcel::getDeviceId));

            for (Map.Entry<String, List<Readerxcel>> entry : codeListMap.entrySet()) {
                List<Readerxcel> value = entry.getValue();
                if (value.size() > 1) {
                    throw new MaginaException("基站编码" + entry.getKey() + "本次导入存在重复数据");
                }
            }

            for (Map.Entry<String, List<Readerxcel>> entry : deviceIdListMap.entrySet()) {
                List<Readerxcel> value = entry.getValue();
                if (value.size() > 1) {
                    throw new MaginaException("设备ID" + entry.getKey() + "本次导入存在重复数据");
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new MaginaException("导入失败！原因：" + e.getMessage());
        }
        //存数据库
        if (CollectionUtils.isNotEmpty(excels)) {
            List<RfidReader> rfidReaders = new ArrayList<>();
            for (Readerxcel data : excels) {
                RfidReader reader = data.getOriReader() == null ? new RfidReader() : data.getOriReader();
                BeanUtils.copyProperties(data, reader);
                if (reader.getId() == null) {
                    reader.setCreatedBy(ApplicationSessions.id());
                }
                reader.setUpdatedBy(ApplicationSessions.id());
                reader.setStatus(ReaderStatus.BREAKDOWN);
                rfidReaders.add(reader);
            }
            this.rfidReaderServiceDomain.saveOrUpdateBatch(rfidReaders);
        }
        return Boolean.TRUE;
    }

    /**
     * 下载模板
     *
     * @param response
     * @return
     */
    public Boolean downTemplate(HttpServletResponse response) {
        List<Readerxcel> excelList = new ArrayList<>();
        try {
            //页面下载设置
            DownloadResponseUtil.addDownLoadHeader(response, "基站信息导入模板.xlsx");
            //将并添加信息，实体类结构，类的数据做成excel表格对象workbook
            Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams(),
                    Readerxcel.class, excelList
            );
            workbook.write(response.getOutputStream());
        } catch (IOException e) {
            e.printStackTrace();
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    /**
     * 导出基站数据
     *
     * @param response
     * @return
     */
    public Boolean download(HttpServletResponse response, RfidReaderQuery pageQuery) {
        try {
            //查询数据
            List<RfidReader> excelList = this.rfidReaderServiceDomain.getReaderList(pageQuery);
            //页面下载设置
            DownloadResponseUtil.addDownLoadHeader(response, "当前资产数据.xlsx");
            //将并添加信息，实体类结构，类的数据做成excel表格对象workbook
            Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams(), RfidReader.class, excelList);

            workbook.write(response.getOutputStream());
        } catch (IOException e) {
            e.printStackTrace();
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    public Boolean clearReader() {
        rfidReaderServiceDomain.clearReader();
        return Boolean.TRUE;
    }

}
