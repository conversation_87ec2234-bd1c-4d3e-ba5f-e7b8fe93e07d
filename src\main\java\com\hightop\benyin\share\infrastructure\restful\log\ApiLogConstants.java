package com.hightop.benyin.share.infrastructure.restful.log;

import com.hightop.fario.base.util.FarioThreadFactory;
import com.hightop.fario.common.core.util.ExecutorUtils;

import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * api调用日志常量
 * @Author: X.S
 * @date 2023/12/21 11:32
 */
public interface ApiLogConstants {
    /**
     * 消息id参数名
     */
    String MESSAGE_ID = "messageId";
    /**
     * 请求头消息id
     */
    String MESSAGE_ID_HEADER = "X-MESSAGE-ID";
    /**
     * 消息id头
     */
    String X_MESSAGE_ID = MESSAGE_ID_HEADER + ": {" + MESSAGE_ID + "}";

    /**
     * api日志线程池
     */
    ThreadPoolExecutor EXECUTOR =
        new ThreadPoolExecutor(
            Runtime.getRuntime().availableProcessors(),
            Runtime.getRuntime().availableProcessors(),
            0L,
            TimeUnit.MILLISECONDS,
            new LinkedBlockingQueue<>(),
            FarioThreadFactory.of("API-LOG", false, new AtomicInteger()),
            new ThreadPoolExecutor.CallerRunsPolicy()
        );

    /**
     * api日志异步过程
     * @param runnable 执行过程
     */
    static void run(Runnable runnable) {
        ExecutorUtils.run(EXECUTOR, runnable);
    }
}
