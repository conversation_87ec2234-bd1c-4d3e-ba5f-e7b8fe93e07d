<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hightop.benyin.rfid.infrastructure.mapper.RfidReaderMapper">

    <resultMap id="readerResultMap" type="com.hightop.benyin.rfid.infrastructure.entity.RfidReader">
        <result column="department_ids" property="departmentIds"  typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler" />
        <result column="id"  property="id" />
        <result column="device_id"  property="deviceId" />
        <result column="voltage"  property="voltage" />
        <result column="location_id"  property="locationId" />
        <result column="ip_addr"  property="ipAddr" />
        <result column="tag_num"  property="tagNum" />
        <result column="heat_interval"  property="heatInterval" />
        <result column="scan_interval"  property="scanInterval" />
        <result column="scan_duration"  property="scanDuration" />
        <result column="scan_status"  property="scanStatus" />
        <result column="focus_status"  property="focusStatus" />
        <result column="focus_interval"  property="focusInterval" />
        <result column="focus_duration"  property="focusDuration" />
        <result column="scan_mode"  property="scanMode" />
        <result column="heat_status"  property="heatStatus" />
        <result column="direct_status"  property="directStatus" />
        <result column="bell_status"  property="bellStatus" />
        <result column="versions"  property="versions" />
        <result column="power1"  property="power1" />
        <result column="power2"  property="power2" />
        <result column="power3"  property="power3" />
        <result column="power4"  property="power4" />
        <result column="energy_status"  property="energyStatus" />
        <result column="is_enable"  property="isEnable" />
        <result column="expire_at"  property="expireAt" />
        <result column="status"  property="status" />
        <result column="created_at"  property="createdAt" />
        <result column="created_by"  property="createdBy" />
        <result column="full_id_path"  property="fullIdPath" />
        <result column="updated_by"  property="updatedBy" />
        <result column="updated_at"  property="updatedAt" />
        <result column="location"  property="location" />
        <result column="locationCode"  property="locationCode" />
        <result column="updatedByName"  property="updatedByName" />
    </resultMap>

    <select id="getReaderList" resultMap="readerResultMap">

        SELECT t.id,
        t.code,
        t.device_id,
        t.voltage,
        t.location_id,
        t.ip_addr,
        t.tag_num,
        t.heat_interval,
        t.scan_interval,
        t.scan_duration,
        t.focus_status,
        t.focus_interval,
        t.focus_duration,
        t.versions,
        t.scan_mode,
        t.heat_status,
        t.scan_status,
        t.direct_status,
        t.bell_status,
        t.power1,
        t.power2,
        t.power3,
        t.power4,
        t.energy_status,
        t.status,
        t.is_report,
        t.is_enable,
        t.expire_at,
        t.created_by,
        t.created_at,
        t.updated_at,
        t.updated_by,
        t.deleted,
        t1.department_ids,
        t1.full_id_path,
        t1.full_name AS location,
        t1.code AS locationCode,
        t2.name AS updatedByName
        FROM b_rfid_reader t
        LEFT JOIN st_location t1 ON  (t1.id = t.location_id)
        LEFT JOIN st_user_basic t2 ON (t2.id = t.updated_by)
        WHERE t.deleted = false
            <if test="qo.code!= null and qo.code!= ''">
                and t.code like  concat ('%',#{qo.code},'%')
            </if>

            <if test="qo.deviceId!= null and qo.deviceId!= ''">
                and t.device_id like  concat ('%',#{qo.deviceId},'%')
            </if>

            <if test="qo.ipAddr!= null and qo.ipAddr!= ''">
                and t.ip_addr like  concat ('%',#{qo.ipAddr},'%')
            </if>

            <if test="qo.status!= null and qo.status!= ''">
                and t.status like  concat ('%',#{qo.status},'%')
            </if>

            <if test="qo.scanStatus!= null">
                and t.scan_status =  #{qo.scanStatus}
            </if>

            <if test="qo.energyStatus!= null">
                and t.energy_status =  #{qo.energyStatus}
            </if>

            <if test="qo.bellStatus!= null">
                and t.bell_status =  #{qo.bellStatus}
            </if>

            <if test="qo.isReport!= null">
                and t.is_report =  #{qo.isReport}
            </if>

            <if test="qo.isEnable!= null">
                and t.is_enable =  #{qo.isEnable}
            </if>

            <if test="qo.updatedByName!= null and qo.updatedByName!= ''">
                and t2.name like  concat ('%',#{qo.updatedByName},'%')
            </if>

            <if test="null != qo.startEnableDate and '' != qo.startEnableDate ">
                and t.created_at &gt;= concat(#{qo.startEnableDate},' 00:00:00')
            </if>
            <if test="null != qo.endEnableDate and '' != qo.endEnableDate ">
                and t.created_at &lt;= concat(#{qo.endEnableDate},' 23:59:59')
            </if>

            <if test="null != qo.startDisableDate and '' != qo.startDisableDate ">
                and t.expire_at &gt;= concat(#{qo.startDisableDate},' 00:00:00')
            </if>
            <if test="null != qo.endDisableDate and '' != qo.endDisableDate ">
                and t.expire_at &lt;= concat(#{qo.endDisableDate},' 23:59:59')
            </if>

            <if test="null!=qo.departmentIds and !qo.departmentIds.isEmpty()">
                AND
                <foreach collection="qo.departmentIds" item="id" separator=" OR " open="(" close=")">
                    JSON_CONTAINS(t1.department_ids,cast(#{id} as char ))
                </foreach>
            </if>
            <if test="null!=qo.locations and !qo.locations.isEmpty()">
                and t.location_id in
                <foreach collection="qo.locations" item="id" separator="," open="(" close=")">
                    #{id}
                </foreach>
            </if>

            <if test="null!=qo.ids and !qo.ids.isEmpty()">
                and t.id in
                <foreach collection="qo.ids" item="id" separator="," open="(" close=")">
                    #{id}
                </foreach>
            </if>

        ORDER BY t.created_at DESC
    </select>

</mapper>