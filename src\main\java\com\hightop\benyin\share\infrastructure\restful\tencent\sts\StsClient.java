package com.hightop.benyin.share.infrastructure.restful.tencent.sts;

import feign.RequestLine;

/**
 * 腾讯云sts rest 接口
 * @Author: X.S
 * @date 2023/10/25 16:35
 */
public interface StsClient {
    /**
     * sts host
     */
    String HOST = "sts.tencentcloudapi.com";

    /**
     * <a href="https://cloud.tencent.com/document/product/1312/48195">联合身份临时访问凭证</a>
     * <p>
     * 文档上并未说明需要body参数，示例给出的是query参数
     * @param request {@link FederationTokenRequest}
     * @return {@link StsResponse}
     */
    @RequestLine("POST /")
    StsResponse<FederationTokenResponse> federationToken(FederationTokenRequest request);
}
