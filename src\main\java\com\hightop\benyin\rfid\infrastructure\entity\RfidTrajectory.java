package com.hightop.benyin.rfid.infrastructure.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.*;
import com.hightop.benyin.configurer.annotation.RecordLogField;
import com.hightop.benyin.rfid.infrastructure.enums.TrajectorySource;
import com.hightop.benyin.rfid.infrastructure.enums.TrajectoryType;
import com.hightop.benyin.share.infrastructure.util.DictUtil;
import com.hightop.magina.standard.code.dictionary.bind.DictItemBind;
import com.hightop.magina.standard.code.dictionary.bind.DictItemEntry;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.time.LocalDateTime;

/**
 * rfid轨迹信息
 *
 * <AUTHOR>
 * @date 2024-10-12 16:36:01
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@TableName(value = "b_rfid_trajectory", autoResultMap = true)
@ApiModel
public class RfidTrajectory {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @ApiModelProperty("id")
    Long id;

    @TableField("asset_id")
    @ApiModelProperty("资产id")
    Long assetId;

    @TableField("rfid_code")
    @ApiModelProperty("rfid编码")
    String rfidCode;

    @TableField("trajectory_type")
    @ApiModelProperty("轨迹类型")
    @Excel(name = "轨迹类型", width = 30, orderNum = "6", enumExportField = "name")
    TrajectoryType trajectoryType;

    @TableField("trajectory_source")
    @ApiModelProperty("异动来源")
    @Excel(name = "异动来源", width = 30, orderNum = "6", enumExportField = "name")
    TrajectorySource trajectorySource;

    @TableField("reader_id")
    @ApiModelProperty("基站id")
    Long readerId;

    @TableField("device_id")
    @ApiModelProperty("基站设备id")
    String deviceId;

    @TableField("location_id")
    @ApiModelProperty("位置id")
    Long locationId;

    @TableField("location")
    @ApiModelProperty("位置")
    String location;

    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @ApiModelProperty("创建时间")
    @Excel(name = "异动时间", width = 30, orderNum = "12", format = "yyyy-MM-dd HH:mm:ss")
    LocalDateTime createdAt;

    @TableField("deleted")
    @ApiModelProperty("是否删除  0未删除  1删除")
    @TableLogic
    Integer deleted;

    @TableField(exist = false)
    @ApiModelProperty("资产名称")
    String assetName;

    @TableField(exist = false)
    @ApiModelProperty("资产编码")
    String assetCode;

    @TableField(exist = false)
    @ApiModelProperty("使用状态")
    @DictItemBind(DictUtil.DICT_USE_STATE)
    DictItemEntry useState;

    @TableField(exist = false)
    @ApiModelProperty("资产类型")
    String assetType;

    @TableField(exist = false)
    @ApiModelProperty("规格型号(选填)")
    String model;
}
