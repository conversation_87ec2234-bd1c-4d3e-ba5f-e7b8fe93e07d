package com.hightop.benyin.rfid.infrastructure.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.hightop.benyin.rfid.infrastructure.enums.FaultStatus;
import com.hightop.benyin.rfid.infrastructure.enums.HandleType;
import com.hightop.benyin.rfid.infrastructure.enums.ReaderStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.time.LocalDateTime;

/**
 * rfid基站故障信息
 *
 * <AUTHOR>
 * @date 2024-10-12 16:36:01
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@TableName("b_rfid_reader_fault")
@ApiModel
public class RfidReaderFault {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @ApiModelProperty("id")
    Long id;

    @TableField("reader_id")
    @ApiModelProperty("基站编码")
    Long readerId;

    @TableField("device_id")
    @ApiModelProperty("基站编码")
    String deviceId;

    @TableField("description")
    @ApiModelProperty("故障描述")
    String description;


    @TableField("status")
    @ApiModelProperty("状态")
    FaultStatus status;

    @TableField("handle_type")
    @ApiModelProperty("处理方式")
    HandleType handleType;

    @TableField("handle_id")
    @ApiModelProperty("处理人")
    Long handleId;

    @TableField(value = "handle_at")
    @ApiModelProperty("处理时间")
    LocalDateTime handleAt;

    @TableField("change_device_id")
    @ApiModelProperty("更换的基站设备")
    String changeDeviceId;

    @TableField("remark")
    @ApiModelProperty("备注")
    String remark;


    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @ApiModelProperty("创建时间")
    LocalDateTime createdAt;

    @TableField("deleted")
    @ApiModelProperty("是否删除  0未删除  1删除")
    @TableLogic
    Integer deleted;

    @TableField(exist = false)
    @ApiModelProperty("基站编码")
    String readerCode;

    @TableField(exist = false)
    @ApiModelProperty("位置")
    String location;

    @TableField(exist = false)
    @ApiModelProperty("处理人")
    String handleName;


}
