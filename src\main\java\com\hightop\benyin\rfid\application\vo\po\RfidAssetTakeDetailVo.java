package com.hightop.benyin.rfid.application.vo.po;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelIgnore;
import com.baomidou.mybatisplus.annotation.TableField;
import com.hightop.benyin.rfid.infrastructure.enums.*;
import com.hightop.benyin.share.infrastructure.util.DictUtil;
import com.hightop.fario.common.jackson.annotation.JsonAmount;
import com.hightop.magina.standard.code.dictionary.bind.DictItemBind;
import com.hightop.magina.standard.code.dictionary.bind.DictItemEntry;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@ApiModel("资产盘点明细DTO")
public class RfidAssetTakeDetailVo {

    @Excel(name = "盘点单号", width = 25, orderNum = "0")
    @ApiModelProperty("盘点单号")
    String takeCode;

    @ApiModelProperty(value = "盘点类型")
    @Excel(name = "盘点类型", width = 20, orderNum = "1",enumExportField = "name")
    TakeTypeEnums takeType;

    @ApiModelProperty(value = "盘点范围")
    @Excel(name = "范围类型", width = 20, orderNum = "2",enumExportField = "name")
    TakeRangeEnums takeRange;

    @ApiModelProperty("盘点范围信息")
    @Excel(name = "盘点范围", width = 30, orderNum = "3")
    String rangeInfoName;

    @ApiModelProperty("盘点范围信息")
    @ExcelIgnore
    List<Long> rangeInfo;

    @ApiModelProperty(value = "RFID编号", required = true)
    @Excel(name = "RFID编号", width = 30, orderNum = "4")
    String rfidCode;

    @ApiModelProperty(value = "资产编码", required = true)
    @Excel(name = "资产编号", width = 30, orderNum = "5")
    String code;

    @ApiModelProperty(value = "资产名称", required = true)
    @Excel(name = "资产名称", width = 30, orderNum = "6")
    String name;

    @ApiModelProperty("资产型号")
    @Excel(name = "资产型号", width = 30, orderNum = "7")
    String model;

    @ApiModelProperty("资产价格")
    @ExcelIgnore
    @JsonAmount
    Long price;

    @TableField(exist = false)
    @ApiModelProperty("资产小类")
    @ExcelIgnore
    String assetType;

    @ApiModelProperty("资产类型")
    @Excel(name = "资产类型", width = 25, orderNum = "8")
    String assetTypeName;

    @ApiModelProperty("使用状态")
    @DictItemBind(DictUtil.DICT_USE_STATE)
    @ExcelIgnore
    DictItemEntry useState;

    @ApiModelProperty("使用状态")
    @Excel(name = "使用状态", width = 20, orderNum = "9")
    String useStateStr;

    @JsonAmount
    @ApiModelProperty("资产价格")
    @Excel(name = "资产价格", width = 20, orderNum = "10")
    BigDecimal priceDecimal;

    @ApiModelProperty("责任人")
    @Excel(name = "责任人", width = 20, orderNum = "11")
    String managerName;

    @ApiModelProperty("责任部门")
    @Excel(name = "责任部门", width = 20, orderNum = "12")
    String managerDeptName;

    @ApiModelProperty("保管人")
    @Excel(name = "保管人", width = 20, orderNum = "13")
    String applyName;

    @ApiModelProperty("保管部门")
    @Excel(name = "保管部门", width = 20, orderNum = "14")
    String departmentName;

    @ApiModelProperty("资产位置")
    @Excel(name = "资产位置", width = 30, orderNum = "15")
    String location;

    @ExcelIgnore
    @ApiModelProperty("扫描状态")
    TakeScanStatus scanStatus;

    @ApiModelProperty("扫描状态")
    @Excel(name = "扫描状态", width = 20, orderNum = "16")
    String scanStatusStr;

    @ExcelIgnore
    @ApiModelProperty("盘点状态")
    TakeStatus takeStatus;

    @ApiModelProperty("盘点状态")
    @Excel(name = "盘点状态", width = 20, orderNum = "17")
    String takeStatusStr;

    @TableField("status")
    @ApiModelProperty("盘点结果状态")
    DiscrepancyStatus status;

    @ApiModelProperty("盘点结果")
    @Excel(name = "盘点结果", width = 20, orderNum = "18")
    String statusStr;

    @ExcelIgnore
    @ApiModelProperty("异常分析")
    String abnormalAnalyze;

    @ExcelIgnore
    @ApiModelProperty("处理类型")
    ProcessType processType;


    @Excel(name = "盘点时间", width = 30, format = "yyyy/MM/dd HH:mm:ss", orderNum = "19")
    @ApiModelProperty("盘点时间")
    LocalDateTime createdAt;

    @ApiModelProperty("处理类型")
    @Excel(name = "处理类型", width = 30, orderNum = "19")
    String processTypeStr;

    @Excel(name = "变更位置", width = 30, orderNum = "20")
    @ApiModelProperty("变更位置")
    String changeLocation;

    @Excel(name = "变更基站", width = 30, orderNum = "21")
    @ApiModelProperty("变更基站")
    String changeDeviceId;

    @Excel(name = "变更责任人", width = 30, orderNum = "22")
    @ApiModelProperty("变更责任人")
    String changeManagerName;

    @Excel(name = "变更保管部门", width = 30, orderNum = "23")
    @ApiModelProperty("变更保管部门")
    String changeManagerDept;

    @Excel(name = "变更保管人", width = 30, orderNum = "24")
    @ApiModelProperty("变更保管人")
    String changeApplyName;

    @ApiModelProperty("资产位置")
    @ExcelIgnore
    List<Long> locations;


    public String getUseStateStr() {
        return useState== null ? "" : useState.getLabel();
    }

    public String getTakeTypeStr() {
        return takeType == null ? "" : takeType.getName();
    }

    public String getTakeRangeStr() {
        return takeRange == null ? "" : takeRange.getName();
    }

    public String getTakeStatusStr() {
        return takeStatus == null ? "" : takeStatus.getName();
    }

    public String getScanStatusStr() {
        return scanStatus == null ? "" : scanStatus.getName();
    }

    public String getStatusStr() {
        return status== null ? "" : status.getName();
    }

    public BigDecimal getPriceDecimal() {
        return price == null ? null : new BigDecimal(price).divide(new BigDecimal(100));
    }
}
