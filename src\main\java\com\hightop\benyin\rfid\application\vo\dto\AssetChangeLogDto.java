package com.hightop.benyin.rfid.application.vo.dto;

import com.hightop.benyin.rfid.infrastructure.enums.AssetChangeSource;
import com.hightop.benyin.rfid.infrastructure.enums.AssetOperatType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.FieldDefaults;
import lombok.experimental.SuperBuilder;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Description: 资产领用DTO
 * @Author: X.S
 * @Date: 2023/12/22 17:16
 */
@FieldDefaults(level = AccessLevel.PRIVATE)
@SuperBuilder
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class AssetChangeLogDto {
    /**
     * 变更类型
     */
    AssetOperatType operatType;

    /**
     * 变更来源
     */
    AssetChangeSource source;

    /**
     * 变更单号
     */
    String changeCode;

    /**
     * 资产ID
     */
    Long assetId;

    /**
     * 变更前
     */
    String before;

    /**
     * 变更后
     */
    String after;

    /**
     * 变更人
     */
    Long createdBy;


}
