package com.hightop.benyin.rfid.api;

import com.hightop.benyin.rfid.application.service.RfidAssetChangeLogService;
import com.hightop.benyin.rfid.application.service.RfidAssetService;
import com.hightop.benyin.rfid.application.vo.dto.AssetReaderDto;
import com.hightop.benyin.rfid.application.vo.po.AssetTotalVo;
import com.hightop.benyin.rfid.application.vo.query.RfidAssetChangeLogQuery;
import com.hightop.benyin.rfid.application.vo.query.RfidAssetQuery;
import com.hightop.benyin.rfid.infrastructure.entity.RfidAsset;
import com.hightop.benyin.rfid.infrastructure.entity.RfidAssetChangeLog;
import com.hightop.benyin.share.infrastructure.util.DictUtil;
import com.hightop.benyin.system.api.vo.UserManageExtendVo;
import com.hightop.benyin.system.api.vo.query.UserExtendPageQuery;
import com.hightop.benyin.system.domain.service.UserInfoDomainService;
import com.hightop.fario.base.web.Operation;
import com.hightop.fario.base.web.RestResponse;
import com.hightop.fario.common.mybatis.DataGrid;
import com.hightop.magina.casual.session.ApplicationSessions;
import com.hightop.magina.standard.behavior.operation.IgnoreOperationLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * 资产管理rest接口
 *
 * <AUTHOR>
 * @date 2024-10-12 16:36:01
 */
@RequestMapping("/asset")
@RestController
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Api(tags = "资产管理")
public class RfidAssetController {
    RfidAssetService rfidassetService;
    RfidAssetChangeLogService rfidassetChangeLogService;
    UserInfoDomainService userInfoDomainService;

    @PostMapping("/page")
    @ApiOperation("所有资产分页查询")
    @IgnoreOperationLog
    public RestResponse<DataGrid<RfidAsset>> page(@RequestBody RfidAssetQuery pageQuery) {
        pageQuery.setHasCondition(rfidassetService.hasCondition(pageQuery));
        return RestResponse.ok(this.rfidassetService.page(pageQuery));
    }

    @PostMapping("/my")
    @ApiOperation("我负责的资产分页查询")
    @IgnoreOperationLog
    public RestResponse<DataGrid<RfidAsset>> my(@RequestBody RfidAssetQuery pageQuery) {
        pageQuery.setHasCondition(rfidassetService.hasCondition(pageQuery));
        String role = userInfoDomainService.getCurrRoleCode();
        //负责人查看自己负责的部门
        if(!ApplicationSessions.code().equals(DictUtil.ADMIN)){
            if (DictUtil.LEADER_ROLE.equals(role)) {
                List<Long> departmentInfos = userInfoDomainService.getCurrDeptIds();
                pageQuery.setDeptIds(departmentInfos);
            }
            //员工查看自己部门
            if (DictUtil.STAFF_ROLE.equals(role)) {
                pageQuery.setUserId(ApplicationSessions.id());
            }
        }

        return RestResponse.ok(this.rfidassetService.page(pageQuery));
    }

    @PostMapping("/getManagerList")
    @ApiOperation("获取资产责任人列表")
    @IgnoreOperationLog
    public RestResponse<List<UserManageExtendVo>> getManagerList(@RequestBody UserExtendPageQuery pageQuery) {
        return RestResponse.ok(this.rfidassetService.getManagerList(pageQuery));
    }

    @PostMapping("/getCreatorList")
    @ApiOperation("获取资产登记人列表")
    @IgnoreOperationLog
    public RestResponse<List<UserManageExtendVo>> getCreatorList(@RequestBody UserExtendPageQuery pageQuery) {
        return RestResponse.ok(this.rfidassetService.getCreatorList(pageQuery));
    }

    @PostMapping("/log")
    @ApiOperation("资产变更记录分页查询")
    @IgnoreOperationLog
    public RestResponse<DataGrid<RfidAssetChangeLog>> pageLog(@RequestBody RfidAssetChangeLogQuery pageQuery) {
        return RestResponse.ok(this.rfidassetChangeLogService.pageList(pageQuery));
    }

    @GetMapping("/{id}")
    @ApiOperation("明细查询")
    public RestResponse<RfidAsset> getById(@PathVariable("id") Long id) {
        return RestResponse.ok(this.rfidassetService.getById(id));
    }

    @PostMapping("/stash")
    @ApiOperation("登记暂存")
    public RestResponse<String> stash(@Validated @RequestBody RfidAsset rfidasset) {
        return RestResponse.ok(this.rfidassetService.stash(rfidasset));
    }

    @PostMapping("/update")
    @ApiOperation("资产编码")
    public RestResponse<Void> update(@Validated @RequestBody RfidAsset rfidasset) {
        return Operation.UPDATE.response(this.rfidassetService.update(rfidasset));
    }

    @PostMapping("/bind/{bindCode}")
    @ApiOperation("绑定资产")
    public RestResponse<Void> bind(@PathVariable @ApiParam("bindCode") String bindCode,@Validated @RequestBody RfidAsset rfidasset) {
        return Operation.ADD.response(this.rfidassetService.bind(rfidasset,bindCode));
    }

    @DeleteMapping("/{id}")
    @ApiOperation("删除")
    public RestResponse<Void> delete(@PathVariable @ApiParam("id") Long id) {
        return Operation.DELETE.response(this.rfidassetService.delete(id));
    }

    @DeleteMapping("remove/{id}")
    @ApiOperation("领用移除")
    public RestResponse<Void> remove(@PathVariable @ApiParam("id") Long id) {
        return Operation.DELETE.response(this.rfidassetService.remove(id));
    }

    @PostMapping("addReader")
    @ApiOperation("基站添加资产")
    public RestResponse<Void> addReader(@Validated @RequestBody AssetReaderDto assetReaderDto) {
        return Operation.UPDATE.response(this.rfidassetService.addReader(assetReaderDto));
    }


    @PostMapping("/listTotal")
    @ApiOperation("统计列表汇总查询")
    @IgnoreOperationLog
    public RestResponse<AssetTotalVo> listTotal(@RequestBody RfidAssetQuery pageQuery) {
        return RestResponse.ok(this.rfidassetService.getListTotal(pageQuery));
    }


    @PostMapping("/total")
    @ApiOperation("统计数量查询")
    @IgnoreOperationLog
    public RestResponse<AssetTotalVo> total(@RequestBody RfidAssetQuery pageQuery) {
        return RestResponse.ok(this.rfidassetService.getTotal(pageQuery));
    }

    @DeleteMapping("clear")
    @ApiOperation("清理资产数据")
    public RestResponse<Void> delete() {
        return Operation.UPDATE.response(this.rfidassetService.clearAsset());
    }


    /**
     * 导出当前资产数据
     *
     * @param response
     * @param pageQuery
     * @return
     */
    @ApiOperation("/导出当前资产数据")
    @PostMapping("/export")
    public RestResponse<Void> downCurrData(HttpServletResponse response, @RequestBody RfidAssetQuery pageQuery) throws IOException {
        Boolean b = rfidassetService.downloadAllData(response, pageQuery);
        if (!b) {
            return new RestResponse(500, "导出失败，请联系技术人员！", null, null);
        }
        return RestResponse.message("导出成功");
    }


    /**
     * 下载模板
     *
     * @param response
     * @return
     */
    @ApiOperation("下载资产模板")
    @GetMapping("/downTemplate")
    public RestResponse<Void> downTemplate(HttpServletResponse response) {
        Boolean b = rfidassetService.downTemplate(response);
        if (!b) {
            return new RestResponse(500, "下载失败", null, null);
        }
        return RestResponse.message("下载成功");
    }

    /**
     * 导入资产数据
     *
     * @param file
     * @return
     * @throws Exception
     */
    @PostMapping("/import")
    @ApiOperation(value = "导入资产数据")
    public RestResponse<Void> impor(@RequestParam("file") MultipartFile file) throws Exception {
        boolean b = this.rfidassetService.importData(file, false);
        if (!b) {
            return new RestResponse(500, "解析失败", null, null);
        }
        return RestResponse.message("导入成功");
    }

    /**
     * 导入资产数据
     *
     * @param file
     * @return
     * @throws Exception
     */
    @PostMapping("/importUpdate")
    @ApiOperation(value = "导入资产数据")
    public RestResponse<Void> importUpdate(@RequestParam("file") MultipartFile file) throws Exception {
        boolean b = this.rfidassetService.importData(file, false);
        if (!b) {
            return new RestResponse(500, "解析失败", null, null);
        }
        return RestResponse.message("导入成功");
    }

}
