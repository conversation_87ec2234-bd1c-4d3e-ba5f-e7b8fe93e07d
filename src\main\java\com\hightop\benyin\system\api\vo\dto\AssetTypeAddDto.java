package com.hightop.benyin.system.api.vo.dto;

import com.hightop.benyin.system.infrastructure.entity.AssetType;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 资产类型新增dto
 *
 * <AUTHOR>
 * @date 2022/10/09 15:48
 * @since 1.0.0
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AssetTypeAddDto {
    @ApiModelProperty("父id")
    Long parentId;
    @ApiModelProperty("名称")
    @NotBlank(message = "名称不能为空")
    @Size(min = 1, max = 64, message = "名称长度在{min}至{max}之间")
    String name;
    @ApiModelProperty("编码")
    String code;

    @ApiModelProperty("是否贴标签")
    Boolean hasTag;

    @ApiModelProperty("是否扫描")
    Boolean isScan;

    @ApiModelProperty("是否上报异常")
    Boolean isReport;

    @ApiModelProperty("是否盘点")
    Boolean isTake;

    @ApiModelProperty("是否盘点统计")
    Boolean isTakeStatis;

    @ApiModelProperty("是否财务统计")
    Boolean isStatis;

    @ApiModelProperty("排序号")
    @NotNull(message = "排序号不能为空")
    Integer sort;
    @ApiModelProperty("负责人id")
    Long managerId;


    public AssetType toAssetType() {
        return
                new AssetType().setParentId(this.parentId)
                        .setName(this.name)
                        .setCode(this.code)
                        .setIsStatis(this.isStatis)
                        .setIsTakeStatis(this.isTakeStatis)
                        .setIsScan(this.isScan)
                        .setIsReport(this.isReport)
                        .setIsTake(this.isTake)
                        .setHasTag(this.hasTag)
                        .setSort(this.sort);
    }
}
