package com.hightop.benyin.rfid.infrastructure.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelIgnore;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.hightop.benyin.rfid.infrastructure.enums.DiscrepancyStatus;
import com.hightop.benyin.rfid.infrastructure.enums.ProcessType;
import com.hightop.benyin.rfid.infrastructure.enums.TakeScanStatus;
import com.hightop.benyin.rfid.infrastructure.enums.TakeStatus;
import com.hightop.benyin.share.infrastructure.util.DictUtil;
import com.hightop.magina.standard.code.dictionary.bind.DictItemBind;
import com.hightop.magina.standard.code.dictionary.bind.DictItemEntry;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 资产盘点明细
 *
 * <AUTHOR>
 * @date 2024-09-15 14:53:13
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@TableName(value = "b_rfid_asset_take_detail",autoResultMap = true)
@ApiModel
public class RfidAssetTakeDetail {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @ApiModelProperty("id")
    Long id;

    @TableField("take_code")
    @ApiModelProperty("盘点编号")
    String takeCode;

    @TableField("take_reader_id")
    @ApiModelProperty("盘点进度id")
    Long takeReaderId;

    @TableField("reader_id")
    @ApiModelProperty("基站id")
    Long readerId;

    @TableField("device_id")
    @ApiModelProperty("已绑设备id")
    String deviceId;

    @TableField("rfid_code")
    @ApiModelProperty("rfid编码")
    String rfidCode;

    @TableField("asset_id")
    @ApiModelProperty("资产id")
    Long assetId;

    @TableField("is_take")
    @ApiModelProperty("是否盘点")
    @ExcelIgnore
    Boolean isTake;


    @TableField("has_tag")
    @ApiModelProperty("是否帖标签")
    @ExcelIgnore
    Boolean hasTag;

    @TableField("is_take_statis")
    @ApiModelProperty("盘点是否统计")
    @ExcelIgnore
    Boolean isTakeStatis;


    @TableField("manager_id")
    @ApiModelProperty(value = "责任人", required = true)
    @ExcelIgnore
    Long managerId;

    @TableField("manager_name")
    @ApiModelProperty(value = "责任人", required = true)
    @Excel(name = "责任部门", width = 30, orderNum = "12")
    String managerName;

    @TableField("manager_dept_id")
    @ApiModelProperty(value = "责任部门", required = true)
    @ExcelIgnore
    Long managerDeptId;

    @TableField("manager_dept_name")
    @Excel(name = "责任部门", width = 30, orderNum = "11")
    @ApiModelProperty("责任部门")
    String managerDeptName;

    @TableField(value = "apply_id", updateStrategy = FieldStrategy.IGNORED)
    @ApiModelProperty(value = "归属人id", required = true)
    @ExcelIgnore
    Long applyId;

    @TableField(value = "apply_name", updateStrategy = FieldStrategy.IGNORED)
    @ApiModelProperty("领用人姓名")
    @Excel(name = "领用人", width = 30, orderNum = "16")
    String applyName;

    @TableField("department_id")
    @ApiModelProperty(value = "领用部门", required = true)
    @ExcelIgnore
    Long departmentId;

    @TableField("department_name")
    @Excel(name = "领用部门", width = 30, orderNum = "15")
    @ApiModelProperty("领用部门")
    String departmentName;

    @TableField(value = "location_id", updateStrategy = FieldStrategy.IGNORED)
    @ApiModelProperty("所在位置")
    Long locationId;

    @TableField("location")
    @ApiModelProperty("资产位置")
    @Excel(name = "资产位置", width = 60, orderNum = "14")
    String location;

    @TableField("remark")
    @ApiModelProperty("备注")
    @Excel(name = "备注", width = 60, orderNum = "14")
    String remark;

    @TableField("scan_status")
    @ApiModelProperty("扫描状态")
    TakeScanStatus scanStatus;

    @TableField("take_status")
    @ApiModelProperty("盘点状态")
    TakeStatus takeStatus;

    @TableField("status")
    @ApiModelProperty("盘点结果状态")
    DiscrepancyStatus status;

    @TableField("abnormal_analyze")
    @ApiModelProperty("异常分析")
    String abnormalAnalyze;

    @TableField("process_type")
    @ApiModelProperty("处理类型")
    ProcessType processType;

    @TableField("scan_type")
    @ApiModelProperty("扫描类型0盘点扫描1搜索扫描")
    Integer scanType;


    @TableField("change_location")
    @ApiModelProperty("变更位置")
    Long changeLocation;

    @TableField("change_reader")
    @ApiModelProperty("变更基站")
    Long changeReader;

    @TableField("change_manager_id")
    @ApiModelProperty("变更负责人")
    Long changeManagerId;

    @TableField("change_manager_dept_id")
    @ApiModelProperty("变更责任部门")
    Long changeManagerDeptId;

    @TableField("change_apply_id")
    @ApiModelProperty("变更领用人")
    Long changeApplyId;

    @TableField("change_apply_dept_id")
    @ApiModelProperty("变更领用部门")
    Long changeApplyDeptId;

    @TableField(exist = false)
    @ApiModelProperty("变更位置")
    String changeLocationName;

    @TableField(exist = false)
    @ApiModelProperty("变更基站设备")
    String changeDeviceId;

    @TableField(value = "scan_location_ids", typeHandler = JacksonTypeHandler.class )
    @ApiModelProperty("实际扫描位置")
    List<Long> scanLocationIds;

    @TableField(value = "scan_reader_ids", typeHandler = JacksonTypeHandler.class)
    @ApiModelProperty("实际扫描基站")
    List<Long> scanReaderIds;

    @TableField("scan_location")
    @ApiModelProperty("实际扫描位置")
    String scanLocation;

    @TableField("scan_reader_device")
    @ApiModelProperty("实际扫描设备")
    String scanReaderDevice;

    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty("更新时间")
    @Excel(name = "最近更新人", width = 30, orderNum = "22", format = "yyyy/MM/dd HH:mm:ss")
    LocalDateTime updatedAt;

    @TableField(value = "updated_by")
    @ApiModelProperty("更新人")
    @ExcelIgnore
    Long updatedBy;

    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @ApiModelProperty("创建时间")
    LocalDateTime createdAt;

    @TableField("deleted")
    @ApiModelProperty("是否删除  0未删除  1删除")
    @TableLogic
    Integer deleted;

    @TableField(exist = false)
    @ApiModelProperty("单价（发起盘点时用）")
    Long price;

    @TableField(exist = false)
    @ApiModelProperty(value = "资产编码", required = true)
    @Excel(name = "资产编号", width = 30, orderNum = "3")
    String code;

    @TableField(exist = false)
    @ApiModelProperty(value = "资产名称", required = true)
    @Excel(name = "资产名称", width = 30, orderNum = "4")
    String name;

    @TableField(exist = false)
    @ApiModelProperty("资产型号")
    @Excel(name = "资产型号", width = 30, orderNum = "5")
    String model;


    @TableField(exist = false)
    @ApiModelProperty("资产型号")
    @Excel(name = "盘点部门", width = 30, orderNum = "5")
    String createdDeptName;

    @TableField(exist = false)
    @ApiModelProperty("盘点人")
    @Excel(name = "盘点人", width = 30, orderNum = "5")
    String createdName;

    @TableField(exist = false)
    @ApiModelProperty("资产小类")
    @ExcelIgnore
    String assetType;


    @TableField(exist = false)
    @ApiModelProperty("资产小类")
    @ExcelIgnore
    String assetTypeName;

    @TableField(exist = false)
    @ApiModelProperty("使用状态")
    @DictItemBind(DictUtil.DICT_USE_STATE)
    @ExcelIgnore
    DictItemEntry useState;


}
