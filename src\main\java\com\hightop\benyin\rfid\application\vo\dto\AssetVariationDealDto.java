package com.hightop.benyin.rfid.application.vo.dto;

import com.hightop.benyin.rfid.infrastructure.enums.ProcessType;
import com.hightop.benyin.rfid.infrastructure.enums.VariationStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Description: 资产领用DTO
 * @Author: X.S
 * @Date: 2023/12/22 17:16
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@ApiModel("资产异动处理DTO")
public class AssetVariationDealDto {

    @ApiModelProperty("异动信息")
    @NotNull(message = "请选择要处理的异动信息")
    Long id;

    @ApiModelProperty("处理方式")
    @NotNull(message = "请选择处理方式")
    ProcessType processType;

    @ApiModelProperty("变更位置")
    Long changeLocationId;

    @ApiModelProperty("变更基站")
    Long changeReaderId;

    @ApiModelProperty("变更保管人")
    Long changeApplyId;

    @ApiModelProperty("变更保管人部门")
    Long changeApplyDeptId;



}
