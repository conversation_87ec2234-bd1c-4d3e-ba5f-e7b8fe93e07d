package com.hightop.benyin.share.infrastructure.restful.tencent.sts;

import com.hightop.fario.base.constant.DateTimeConstants;
import com.hightop.fario.base.constant.StringConstants;
import com.hightop.fario.common.crypto.digest.HmacDigest;
import com.hightop.fario.common.crypto.digest.ShaDigest;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static com.hightop.fario.base.constant.StringConstants.*;
import static com.hightop.fario.common.crypto.digest.ShaDigest.Algorithm.SHA256;
import static org.springframework.http.HttpHeaders.*;
import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;

/**
 * <a href="https://cloud.tencent.com/document/product/213/30654">签名方法3</a>签名拦截
 * @Author: X.S
 * @date 2023/10/25 15:29
 */
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class StsInterceptor implements RequestInterceptor {
    /**
     * 服务名称
     */
    String service;
    /**
     * 请求api版本
     */
    String version;
    /**
     * 接口名称
     */
    String action;
    /**
     * 地区
     */
    String region;
    /**
     * 密钥id
     */
    String secretId;
    /**
     * 密钥
     */
    String secretKey;
    /**
     * 请求主机地址
     */
    String host;
    /**
     * 固定结尾
     */
    private static final String ENDING = "tc3_request";
    /**
     * 算法名称
     */
    private static final String ALGORITHM = "TC3-HMAC-SHA256";

    @Override
    public void apply(RequestTemplate it) {
        ZonedDateTime now = LocalDateTime.now().atZone(ZoneId.systemDefault()).withZoneSameInstant(ZoneOffset.UTC);
        String timestamp = String.valueOf(now.toEpochSecond()), date = now.format(DateTimeConstants.DTF_YYYY_MM_DD);

        // http请求相关hash
        List<String> canonical = new ArrayList<>();
        // 只对host、content-type签名 需要按照字典升序排序
        String signedHeaders = CONTENT_TYPE.toLowerCase() + SEMICOLON + HOST.toLowerCase();
        canonical.add(it.method().toUpperCase());
        canonical.add(it.path());
        // 查询参数
        canonical.add(
            Optional.of(it.queryLine())
                .filter(q -> !q.isEmpty())
                // ?之后的查询参数
                .map(q -> q.substring(1))
                .orElse(StringConstants.EMPTY)
        );
        // host
        canonical.add(String.format("%s:%s", CONTENT_TYPE.toLowerCase(), APPLICATION_JSON_VALUE.toLowerCase()));
        // content-type
        canonical.add(String.format("%s:%s", HOST.toLowerCase(), this.host.toLowerCase()));
        // 追加换行
        canonical.add(StringConstants.EMPTY);
        canonical.add(signedHeaders);
        canonical.add(ShaDigest.of(SHA256).update(Optional.ofNullable(it.body()).orElse(new byte[0])).hexLower());

        String scope = String.format("%s/%s/%s", date, this.service, ENDING);
        // 待签名字符串
        List<String> toBeSigned = Arrays.asList(
            ALGORITHM,
            timestamp,
            scope,
            ShaDigest.of(SHA256).update(String.join(BR, canonical)).hexLower()
        );

        // 初始化密钥拼接
        String secret = String.format("TC3%s", this.secretKey);
        byte[] secretDate = HmacDigest.of(HmacDigest.Algorithm.SHA256, secret).update(date).bytes();
        byte[] secretService = HmacDigest.of(HmacDigest.Algorithm.SHA256, secretDate).update(this.service).bytes();
        byte[] secretSigning = HmacDigest.of(HmacDigest.Algorithm.SHA256, secretService).update(ENDING).bytes();

        // 授权头拼接字符串使用逗号、空格分隔
        List<String> authorization = new ArrayList<>();
        authorization.add(String.format("Credential=%s/%s", this.secretId, scope));
        authorization.add(String.format("SignedHeaders=%s", signedHeaders));
        authorization.add(
            String.format(
                "Signature=%s",
                HmacDigest.of(HmacDigest.Algorithm.SHA256, secretSigning)
                    .update(String.join(BR, toBeSigned))
                    .hexLower()
            )
        );

        // 请求头
        it.header("X-TC-Action", this.action)
            .header("X-TC-Region", this.region)
            .header("X-TC-Timestamp", timestamp)
            .header("X-TC-Version", this.version)
            .header(HOST, this.host)
            .header(CONTENT_TYPE, APPLICATION_JSON_VALUE)
            // 签名
            .header(AUTHORIZATION, String.format("%s %s", ALGORITHM, String.join(COMMA + SPACE, authorization)));
    }
}
