package com.hightop.benyin.rfid.infrastructure.mapper;

import com.github.yulichang.base.MPJBaseMapper;
import com.hightop.benyin.rfid.infrastructure.entity.RfidAssetTakeReader;
import org.apache.ibatis.annotations.Delete;

/**
 * 资产盘点明细管理mapper
 *
 * <AUTHOR>
 * @date 2024-09-15 14:53:13
 */
public interface RfidAssetTakeReaderMapper extends MPJBaseMapper<RfidAssetTakeReader> {

    @Delete("TRUNCATE TABLE b_rfid_asset_take_reader")
    void clear();
}
