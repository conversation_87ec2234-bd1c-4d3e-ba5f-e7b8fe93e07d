<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hightop.benyin.rfid.infrastructure.mapper.RfidAssetTransferMapper">

    <select id="pageList" resultType="com.hightop.benyin.rfid.infrastructure.entity.RfidAssetTransfer">
        select t.*,t2.full_name location,t3.full_name location newLocation,
             (select count(1) from b_rfid_asset_transfer_detail where transfer_code=t.transfer_code) num
               ,t4.name departmentName,t5.name newDepartmentName
        from  b_rfid_asset_transfer t
        left join st_location t2 on t2.id = t.location_id and t.transfer_type='CHANGE'
        left join st_location t3 on t3.id = t.new_location_id and t.transfer_type='CHANGE'
        left join st_department t4 on t4.id = t.department_id
        left join st_department t5 on t5.id = t.new_department_id
        <where>
            <if test="qo.transferCode!= null and qo.transferCode!= ''">
                and t.transfer_code like  concat ('%',#{qo.transferCode},'%')
            </if>
            <if test="qo.applyName!= null and qo.applyName!= ''">
                and t1.apply_name like  concat ('%',#{qo.applyName},'%')
            </if>
            <if test="qo.newApplyName!= null and qo.newApplyName!= ''">
                and t1.new_apply_name like  concat ('%',#{qo.newApplyName},'%')
            </if>

            <if test="null != qo.startDate and '' != qo.startDate ">
                and t.created_at &gt;= concat(#{qo.startDate},' 00:00:00')
            </if>
            <if test="null != qo.endDate and '' != qo.endDate ">
                and t.created_at &lt;= concat(#{qo.endDate},' 23:59:59')
            </if>

            <if test="qo.transferType!= null and qo.transferType!= ''">
                and t.transfer_type =  #{qo.transferType}
            </if>

            <if test="qo.status!= null and qo.status!= ''">
                and t.status =#{qo.status}
            </if>
            <if test="null != qo.startAuditDate and '' != qo.startAuditDate ">
                and t.audit_at &gt;= concat(#{qo.startAuditDate},' 00:00:00')
            </if>
            <if test="null != qo.endAuditDate and '' != qo.endAuditDate ">
                and t.audit_at &lt;= concat(#{qo.endAuditDate},' 23:59:59')
            </if>
            <if test="null != qo.startTransferDate and '' != qo.startTransferDate ">
                and t.transfer_date &gt;= #{qo.startTransferDate}
            </if>
            <if test="null != qo.endTransferDate and '' != qo.endTransferDate ">
                and t.transfer_date &lt;= #{qo.endTransferDate}
            </if>

            <if test="null!=qo.departmentIds and !qo.departmentIds.isEmpty()">
                AND t.department_id
                <foreach collection="qo.departmentIds" item="id" separator=","  open="(" close=")">
                    #{id}
                </foreach>
            </if>

            <if test="null!=qo.newDepartmentIds and !qo.newDepartmentIds.isEmpty()">
                AND t.new_department_id in
                <foreach collection="qo.newDepartmentIds" item="id" separator=","  open="(" close=")">
                    #{id}
                </foreach>
            </if>

            <if test="null!=qo.locations and !qo.locations.isEmpty()">
                AND t.location_id in
                <foreach collection="qo.locations" item="id" separator="," open="(" close=")">
                    #{id}
                </foreach>
            </if>

            <if test="null!=qo.newLocations and !qo.newLocations.isEmpty()">
                AND t.new_location_id in
                <foreach collection="qo.newLocations" item="id" separator="," open="(" close=")">
                    #{id}
                </foreach>
            </if>

        </where>
        order by t.created_at desc
    </select>

</mapper>