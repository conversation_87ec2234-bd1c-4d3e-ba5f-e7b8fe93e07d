package com.hightop.benyin.share.socket.event;

import com.hightop.benyin.share.socket.enums.CommandType;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import org.springframework.context.ApplicationEvent;

/**
 * 消息接收处理事件
 *
 * @Author: X.S
 * @date 2024/09/16 14:55
 */
@FieldDefaults(level = AccessLevel.PRIVATE)
@Getter
@Setter
public class CommandReceiveEvent extends ApplicationEvent {

    public CommandReceiveEvent(Object source, String clientName, String deviceId,CommandType commandType, Integer length, String data) {
        super(source);
        this.clientName = clientName;
        this.deviceId = deviceId;
        this.commandType = commandType;
        this.length = length;
        this.data = data;
    }

    /**
     * 消息类型
     */
    CommandType commandType;

    /**
     * ip地址
     */
    String clientName;

    /**
     * 设备id
     */
    String deviceId;

    /**
     * ip地址
     */
    Integer length;

    /**
     * 消息
     */
    String data;

}
