package com.hightop.benyin.rfid.infrastructure.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelIgnore;
import com.baomidou.mybatisplus.annotation.*;
import com.hightop.benyin.rfid.infrastructure.enums.AssetFlowStatus;
import com.hightop.benyin.rfid.infrastructure.enums.AssetBusinessType;
import com.hightop.fario.common.jackson.annotation.JsonAmount;
import com.hightop.magina.standard.ums.user.bind.UserBind;
import com.hightop.magina.standard.ums.user.bind.UserEntry;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.time.LocalDateTime;
import java.util.List;

/**
 * rfid资产流水信息
 *
 * <AUTHOR>
 * @date 2024-10-12 16:36:01
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@TableName("b_rfid_asset_flow")
@ApiModel
public class RfidAssetFlow {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @ApiModelProperty("id")
    Long id;

    @TableField("code")
    @ApiModelProperty("业务单号")
    @Excel(name = "业务单号", width = 22, orderNum = "1")
    String code;


    @TableField("operate_type")
    @ApiModelProperty("业务类型")
    @Excel(name = "业务类型", width = 22, orderNum = "2",enumExportField = "name")
    AssetBusinessType operateType;


    @TableField("status")
    @ApiModelProperty("业务状态")
    @Excel(name = "业务状态", width = 22, orderNum = "3",enumExportField = "name")
    AssetFlowStatus status;

    @TableField("number")
    @ApiModelProperty("申请数量")
    Integer number;

    @TableField("amount")
    @ApiModelProperty("申请金额")
    @JsonAmount
    Long amount;

    @TableField("remark")
    @Excel(name = "备注", width = 30, orderNum = "10")
    @ApiModelProperty("备注")
    String remark;

    @TableField("external_unit")
    @ApiModelProperty("外部单位")
    String externalUnit;


    @TableField("external_dept")
    @ApiModelProperty("外部部门")
    String externalDept;


    @TableField("external_person")
    @ApiModelProperty("外部人员")
    String externalPerson;


    @TableField("department_id")
    @ApiModelProperty(value = "登记/领用部门")
    Long departmentId;

    @TableField("department_name")
    @ApiModelProperty(value = "登记/领用部门")
    String departmentName;

    @TableField("apply_id")
    @ApiModelProperty(value = "归属人id", required = true)
    Long applyId;

    @TableField("apply_name")
    @ApiModelProperty("申请人姓名")
    @Excel(name = "申请人姓名", width = 30, orderNum = "8")
    String applyName;

    @TableField("apply_at")
    @ApiModelProperty("申请时间")
    LocalDateTime applyAt;


    @TableField("reader_id")
//    @Excel(name = "基站编码", width = 30, orderNum = "7")
    @ApiModelProperty("基站编码")
    Long readerId;

    @TableField("location_id")
//    @Excel(name = "位置编码", width = 30, orderNum = "7")
    @ApiModelProperty("位置编码")
    Long locationId;

    @TableField("device_id")
    @Excel(name = "基站", width = 30, orderNum = "7")
    @ApiModelProperty("基站")
    String deviceId;

    @TableField("location")
    @Excel(name = "位置", width = 30, orderNum = "7")
    @ApiModelProperty("位置")
    String location;


    @TableField("ori_dept_id")
    @ApiModelProperty(value = "调拨来源部门")
    Long oriDeptId;

    @TableField("ori_dept_name")
    @ApiModelProperty(value = "调拨来源部门")
    String oriDeptName;

    @TableField("ori_apply_id")
    @ApiModelProperty(value = "调拨来源人")
    Long oriApplyId;

    @TableField("ori_apply_name")
    @ApiModelProperty("调拨来源人")
    String oriApplyName;



    @TableField("change_location_id")
    @ApiModelProperty("变更位置")
    Long changeLocationId;

    @TableField("change_location")
    @ApiModelProperty("变更位置")
    String changeLocation;

    @TableField("change_reader_id")
    @ApiModelProperty("变更基站")
    Long changeReaderId;

    @TableField("change_reader")
    @ApiModelProperty("变更基站")
    String changeReader;


    @TableField("approve_id")
    @ApiModelProperty("审核人id")
    Long approveId;

    @TableField("approve_name")
    @ApiModelProperty("审核人姓名")
    String approveName;


    @TableField(value = "approve_at", fill = FieldFill.INSERT)
    @ApiModelProperty("审核时间")
    @Excel(name = "操作时间", width = 30, orderNum = "10", format = "yyyy/MM/dd HH:mm:ss")
    LocalDateTime approveAt;

    @TableField("created_by")
    @ApiModelProperty("创建人")
    @UserBind
    UserEntry createdBy;

    @TableField(value = "created_dept")
    @ApiModelProperty("创建人部门")
    @ExcelIgnore
    Long createDept;

    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @ApiModelProperty("创建时间")
    @Excel(name = "操作时间", width = 30, orderNum = "10", format = "yyyy/MM/dd HH:mm:ss")
    LocalDateTime createdAt;

    @TableField(exist = false)
    @ApiModelProperty("操作人")
    @Excel(name = "操作时间", width = 30, orderNum = "11")
    String createdByName;

    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty("更新时间")
    LocalDateTime updatedAt;

    @TableField(value = "updated_by")
    @ApiModelProperty("更新人")
    Long updatedBy;

    @TableField("deleted")
    @ApiModelProperty("是否删除  0未删除  1删除")
    @TableLogic
    Integer deleted;

    @TableField(exist = false)
    @ApiModelProperty("资产登记列表")
    List<RfidAsset> rfidAssetList;

    @TableField(exist = false)
    @ApiModelProperty("登记部门名称")
   String createDeptName;

    @TableField(exist = false)
    @ApiModelProperty("申请领用时间")
    @Excel(name = "操作时间", width = 30, orderNum = "10", format = "yyyy/MM/dd HH:mm:ss")
    LocalDateTime applyAddTime;


    @TableField(exist = false)
    @ApiModelProperty("总数量")
    Integer totalNum;


    @TableField(exist = false)
    @ApiModelProperty("应绑定数量")
    Long needBindNum;


    public String getCreatedByName() {
        return createdBy.getName();
    }
}
