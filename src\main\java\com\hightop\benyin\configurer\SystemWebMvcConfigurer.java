package com.hightop.benyin.configurer;

import com.hightop.fario.base.constant.ProfileConstants;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.http.HttpMethod;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.Arrays;

/**
 * @Description: mvc配置
 * @Author: X.S
 * @Date: 2023/11/7 17:34
 */
@Configuration(proxyBeanMethods = false)
@Profile(ProfileConstants.NOT_PRODUCTION)
public class SystemWebMvcConfigurer implements WebMvcConfigurer {
    /**
     * 跨域处理
     * @param registry
     * @return:
     * @Author: X.S
     * @Date: 2023/11/7 17:36
     */
    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
            .allowedOriginPatterns("*")
            .allowCredentials(true)
            .allowedMethods(Arrays.stream(HttpMethod.values()).map(HttpMethod::name).toArray(String[]::new));
    }
}
