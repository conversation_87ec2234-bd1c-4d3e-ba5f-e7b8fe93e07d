package com.hightop.benyin.rfid.application.vo.po;

import com.hightop.benyin.rfid.infrastructure.entity.RfidAsset;
import com.hightop.fario.common.jackson.annotation.JsonAmount;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.util.List;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@ApiModel("资产DTO")
public class AssetBaseVo {

    @ApiModelProperty("id")
    Long id;

    @ApiModelProperty(value = "资产编码", required = true)
    String code;

    @ApiModelProperty(value = "资产名称", required = true)
    String name;

    @ApiModelProperty(value = "rfid编码", required = true)
    String rfidCode;

    @ApiModelProperty("原rfid编码")
    String oriRfidCode;

    @ApiModelProperty("资产型号")
    String model;

    @JsonAmount
    @ApiModelProperty("价格")
    Long price;

    @ApiModelProperty("资产位置id")
    String location;

    @ApiModelProperty("资产位置")
    Long locationId;

    @ApiModelProperty("所属基站")
    Long readerId;

    @ApiModelProperty("基站设备id")
    String deviceId;

    @ApiModelProperty("资产保管部门")
    Long departmentId;

    @ApiModelProperty("所属单位")
    String departmentName;

    @ApiModelProperty("领用人姓名")
    Long applyId;

    @ApiModelProperty("领用人姓名")
    String applyName;

    @ApiModelProperty(value = "责任部门", required = true)
    Long managerDeptId;

    @ApiModelProperty("责任部门")
    String managerDeptName;

    @ApiModelProperty(value = "责任人", required = true)
    Long managerId;

    @ApiModelProperty(value = "责任人", required = true)
    String managerName;

    @ApiModelProperty("储藏位置(选填)")
    String storageLocation;

    @ApiModelProperty("基站编码")
    String readerCode;

    @ApiModelProperty("资产类型")
    Integer type;

    @ApiModelProperty("上级资产编码(选填)")
    String parentCode;

    @ApiModelProperty("下级资产")
    List<RfidAsset> children;
}
