package com.hightop.benyin.system.api.controller;

import com.hightop.benyin.share.infrastructure.util.DownloadResponseUtil;
import com.hightop.benyin.system.api.vo.AssetTypeTreeVo;
import com.hightop.benyin.system.api.vo.dto.AssetTypeAddDto;
import com.hightop.benyin.system.api.vo.dto.AssetTypeUpdateDto;
import com.hightop.benyin.system.api.vo.query.AssetTypeQuery;
import com.hightop.benyin.system.application.service.AssetTypeService;
import com.hightop.benyin.system.infrastructure.entity.AssetType;
import com.hightop.fario.base.web.Operation;
import com.hightop.fario.base.web.RestResponse;
import com.hightop.magina.standard.behavior.operation.IgnoreOperationLog;
import com.hightop.magina.standard.behavior.operation.OperationLogType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * 资产类型管理rest接口
 *
 * <AUTHOR>
 * @date 2022/09/13 21:25
 * @since 1.0.0
 */
@RestController
@RequestMapping("/magina/manage/assetType")
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Api(tags = "资产类型管理")
@OperationLogType
public class AssetTypeController {
    AssetTypeService assetTypeService;

    @GetMapping
    @ApiOperation("资产类型树查询")
    public List<AssetTypeTreeVo> tree(AssetTypeQuery assetTypeQuery) {
        return this.assetTypeService.getAssetTypeTree(assetTypeQuery);
    }

    @GetMapping("/getList")
    @ApiOperation("根据查询条件查询资产类型列表")
    public List<AssetTypeTreeVo> getList(AssetTypeQuery assetTypeQuery) {
        return this.assetTypeService.getAssetTypeList(assetTypeQuery);
    }

    @PostMapping
    @ApiOperation("资产类型添加")
    public RestResponse<Void> add(@Validated @RequestBody AssetTypeAddDto assetType) {
        return Operation.ADD.response(this.assetTypeService.save(assetType.toAssetType()));
    }

    @PutMapping
    @ApiOperation("资产类型修改")
    public RestResponse<Void> update(@Validated @RequestBody AssetTypeUpdateDto assetType) {
        return Operation.UPDATE.response(this.assetTypeService.updateById(assetType.toAssetType()));
    }

    @DeleteMapping("/{id}")
    @ApiOperation("资产类型删除")
    public RestResponse<Void> delete(@ApiParam("资产类型id") @PathVariable Long id) {
        return Operation.DELETE.response(this.assetTypeService.removeById(id));
    }

    @PutMapping("/enable/{id}/{enable}")
    @ApiOperation("资产类型启停")
    public RestResponse<Void> enable(@ApiParam(value = "资产类型id", required = true) @PathVariable Long id,
                                     @ApiParam(value = "启停标识", required = true) @PathVariable Boolean enable) {
        return Operation.SAVE.response(this.assetTypeService.updateEnable(id, enable));
    }

    @DeleteMapping("clear")
    @ApiOperation("清理资产类型")
    public RestResponse<Void> delete() {
        return Operation.UPDATE.response(this.assetTypeService.clearAssetType());
    }
    /**
     * 导入资产类型
     **/
    @PostMapping("/import")
    @ApiOperation(value = "导入资产类型数据")
    public RestResponse<Void> impor(@RequestParam("file") MultipartFile file) throws Exception {
        boolean b = this.assetTypeService.importData(file);
        if (!b) {
            return new RestResponse(500, "解析失败", null, null);
        }
        return RestResponse.message("导入成功");
    }

    /**
     * 下载资产类型模板
     *
     * @param response
     * @return
     */
    @ApiOperation("下载导入资产类型模板")
    @GetMapping("/downTemplate")
    public RestResponse<Void> downTemplate(HttpServletResponse response) {
        Boolean b = assetTypeService.downTemplate(response);
        if (!b) {
            return new RestResponse(500, "下载失败", null, null);
        }
        return RestResponse.message("下载成功");
    }

    /**
     * 导出资产类型信息
     *
     * @param response
     * @param pageQuery
     * @return
     */
    @ApiOperation("/导出资产类型信息")
    @PostMapping("/export")
    @IgnoreOperationLog
    public RestResponse<Void> downOrderData(HttpServletResponse response, @RequestBody AssetTypeQuery pageQuery) {
        try {
            //页面下载设置
            Workbook workbook = assetTypeService.downloadData(pageQuery);
            DownloadResponseUtil.addDownLoadHeader(response, "资产类型信息.xlsx");
            workbook.write(response.getOutputStream());
        } catch (IOException e) {
            e.printStackTrace();
            return new RestResponse(500, "导出失败", null, null);
        }
        return RestResponse.message("导出成功");
    }

}
