package com.hightop.benyin.rfid.domain.service;

import com.github.yulichang.base.MPJBaseServiceImpl;
import com.hightop.benyin.rfid.application.vo.po.RfidAssetTakeDetailVo;
import com.hightop.benyin.rfid.application.vo.query.RfidAssetTakeDetailQuery;
import com.hightop.benyin.rfid.application.vo.query.RfidAssetTakeQuery;
import com.hightop.benyin.rfid.application.vo.query.RfidTakeDetailQuery;
import com.hightop.benyin.rfid.infrastructure.entity.RfidAssetTake;
import com.hightop.benyin.rfid.infrastructure.mapper.RfidAssetTakeMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 资产盘点领域服务
 *
 * <AUTHOR>
 * @date 2024-10-12 16:36:01
 */
@Service
public class RfidAssetTakeServiceDomain extends MPJBaseServiceImpl<RfidAssetTakeMapper, RfidAssetTake> {

    /**
     * 查询资产盘点列表
     *
     * @param query
     * @return
     */
    public List<RfidAssetTake> findAssetTakeList(RfidAssetTakeQuery query) {
        return baseMapper.findAssetTakeList(query);
    }

    /**
     * 查询资产盘点明细列表
     *
     * @param query
     * @return
     */
    public List<RfidAssetTakeDetailVo> findAssetTakeDetailList(RfidAssetTakeDetailQuery query) {
        return baseMapper.findAssetTakeDetailList(query);
    }
    /**
     * 查询资产盘点明细列表
     *
     * @param rfidTakeDetailQuery
     * @return
     */
    public List<RfidAssetTakeDetailVo> findAssetTakeScanList(RfidTakeDetailQuery rfidTakeDetailQuery) {
        return baseMapper.findAssetTakeScanList(rfidTakeDetailQuery);
    }

    public void clear() {
        baseMapper.clear();
    }
}
