<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hightop.benyin.system.infrastructure.mapper.LocaltionMapper">

    <resultMap id="locationResultMap" type="com.hightop.benyin.system.api.vo.LocationVo">
        <result column="departIds" property="departIds"  typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler" />
        <result column="id"  property="id" />
        <result column="name"  property="name" />
        <result column="another_name"  property="anotherName" />
        <result column="code"  property="code" />
        <result column="sort"  property="sort" />
        <result column="is_enable"  property="isEnable" />
        <result column="expire_at"  property="expireAt" />
        <result column="created_at"  property="createdAt" />
        <result column="full_id_path"  property="fullIdPath" />
        <result column="full_name"  property="fullName" />
        <result column="parentName"  property="parentName" />
    </resultMap>

    <select id="getLocationTreeList" resultMap="locationResultMap">
        SELECT t.id,
        t.parent_id,t.another_name,t.full_name,
       case when t.another_name is not null and t.another_name != '' and t.type!=2 then concat(t.name,'(',t.another_name,')') else t.name end name,
        t.type,
        t.code,
        t.is_enable,
        t.sort,
        t.is_available,
        t.expire_at,
        t.created_at,
        t.updated_at,
        t.updated_by,
        t.department_ids departIds,
        t.full_id_path,
        t2.name parentName
        FROM
            st_location t
        left join st_location t2 on t2.id = t.parent_id
        <where>
            t.is_available =1
            <if test="qo.code!= null and qo.code!= ''">
                and t.code like  concat ('%',#{qo.code},'%')
            </if>

            <if test="qo.name!= null and qo.name!= ''">
                and t.name like  concat ('%',#{qo.name},'%')
            </if>

            <if test="qo.companyCode!= null and qo.companyCode!= ''">
                and t.code like  concat ('%',#{qo.companyCode},'%')
            </if>

            <if test="qo.id!= null">
                and t.id =  #{qo.id}
            </if>


            <if test="qo.type!= null">
                and t.type =  #{qo.type}
            </if>

            <if test="qo.isEnable!= null">
                and t.is_enable =  #{qo.isEnable}
            </if>

            <if test="qo.isLeaf!= null">
                and t.is_leaf =  #{qo.isLeaf}
            </if>

            <if test="qo.departmentId!= null">
                AND JSON_CONTAINS(t.department_ids,cast(#{qo.departmentId} as char ))
            </if>

            <if test="null != qo.startDisableDate and '' != qo.startDisableDate ">
                and t.expire_at &gt;= concat(#{qo.startDisableDate},' 00:00:00')
            </if>
            <if test="null != qo.endDisableDate and '' != qo.endDisableDate ">
                and t.expire_at &lt;= concat(#{qo.endDisableDate},' 23:59:59')
            </if>

            <if test="null != qo.startEnableDate and '' != qo.startEnableDate ">
                and t.created_at &gt;= concat(#{qo.startEnableDate},' 00:00:00')
            </if>
            <if test="null != qo.endEnableDate and '' != qo.endEnableDate ">
                and t.created_at &lt;= concat(#{qo.endEnableDate},' 23:59:59')
            </if>
            <if test="null!=qo.departmentIds and !qo.departmentIds.isEmpty()">
                AND
                <foreach collection="qo.departmentIds" item="id" separator=" OR " open="(" close=")">
                    JSON_CONTAINS(t.department_ids,cast(#{id} as char ))
                </foreach>
            </if>

        </where>
    </select>

</mapper>