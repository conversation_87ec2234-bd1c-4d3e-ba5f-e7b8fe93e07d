package com.hightop.benyin.system.infrastructure.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.hightop.benyin.configurer.annotation.RecordLog;
import com.hightop.benyin.configurer.annotation.RecordLogField;
import com.hightop.benyin.configurer.enums.DataType;
import com.hightop.magina.standard.cipher.CipherText;
import com.hightop.magina.standard.ums.user.basic.UserState;
import com.hightop.magina.standard.ums.user.basic.UserType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.experimental.FieldDefaults;
import com.hightop.benyin.system.infrastructure.enums.BusinessType;

import java.time.LocalDateTime;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@Accessors(chain = true)
@TableName("st_user_basic")
@ApiModel
@RecordLog(businessType = BusinessType.USER)
public class UserInfo {
    @TableId(value = "id", type = IdType.NONE)
    Long id;
    /**
     * 账号
     */
    @RecordLogField(value = "账号")
    @TableField("code")
    String code;
    /**
     * 名字
     */
    @RecordLogField(value = "姓名")
    @TableField("name")
    String name;

    @RecordLogField(value = "所属部门",dataType = DataType.DEPT)
    @TableField("department_id")
    @ApiModelProperty(value = "所属部门", required = true)
    Long departmentId;

    @RecordLogField(value = "所在位置",dataType = DataType.LOCATION)
    @TableField("location_id")
    @ApiModelProperty(value = "所在位置", required = true)
    Long locationId;

    /**
     * 用户类型
     */
    @TableField("type")
    UserType type;
    /**
     * 用户状态
     */
    @TableField("state")
    UserState state;
    /**
     * 账户失效时间
     */
    @TableField(value = "account_expire_at",updateStrategy = FieldStrategy.IGNORED)
    LocalDateTime accountExpireAt;
    /**
     * 上次登录成功时间
     */
    @TableField("last_login_at")
    LocalDateTime lastLoginAt;
    /**
     * 锁定失效时间
     */
    @TableField("locked_expire_at")
    LocalDateTime lockedExpireAt;
    /**
     * 是否内置账号
     */
    @JsonIgnore
    @TableField("is_build_in")
    Boolean isBuildIn;
    /**
     * 是否可用 类似逻辑删除的状态标识字段
     */
    @RecordLogField(value = "启停状态",dataType = DataType.ENABLE)
    @TableField("is_available")
    @JsonIgnore
    Boolean isAvailable;
    /**
     * 创建人
     */
    @TableField("created_by")
    Long createdBy;
    /**
     * 创建时间
     */
    @TableField("created_at")
    LocalDateTime createdAt;

    /**
     * 更新人
     */
    @TableField(value = "updated_by")
    @ApiModelProperty("更新人")
    Long updatedBy;

    /**
     * 更新时间
     */
    @TableField("updated_at")
    LocalDateTime updatedAt;

    @TableField(exist = false )
    @RecordLogField(value = "邮箱")
    CipherText email;

    @TableField(exist = false )
    @RecordLogField(value = "电话")
    CipherText mobileNumber;

}
