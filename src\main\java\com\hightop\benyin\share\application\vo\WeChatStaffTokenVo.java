package com.hightop.benyin.share.application.vo;

import com.hightop.magina.standard.ums.system.LoggedUserVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.experimental.FieldDefaults;

/**
 * 员工小程序令牌
 * @Author: X.S
 * @date 2023/11/07 14:05
 */
@FieldDefaults(level = AccessLevel.PRIVATE)
@Accessors(chain = true)
@Data
public class WeChatStaffTokenVo {
    @ApiModelProperty("是否已关注公众号")
    Boolean isSubscribed;
    @ApiModelProperty("会话令牌")
    String token;
    @ApiModelProperty("会话用户信息")
    LoggedUserVo user;
}
