package com.hightop.benyin.share.infrastructure.restful.tencent.sts;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 联合身份临时访问凭证响应
 * @Author: X.S
 * @date 2023/10/25 19:20
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FederationTokenResponse extends StsResponse.StsBaseResponse {
    /**
     * 临时访问凭证
     */
    @JsonProperty("Credentials")
    FederationCredentials credentials;
    /**
     * 临时访问凭证有效的时间
     */
    @JsonProperty("ExpiredTime")
    Long expiredTime;
    /**
     * 临时访问凭证有效的时间，以 iso8601 格式的 UTC 时间表示
     */
    @JsonProperty("Expiration")
    String expiration;
}
