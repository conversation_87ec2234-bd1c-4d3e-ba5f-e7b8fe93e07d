package com.hightop.benyin.rfid.api;

import com.hightop.benyin.rfid.application.service.RfidAssetApplyService;
import com.hightop.benyin.rfid.application.service.RfidAssetFlowService;
import com.hightop.benyin.rfid.application.vo.dto.AssetApplyDto;
import com.hightop.benyin.rfid.application.vo.dto.AssetFlowAuditDto;
import com.hightop.benyin.rfid.application.vo.dto.AssetSubmitDto;
import com.hightop.benyin.rfid.application.vo.query.RfidAssetApplyQuery;
import com.hightop.benyin.rfid.application.vo.query.RfidAssetFlowQuery;
import com.hightop.benyin.rfid.application.vo.query.RfidAssetRecordQuery;
import com.hightop.benyin.rfid.infrastructure.entity.RfidAsset;
import com.hightop.benyin.rfid.infrastructure.entity.RfidAssetApply;
import com.hightop.benyin.rfid.infrastructure.entity.RfidAssetFlow;
import com.hightop.benyin.rfid.infrastructure.entity.RfidAssetRecord;
import com.hightop.fario.base.web.Operation;
import com.hightop.fario.base.web.RestResponse;
import com.hightop.fario.common.mybatis.DataGrid;
import com.hightop.magina.standard.behavior.operation.IgnoreOperationLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 资产日志管理rest接口
 *
 * <AUTHOR>
 * @date 2024-10-12 16:36:01
 */
@RequestMapping("/asset-flow")
@RestController
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Api(tags = "资产流程管理")
public class RfidAssetFlowController {
    RfidAssetFlowService rfidassetFlowService;
    RfidAssetApplyService rfidAssetApplyService;

    @PostMapping("/page")
    @ApiOperation("分页查询")
    @IgnoreOperationLog
    public RestResponse<DataGrid<RfidAssetFlow>> page(@RequestBody RfidAssetFlowQuery pageQuery) {
        return RestResponse.ok(this.rfidassetFlowService.page(pageQuery));
    }

    @PostMapping("/detailPage")
    @ApiOperation("资产业务明细查询")
    @IgnoreOperationLog
    public RestResponse<DataGrid<RfidAssetApply>> detailPage(@RequestBody RfidAssetApplyQuery pageQuery) {
        return RestResponse.ok(this.rfidAssetApplyService.page(pageQuery));
    }

    @PostMapping()
    @ApiOperation("登记暂存提交")
    public RestResponse<Void> apply(@Validated@RequestBody AssetSubmitDto assetSubmitDto) {
        return Operation.ADD.response(this.rfidassetFlowService.submit(assetSubmitDto,null,null));
    }

    @PostMapping("/inStock")
    @ApiOperation("登记保存提交")
    public RestResponse<Void> inStock(@Validated @RequestBody RfidAsset rfidasset) {
        return Operation.ADD.response(this.rfidassetFlowService.inStock(rfidasset));
    }

    @PostMapping("/apply")
    @ApiOperation("资产业务申请")
    public RestResponse<Void> apply(@Validated @RequestBody AssetApplyDto assetApplyDto) {
        return Operation.ADD.response(this.rfidassetFlowService.apply(assetApplyDto));
    }


    @PostMapping("approve")
    @ApiOperation("审核资产业务申请")
    public RestResponse<Void> approve(@Validated @RequestBody AssetFlowAuditDto assetFlowAuditDto) {
        return Operation.ADD.response(this.rfidassetFlowService.approve(assetFlowAuditDto));
    }


    @DeleteMapping("close/{id}")
    @ApiOperation("关闭资产业务申请")
    public RestResponse<Void> close(@PathVariable @ApiParam("id") Long id) {
        return Operation.ADD.response(this.rfidassetFlowService.close(id));
    }


    @GetMapping("/recordPage")
    @ApiOperation("资产业务申请审核记录")
    public RestResponse<DataGrid<RfidAssetRecord>> recordPage(RfidAssetRecordQuery rfidAssetRecordQuery) {
        return RestResponse.ok(this.rfidassetFlowService.recordPage(rfidAssetRecordQuery));
    }

    @DeleteMapping("/{id}")
    @ApiOperation("删除")
    public RestResponse<Void> delete(@PathVariable @ApiParam("id") Long id) {
        return Operation.DELETE.response(this.rfidassetFlowService.remove(id));
    }

    /**
     * 导出当前资产数据
     *
     * @param response
     * @param pageQuery
     * @return
     */
    @ApiOperation("/导出当前资产数据")
    @GetMapping("/export")
    public void downOrderData(HttpServletResponse response, RfidAssetFlowQuery pageQuery) throws IOException {
        rfidassetFlowService.downloadData(response, pageQuery);
    }


}
