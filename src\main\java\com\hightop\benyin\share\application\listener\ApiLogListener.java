package com.hightop.benyin.share.application.listener;

import com.hightop.benyin.rfid.domain.service.RfidReaderServiceDomain;
import com.hightop.benyin.rfid.infrastructure.entity.RfidReader;
import com.hightop.benyin.share.domain.event.ApiLogEvent;
import com.hightop.benyin.share.domain.service.ApiLogServiceDomain;
import com.hightop.benyin.share.infrastructure.entity.ApiLog;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * 日志监听
 *
 * @Author: X.S
 * @date 2024/09/25 15:48
 */
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Service
@Slf4j
public class ApiLogListener {
    ApiLogServiceDomain apiLogServiceDomain;
    RfidReaderServiceDomain rfidReaderServiceDomain;

    @EventListener
    public void doEvent(ApiLogEvent event) {

        log.info("日志保存事件: {}", event);
        ApiLog apiLog = new ApiLog();
        apiLog.setType(event.getMessageType());
        apiLog.setCommand(event.getCommand());
        apiLog.setRequestPayload(event.getMessage());
        apiLog.setDeviceId(event.getDeviceId());
        apiLog.setRequestedAt(LocalDateTime.now());
        apiLog.setRemark(event.getRemark());
        apiLog.setStatusCode(event.getIsSuccess());

        RfidReader rfidReader =   rfidReaderServiceDomain.lambdaQuery()
                .eq(RfidReader::getDeviceId, apiLog.getDeviceId())
                .one();
        apiLog.setReaderCode(rfidReader!=null?rfidReader.getCode():null);
        apiLogServiceDomain.save(apiLog);
    }
}
