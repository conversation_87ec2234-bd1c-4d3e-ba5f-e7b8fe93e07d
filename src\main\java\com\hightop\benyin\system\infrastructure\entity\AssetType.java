package com.hightop.benyin.system.infrastructure.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelIgnore;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.hightop.magina.standard.ums.user.bind.UserBind;
import com.hightop.magina.standard.ums.user.bind.UserEntry;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.experimental.FieldDefaults;
import net.sf.jsqlparser.expression.Alias;
import net.sf.jsqlparser.schema.Table;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Comparator;
import java.util.Objects;

@Data
@TableName(value = "b_asset_type")
@FieldDefaults(level = AccessLevel.PRIVATE)
@Accessors(chain = true)
public class AssetType implements Serializable {

    @TableId(value = AssetType.ID_FIELD, type = IdType.NONE)
    @ApiModelProperty("id")
    Long id;

    @TableField("parent_id")
    @ApiModelProperty("父id")
    Long parentId;


    @TableField("code")
    @ApiModelProperty("资产类型编码")
    @Excel(name = "资产类型编码", width = 30, orderNum = "0")
    String code;

    @TableField("name")
    @ApiModelProperty("资产类型名称")
    @Excel(name = "资产类型名称", width = 30, orderNum = "1")
    String name;

    @TableField(exist = false)
    @ApiModelProperty("上级类型名称")
    @Excel(name = "上级类型名称", width = 30, orderNum = "2")
    String parentName;

    @TableField(exist = false)
    @Excel(name = "上级类型编码", width = 30, orderNum = "3")
    @ApiModelProperty("上级类型编码")
    String parentCode;

    @TableField("has_tag")
    @ApiModelProperty("是否贴标签")
    @Excel(name = "是否贴标签", width = 30, orderNum = "4", replace = {" _null","是_true", "否_false"})
    Boolean hasTag;

    @TableField("is_scan")
    @ApiModelProperty("是否扫描")
    @Excel(name = "是否扫描", width = 30, orderNum = "5", replace = {" _null","是_true", "否_false"})
    Boolean isScan;

    @TableField("is_report")
    @ApiModelProperty("是否上报异常")
    @Excel(name = "是否上报异常", width = 30, orderNum = "6", replace = {" _null","是_true", "否_false"})
    Boolean isReport;

    @TableField("is_take")
    @ApiModelProperty("是否盘点")
    @Excel(name = "是否盘点", width = 30, orderNum = "7", replace = {" _null","是_true", "否_false"})
    Boolean isTake;

    @TableField("is_take_statis")
    @ApiModelProperty("是否盘点统计")
    @Excel(name = "是否盘点统计", width = 30, orderNum = "8", replace = {" _null","是_true", "否_false"})
    Boolean isTakeStatis;

    @TableField("is_statis")
    @ApiModelProperty("是否财务统计")
    @Excel(name = "是否财务统计", width = 30, orderNum = "9", replace = {" _null", "是_true", "否_false"})
    Boolean isStatis;

    @TableField("sort")
    @ApiModelProperty("排序号")
    @Excel(name = "排序号", width = 30, orderNum = "10")
    Integer sort;

    @TableField("is_enable")
    @ApiModelProperty("启停状态")
    @ExcelIgnore
    Boolean isEnable;

    /**
     * 是否可用 类似逻辑删除的状态标识字段
     */
    @TableField("is_available")
    @JsonIgnore
    Boolean isAvailable;

    @TableField(value = "expire_at", updateStrategy = FieldStrategy.IGNORED)
    @ApiModelProperty("失效时间")
    @ExcelIgnore
    LocalDateTime expireAt;

    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @ApiModelProperty("创建时间")
    @Excel(name = "创建时间", width = 30, orderNum = "11", format = "yyyy/MM/dd HH:mm:ss")
    LocalDateTime createdAt;


    @TableField("created_by")
    @ApiModelProperty("创建人")
    @UserBind
    @ExcelIgnore
    UserEntry createdBy;

    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty("更新时间")
    @Excel(name = "更新时间", width = 30, orderNum = "12", format = "yyyy/MM/dd HH:mm:ss")
    LocalDateTime updatedAt;

    @TableField(value = "updated_by")
    @ApiModelProperty("更新人")
    Long updatedBy;


    /**
     * id全路径
     */
    @TableField(AssetType.FULL_ID_PATH_FIELD)
    @JsonIgnore
    String fullIdPath;

    /**
     * 顶级节点父id
     */
    public static final Long TOP = 0L;
    /**
     * 默认比较器
     */
    public static final Comparator<AssetType> COMPARATOR =
            // 先按照排序号排序再按照编码排序
            Comparator.comparingInt(AssetType::getSort).thenComparing(it -> it.getCode());
    /**
     * id字段名称
     */
    public static final String ID_FIELD = "id";
    /**
     * id路径字段名称
     */
    public static final String FULL_ID_PATH_FIELD = "full_id_path";
    /**
     * 表名称
     */
    static final String TABLE_NAME = "b_asset_type";

    /**
     * jsql位置表名
     *
     * @param alias  别名
     * @param withAs 是否要as关键字
     * @return {@link Table}
     */
    public static Table table(String alias, boolean withAs) {
        return new Table().withName("st_location").withAlias(new Alias(alias, withAs));
    }

    /**
     * 是否是不可用的位置
     *
     * @param assetType {@link AssetType}
     * @return true/false
     */
    public static boolean isDisable(AssetType assetType) {
        return assetType.isNotExists(assetType) || !assetType.getIsEnable();
    }

    /**
     * 是否是不存在的位置
     *
     * @param assetType {@link AssetType}
     * @return true/false
     */
    public static boolean isNotExists(AssetType assetType) {
        return Objects.isNull(assetType) || !assetType.getIsAvailable();
    }


}
