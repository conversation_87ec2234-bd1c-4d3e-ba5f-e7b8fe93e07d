package com.hightop.benyin.configurer;

import com.hightop.fario.base.exception.FarioException;
import com.hightop.fario.base.web.RestResponse;
import com.hightop.fario.common.core.configuration.GlobalExceptionAdvice;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.util.Objects;
import java.util.concurrent.CompletionException;

/**
 * 扩展异常拦截
 * @Author: X.S
 * @date 2024/09/30 10:44
 */
@RestControllerAdvice
public class ExpandExceptionAdvice extends GlobalExceptionAdvice {
    /**
     * 处理{@link java.util.concurrent.CompletableFuture}异常
     * @param e {@link CompletionException}
     * @return {@link RestResponse}
     */
    @ExceptionHandler(CompletionException.class)
    @ResponseStatus(HttpStatus.OK)
    public RestResponse<?> handleCompletionException(CompletionException e) {
        Throwable cause = e.getCause();
        if (Objects.isNull(cause)) {
            return super.handleException(e);
        }

        if (cause instanceof FarioException) {
            return super.handleFarioException((FarioException) cause);
        }

        return super.handleException((Exception) cause);
    }
}
