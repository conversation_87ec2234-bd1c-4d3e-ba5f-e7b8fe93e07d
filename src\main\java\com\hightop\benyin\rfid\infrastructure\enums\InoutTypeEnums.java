package com.hightop.benyin.rfid.infrastructure.enums;

import com.hightop.magina.core.custom.entry.EnumEntry;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

/**
 * 出登记
 *
 * @Author: X.S
 * @Date: 2023/12/19 11:43
 */
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@Getter
@RequiredArgsConstructor
public enum InoutTypeEnums implements EnumEntry<Integer> {
    /**
     * 未扫描
     */
    IN(1, "登记"),
    /**
     * 未扫描
     */
    OUT(2, "领用");
    /**
     * 代码
     */
    Integer code;
    /**
     * 描述
     */
    String name;
}
