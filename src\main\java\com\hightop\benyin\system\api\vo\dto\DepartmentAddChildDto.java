package com.hightop.benyin.system.api.vo.dto;

import com.hightop.magina.standard.cipher.CipherText;
import com.hightop.magina.standard.ums.department.Department;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class DepartmentAddChildDto {
    @ApiModelProperty("父id")
    Long parentId;
    @ApiModelProperty("名称")
    @NotBlank(message = "名称不能为空")
    @Size(min = 1, max = 64, message = "名称长度在{min}至{max}之间")
    String name;
    @ApiModelProperty("排序号")
    @NotNull(message = "排序号不能为空")
    Integer sort;

    public Department toDepartment() {
        return
                new Department().setParentId(this.parentId)
                        .setName(new CipherText(this.name))
                        .setSort(this.sort);
    }
}
