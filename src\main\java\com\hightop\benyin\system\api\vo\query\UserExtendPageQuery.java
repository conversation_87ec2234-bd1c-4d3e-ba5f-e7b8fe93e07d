package com.hightop.benyin.system.api.vo.query;

import com.hightop.magina.standard.ums.user.manage.UserPageQuery;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldDefaults;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@FieldDefaults(level = AccessLevel.PRIVATE)
public class UserExtendPageQuery extends UserPageQuery {

    @ApiModelProperty("id")
    Long id;

    @ApiModelProperty("账号")
    String code;

    @ApiModelProperty("启停状态1 启用 0 停用")
    Boolean isAvailable;

    @ApiModelProperty("启用时间-起始")
    String startEnableDate;

    @ApiModelProperty("启用时间-终止")
    String endEnableDate;

    @ApiModelProperty("停用时间-起始")
    String startDisableDate;

    @ApiModelProperty("停用时间-终止")
    String endDisableDate;

    @ApiModelProperty("部门id")
    Long departmentId;

    @ApiModelProperty("部门编码")
    String departmentCode;

    @ApiModelProperty("多部门id")
    List<Long> departmentIds;

    @ApiModelProperty("多位置id")
    List<Long> locations;

    @ApiModelProperty("位置id")
    Long locationId;

    @ApiModelProperty("更新人")
    String updatedByName;

    @ApiModelProperty("性别")
    String sex;

    @ApiModelProperty("手机号")
    String mobileNumber;

    @ApiModelProperty("身份证号码")
    String identityCardNumber;

    @ApiModelProperty("邮箱")
    String email;

    @ApiModelProperty("创建时间-起始")
    String startDate;

    @ApiModelProperty("创建时间-终止")
    String endDate;

    @ApiModelProperty("修改时间-起始")
    String startUpdateDate;

    @ApiModelProperty("修改时间-终止")
    String endUpdateDate;

    @ApiModelProperty("是否内置账号")
    Boolean isBuildIn;

}
