package com.hightop.benyin.rfid.application.vo.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @Description: 资产扫描结果接收DTO
 * @Author: X.S
 * @Date: 2023/12/22 17:16
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@ApiModel("资产扫描结果接收DTO")
public class AssetScanDto {

    @ApiModelProperty("业务类型")
    @NotBlank(message = "业务类型不能为空")
    String businessType;

    @ApiModelProperty("业务编码")
    @NotBlank(message = "业务编码不能为空")
    String bindCode;

    @ApiModelProperty(value = "扫描设备")
    @NotNull(message = "扫描设备不能为空")
    Long readerId;

}
