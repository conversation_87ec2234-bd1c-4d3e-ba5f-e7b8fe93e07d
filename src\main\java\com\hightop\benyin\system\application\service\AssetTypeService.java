package com.hightop.benyin.system.application.service;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.github.yulichang.toolkit.MPJWrappers;
import com.google.common.collect.Lists;
import com.hightop.benyin.share.infrastructure.entity.Region;
import com.hightop.benyin.share.infrastructure.util.DownloadResponseUtil;
import com.hightop.benyin.system.api.vo.AssetTypeTreeVo;
import com.hightop.benyin.system.api.vo.excel.AssetTypeExcel;
import com.hightop.benyin.system.api.vo.query.AssetTypeQuery;
import com.hightop.benyin.system.application.handler.AssetTypeExcelVerifyHandler;
import com.hightop.benyin.system.domain.service.AssetTypeDomainService;
import com.hightop.benyin.system.infrastructure.entity.AssetType;
import com.hightop.benyin.system.infrastructure.entity.AssetTypeJoin;
import com.hightop.fario.base.constant.StringConstants;
import com.hightop.fario.base.util.CollectionUtils;
import com.hightop.fario.base.util.StringUtils;
import com.hightop.fario.common.core.util.ExecutorUtils;
import com.hightop.magina.casual.session.ApplicationSessions;
import com.hightop.magina.core.custom.entry.TreeEntry;
import com.hightop.magina.core.exception.MaginaException;
import com.hightop.magina.standard.ums.user.bind.UserEntry;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;

/**
 * 资产类型管理服务
 *
 * <AUTHOR>
 * @date 2022/09/13 14:28
 * @since 2.0.0
 */
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Service
public class AssetTypeService {

    AssetTypeDomainService assetTypeDomainService;

    /**
     * 获得资产类型树
     *
     * @return {@link List}
     */
    public List<AssetTypeTreeVo> getAssetTypeTree(AssetTypeQuery assetTypeQuery) {
        List<AssetTypeTreeVo> items = this.assetTypeDomainService.selectJoinList(AssetTypeTreeVo.class,
                MPJWrappers.<AssetType>lambdaJoin()
                        .selectAll(AssetType.class)
                        .eq(AssetType::getIsAvailable, true)
                        .like(StringUtils.isNotBlank(assetTypeQuery.getCode()), AssetType::getCode, assetTypeQuery.getCode())
                        .like(StringUtils.isNotBlank(assetTypeQuery.getName()), AssetType::getName, assetTypeQuery.getName())
                        .eq(assetTypeQuery.getIsEnable() != null, AssetType::getIsEnable, assetTypeQuery.getIsEnable())
                        .eq(assetTypeQuery.getHasTag() != null, AssetType::getHasTag, assetTypeQuery.getHasTag())
                        .eq(assetTypeQuery.getIsReport() != null, AssetType::getIsReport, assetTypeQuery.getIsReport())
                        .eq(assetTypeQuery.getIsScan() != null, AssetType::getIsScan, assetTypeQuery.getIsScan())
                        .eq(assetTypeQuery.getIsStatis() != null, AssetType::getIsStatis, assetTypeQuery.getIsStatis())
                        .eq(assetTypeQuery.getIsTake() != null, AssetType::getIsTake, assetTypeQuery.getIsTake())
                        .eq(assetTypeQuery.getIsTakeStatis() != null, AssetType::getIsTakeStatis, assetTypeQuery.getIsTakeStatis())
        );
//        if (CollectionUtils.isNotEmpty(items)) {
//            List<AssetTypeTreeVo> assetTypeTreeVos = items.stream().filter(item -> item.getParentId().equals(AssetTypeTreeVo.TOP)).collect(Collectors.toList());
//            if (CollectionUtils.isEmpty(assetTypeTreeVos)) {
//                for (AssetTypeTreeVo assetTypeTreeVo : items) {
//                    while (!assetTypeTreeVo.getParentId().equals(DepartmentInfo.TOP)) {
//                        AssetTypeTreeVo parent = this.assetTypeDomainService.getBaseMapper()
//                                .selectJoinOne(
//                                        AssetTypeTreeVo.class,
//                                        MPJWrappers.<AssetType>lambdaJoin()
//                                                .selectAll(AssetType.class)
//                                                .eq(AssetType::getId, assetTypeTreeVo.getParentId())
//                                );
//                        assetTypeTreeVos.add(parent);
//                        assetTypeTreeVo = parent;
//                    }
//                }
//                assetTypeTreeVos.addAll(items);
//                items = assetTypeTreeVos.stream().distinct().collect(Collectors.toList());
//            }
//        }
        return TreeEntry.generate(items, AssetType.COMPARATOR, AssetType.TOP);
    }

    /**
     * 资产类型保存
     *
     * @param assetType {@linkAssetType}
     * @return true/false
     */
    public boolean save(AssetType assetType) {
        if (Objects.isNull(assetType.getParentId())) {
            assetType.setParentId(AssetType.TOP);
        }
        this.checkAssetType(assetType);
        assetType.setId(IdWorker.getId());
        this.setFullIdPath(assetType);

        return this.assetTypeDomainService.save(assetType);
    }

    public  List<AssetTypeTreeVo> getQueryList(AssetTypeQuery assetTypeQuery) {

        return this.getAssetTypeList(assetTypeQuery);
    }

    public List<AssetTypeTreeVo> getAssetTypeList(AssetTypeQuery assetTypeQuery) {

        JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(assetTypeQuery));
        Set<Map.Entry<String, Object>> paramSet = jsonObject.entrySet().stream().filter(entry -> entry.getValue() != null).collect(Collectors.toSet());
        boolean hasQueryCondition = false;
        if (CollectionUtils.isNotEmpty(paramSet)) {
            hasQueryCondition= true;
        }
        //表示有查询条件
        if(hasQueryCondition){
            List<Long> ids =Lists.newArrayList(1L);
                    List<AssetType> assetTypes = this.assetTypeDomainService.lambdaQuery()
                    .eq(AssetType::getIsAvailable, true)
                    .like(StringUtils.isNotBlank(assetTypeQuery.getCode()), AssetType::getCode, assetTypeQuery.getCode())
                    .like(StringUtils.isNotBlank(assetTypeQuery.getName()), AssetType::getName, assetTypeQuery.getName())
                    .eq(assetTypeQuery.getIsEnable() != null, AssetType::getIsEnable, assetTypeQuery.getIsEnable())
                    .eq(assetTypeQuery.getHasTag() != null, AssetType::getHasTag, assetTypeQuery.getHasTag())
                    .eq(assetTypeQuery.getIsReport() != null, AssetType::getIsReport, assetTypeQuery.getIsReport())
                    .eq(assetTypeQuery.getIsScan() != null, AssetType::getIsScan, assetTypeQuery.getIsScan())
                    .eq(assetTypeQuery.getIsStatis() != null, AssetType::getIsStatis, assetTypeQuery.getIsStatis())
                    .eq(assetTypeQuery.getIsTake() != null, AssetType::getIsTake, assetTypeQuery.getIsTake())
                    .eq(assetTypeQuery.getIsTakeStatis() != null, AssetType::getIsTakeStatis, assetTypeQuery.getIsTakeStatis())
                    .list();
            if(CollectionUtils.isNotEmpty(assetTypes)){
                ids = assetTypes.stream().map(AssetType::getId).collect(Collectors.toList());
                for (AssetType item : assetTypes) {
                    AssetType assetType =item;
                    while (!Objects.equals(assetType.getParentId(), AssetType.TOP)) {
                        AssetType parentType = assetTypeDomainService.getById(assetType.getParentId());
                        if (Objects.isNull(parentType)) {
                            break;
                        }
                        assetType = parentType;
                        if(!ids.contains(assetType.getId())){
                            ids.add(assetType.getId());
                        }
                    }
                }
            }
            assetTypeQuery.setIds(ids);
        }
        List<AssetTypeTreeVo> assetTypes = this.assetTypeDomainService.selectJoinList(AssetTypeTreeVo.class,
                MPJWrappers.<AssetType>lambdaJoin()
                        .selectAll(AssetType.class)
                        .leftJoin(AssetTypeJoin.class,AssetTypeJoin::getId, AssetType::getParentId)
                        .eq(assetTypeQuery.getParentId() != null, AssetType::getParentId, assetTypeQuery.getParentId())
                        .eq(assetTypeQuery.getParentId() == null&&StringUtils.isBlank(assetTypeQuery.getParentCode()), AssetType::getParentId,AssetType.TOP)
                        .in(CollectionUtils.isNotEmpty(assetTypeQuery.getIds()), AssetType::getId, assetTypeQuery.getIds())
                        .eq(StringUtils.isNotBlank(assetTypeQuery.getParentCode()),AssetTypeJoin::getCode, assetTypeQuery.getParentCode())
                        .eq(AssetType::getIsAvailable, true)
        );

        List<Long> parentIds = assetTypes.stream().map(AssetType::getId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(parentIds)) {
            List<AssetTypeTreeVo> childAssetTypes = this.assetTypeDomainService.selectJoinList(AssetTypeTreeVo.class,
                    MPJWrappers.<AssetType>lambdaJoin()
                            .selectAll(AssetType.class).in(AssetType::getParentId, parentIds));

            Map<Long, List<AssetTypeTreeVo>> childMap = childAssetTypes.stream().collect(Collectors.groupingBy(AssetType::getParentId));
            assetTypes.forEach(item -> {
                if (childMap.containsKey(item.getId())) {
                    item.setChildren(new ArrayList<>());
                }
            });
        }

        return assetTypes;
    }

    /**
     * 资产类型更新
     *
     * @param assetType {@linkAssetType}
     * @return true/false
     */
    public boolean updateById(AssetType assetType) {
        this.checkAssetType(assetType);
        // 原始记录
        AssetType origin = this.assetTypeDomainService.getById(assetType.getId());
        // 仅当修改了父资产类型id时修改资产类型全路径
        if (!Objects.equals(assetType.getParentId(), origin.getParentId())) {
            // 按照父资产类型id设置id全路径
            this.setFullIdPath(assetType);

            // 更新子资产类型id全路径
            List<AssetType> children = new ArrayList<>();
            BiConsumer<List<AssetType>, String> recursion = new BiConsumer<List<AssetType>, String>() {
                @Override
                public void accept(List<AssetType> list, String prefix) {
                    if (CollectionUtils.isEmpty(list)) {
                        return;
                    }
                    list.forEach(it -> {
                        it.setFullIdPath(prefix + section(it.getId()));
                        // 待更新数据
                        children.add(it);
                        // 递归子节点
                        Optional.ofNullable(AssetTypeService.this.assetTypeDomainService.lambdaQuery()
                                        .eq(AssetType::getParentId, it.getId())
                                        .eq(AssetType::getIsAvailable, true)
                                        .list())
                                .filter(CollectionUtils::isNotEmpty)
                                .ifPresent(v -> this.accept(v, it.getFullIdPath()));
                    });
                }
            };

            recursion.accept(Collections.singletonList(assetType), assetType.getFullIdPath());
            return this.assetTypeDomainService.updateBatchById(children);
        }

        return this.assetTypeDomainService.updateById(assetType);
    }

    /**
     * 资产类型删除 逻辑删除
     *
     * @param id 资产类型id
     * @return 是否更新成功
     */
    public boolean removeById(Long id) {
        // 检查是否有下级
        long count =
                this.assetTypeDomainService.lambdaQuery()
                        .eq(AssetType::getParentId, id)
                        .eq(AssetType::getIsAvailable, true)
                        .count();
        if (count > 0) {
            throw new MaginaException("存在子资产类型不允许删除");
        }
        // 更新资产类型为不可用
        return this.assetTypeDomainService.removeById(id);
    }

    /**
     * 资产类型启停
     *
     * @param id   资产类型id
     * @param flag 启停标识
     * @return 是否更新成功
     */
    public boolean updateEnable(Long id, Boolean flag) {
        this.assetTypeDomainService.lambdaUpdate()
                .set(AssetType::getIsEnable, flag)
                .eq(AssetType::getParentId, id)
                .update();
        return this.assetTypeDomainService.lambdaUpdate()
                .set(AssetType::getIsEnable, flag)
                .eq(AssetType::getId, id)
                .update();
    }

    /**
     * 资产类型编码重复性校验
     *
     * @param assetType {@linkAssetType}
     */
    protected void checkAssetType(AssetType assetType) {
        Long codeCount =
                this.assetTypeDomainService.lambdaQuery()
                        .eq(AssetType::getCode, assetType.getCode())
                        .eq(AssetType::getIsAvailable, true)
                        .ne(Objects.nonNull(assetType.getId()), AssetType::getId, assetType.getId())
                        .count();

        if (codeCount > 0) {
            throw new MaginaException("资产类型编码已存在");
        }

        Long levelCount =
                this.assetTypeDomainService.lambdaQuery()
                        .eq(AssetType::getParentId, assetType.getParentId())
                        .eq(AssetType::getName, assetType.getName())
                        .eq(AssetType::getIsAvailable, true)
                        .ne(Objects.nonNull(assetType.getId()), AssetType::getId, assetType.getId())
                        .count();

        if (levelCount > 0) {
            throw new MaginaException("同层级已存在相同资产类型名称");
        }
    }

    /**
     * 设置id全路径
     *
     * @param assetType {@linkAssetType}
     */
    protected void setFullIdPath(AssetType assetType) {
        // 设置全路径id
        assetType.setFullIdPath(
                Optional.ofNullable(assetType.getParentId())
                        .map(this.assetTypeDomainService::getById)
                        .map(AssetType::getFullIdPath)
                        .orElse(StringConstants.EMPTY)
                        + section(assetType.getId())
        );
    }

    /**
     * 路径分段
     *
     * @param id 资产类型id
     * @return 路径段
     */
    static String section(Long id) {
        return StringConstants.SLASH + id;
    }


    /**
     * 下载模板
     *
     * @param response
     * @return
     */
    public Boolean downTemplate(HttpServletResponse response) {
        List<AssetTypeExcel> excelList = new ArrayList<>();
        try {
            //页面下载设置
            DownloadResponseUtil.addDownLoadHeader(response, "资产类型导入模板.xlsx");
            //将并添加信息，实体类结构，类的数据做成excel表格对象workbook
            Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams(),
                    AssetTypeExcel.class, excelList
            );
            workbook.write(response.getOutputStream());
        } catch (IOException e) {
            e.printStackTrace();
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    /**
     * 导入用户
     *
     * @param file
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean importData(MultipartFile file) {
        List<AssetTypeExcel> excels;
        try {
            InputStream in = file.getInputStream();
            ImportParams params = new ImportParams();
            params.setNeedVerify(true);
            params.setVerifyHandler(new AssetTypeExcelVerifyHandler());
            ExcelImportResult<AssetTypeExcel> excelImportResult = ExcelImportUtil.importExcelMore(in, AssetTypeExcel.class, params);
            if (excelImportResult.isVerifyFail()) {
                String errorMsg = excelImportResult.getFailList().stream().map(v -> {
                    return "第" + v.getRowNum() + "行，" + v.getErrorMsg();
                }).collect(Collectors.joining("！"));
                throw new MaginaException(errorMsg);
            }
            excels = excelImportResult.getList();
            Map<String, List<AssetTypeExcel>> stringListMap = excels.stream().collect(Collectors.groupingBy(AssetTypeExcel::getCode));
            for (Map.Entry<String, List<AssetTypeExcel>> entry : stringListMap.entrySet()) {
                List<AssetTypeExcel> value = entry.getValue();
                if (value.size() > 1) {
                    throw new MaginaException("资产类型编码" + entry.getKey() + "本次导入存在重复数据");
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new MaginaException("导入失败！原因：" + e.getMessage());
        }

        if (CollectionUtils.isEmpty(excels)) {
            throw new MaginaException("导入失败！解析数据为空！");
        }
        Map<String, Long> parentMap = excels.stream().collect(Collectors.toMap(AssetTypeExcel::getCode, AssetTypeExcel::getId));
        List<AssetType> assetTypeTrees = excels.stream().map(v -> {
            AssetType assetType = new AssetType();
            assetType.setId(v.getId());
            assetType.setParentId(v.getParentId());
            assetType.setName(v.getName());
            assetType.setCode(v.getCode());
            assetType.setHasTag(v.getHasTag());
            assetType.setIsScan(v.getIsScan());
            assetType.setIsReport(v.getIsReport());
            assetType.setIsStatis(v.getIsStatis());
            assetType.setIsTake(v.getIsTake());
            assetType.setIsTakeStatis(v.getIsTakeStatis());
            assetType.setIsEnable(v.getIsEnable() == null ? true : v.getIsEnable());
            if (StringUtils.isNotBlank(v.getParentCode())) {
                if (v.getParentId() == null) {
                    assetType.setParentId(parentMap.get(v.getParentCode()));
                }
            } else {
                assetType.setParentId(AssetType.TOP);
            }
            assetType.setIsAvailable(true);
            assetType.setUpdatedAt(LocalDateTime.now());
            assetType.setUpdatedBy(ApplicationSessions.id());
            assetType.setCreatedBy(new UserEntry().setId(ApplicationSessions.id()));
            return assetType;
        }).collect(Collectors.toList());
        this.assetTypeDomainService.saveOrUpdateBatch(assetTypeTrees);
        ExecutorUtils.doAfterCommit(() -> {
            this.setFullIdPaths(assetTypeTrees);
        });
        return Boolean.TRUE;
    }

    public void setFullIdPaths(List<AssetType> assetTypes) {
        assetTypes.forEach(this::setFullIdPath);
        this.assetTypeDomainService.updateBatchById(assetTypes);
    }


    /**
     * 导出资产类型信息
     *
     * @param pageQuery
     * @return
     */
    public Workbook downloadData(AssetTypeQuery assetTypeQuery) {
        //查询数据
        List<AssetType> excelList = this.assetTypeDomainService.lambdaQuery()
                .like(StringUtils.isNotBlank(assetTypeQuery.getCode()), AssetType::getCode, assetTypeQuery.getCode())
                .like(StringUtils.isNotBlank(assetTypeQuery.getName()), AssetType::getName, assetTypeQuery.getName())
                .eq(assetTypeQuery.getHasTag() != null, AssetType::getHasTag, assetTypeQuery.getHasTag())
                .eq(assetTypeQuery.getIsReport() != null, AssetType::getIsReport, assetTypeQuery.getIsReport())
                .eq(assetTypeQuery.getIsScan() != null, AssetType::getIsScan, assetTypeQuery.getIsScan())
                .eq(assetTypeQuery.getIsStatis() != null, AssetType::getIsStatis, assetTypeQuery.getIsStatis())
                .eq(assetTypeQuery.getIsTake() != null, AssetType::getIsTake, assetTypeQuery.getIsTake())
                .eq(assetTypeQuery.getIsTakeStatis() != null, AssetType::getIsTakeStatis, assetTypeQuery.getIsTakeStatis())
                .orderByAsc(AssetType::getCode)
                .list();
        for (AssetType assetType : excelList) {
            if (assetType.getParentId() != null && assetType.getParentId() != AssetType.TOP) {
                AssetType assetType1 = assetTypeDomainService.getById(assetType.getParentId());
                assetType.setParentName(assetType1.getName());
                assetType.setParentCode(assetType1.getCode());
            }
        }
        //将并添加信息，实体类结构，类的数据做成excel表格对象workbook
        return ExcelExportUtil.exportExcel(new ExportParams(), AssetType.class, excelList);
    }

    public Boolean clearAssetType() {
        assetTypeDomainService.clearAssetType();
        return Boolean.TRUE;
    }

}
