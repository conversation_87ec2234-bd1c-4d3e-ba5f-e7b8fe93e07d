package com.hightop.benyin.rfid.domain.service;

import com.github.yulichang.base.MPJBaseServiceImpl;
import com.hightop.benyin.rfid.infrastructure.entity.RfidAssetFlow;
import com.hightop.benyin.rfid.infrastructure.mapper.RfidAssetFlowMapper;
import org.springframework.stereotype.Service;

/**
 * rfid资产流水管理领域服务
 *
 * <AUTHOR>
 * @date 2024-10-12 16:36:01
 */
@Service
public class RfidAssetFlowServiceDomain extends MPJBaseServiceImpl<RfidAssetFlowMapper, RfidAssetFlow> {
}
