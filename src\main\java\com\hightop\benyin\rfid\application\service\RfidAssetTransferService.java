package com.hightop.benyin.rfid.application.service;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.hightop.benyin.rfid.application.vo.dto.AssetChangeLogDto;
import com.hightop.benyin.rfid.application.vo.dto.AssetTransferAuditDto;
import com.hightop.benyin.rfid.application.vo.dto.RfidChangerDto;
import com.hightop.benyin.rfid.application.vo.dto.RfidTransferDto;
import com.hightop.benyin.rfid.application.vo.query.RfidAssetTransferDetailQuery;
import com.hightop.benyin.rfid.application.vo.query.RfidAssetTransferQuery;
import com.hightop.benyin.rfid.domain.event.AssetChangeEvent;
import com.hightop.benyin.rfid.domain.service.RfidAssetServiceDomain;
import com.hightop.benyin.rfid.domain.service.RfidAssetTransferDetailServiceDomain;
import com.hightop.benyin.rfid.domain.service.RfidAssetTransferServiceDomain;
import com.hightop.benyin.rfid.infrastructure.entity.RfidAsset;
import com.hightop.benyin.rfid.infrastructure.entity.RfidAssetTransfer;
import com.hightop.benyin.rfid.infrastructure.entity.RfidAssetTransferDetail;
import com.hightop.benyin.rfid.infrastructure.enums.AssetChangeSource;
import com.hightop.benyin.rfid.infrastructure.enums.AssetOperatType;
import com.hightop.benyin.rfid.infrastructure.enums.TransferStatus;
import com.hightop.benyin.rfid.infrastructure.enums.TransferType;
import com.hightop.benyin.share.domain.service.SequenceDomainService;
import com.hightop.benyin.system.domain.service.LocationDomainService;
import com.hightop.benyin.system.infrastructure.entity.Location;
import com.hightop.fario.common.core.util.ExecutorUtils;
import com.hightop.fario.common.mybatis.DataGrid;
import com.hightop.fario.common.mybatis.PageQuery;
import com.hightop.fario.common.mybatis.util.PageHelper;
import com.hightop.magina.casual.session.ApplicationSessions;
import com.hightop.magina.core.exception.MaginaException;
import com.hightop.magina.standard.ums.user.bind.UserEntry;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * Rfid变更数据管理服务
 *
 * <AUTHOR>
 * @date 2024-10-12 16:36:01
 */
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Service
public class RfidAssetTransferService {

    RfidAssetTransferServiceDomain rfidAssetTransferServiceDomain;
    RfidAssetTransferDetailServiceDomain rfidAssetTransferDetailServiceDomain;
    SequenceDomainService sequenceDomainService;
    RfidAssetServiceDomain rfidAssetServiceDomain;
    LocationDomainService locationDomainService;
    ApplicationEventPublisher applicationEventPublisher;

    /**
     * Rfid变更数据分页查询
     *
     * @param pageQuery {@link PageQuery}
     * @return {@link DataGrid}
     */
    public DataGrid<RfidAssetTransfer> page(RfidAssetTransferQuery pageQuery) {
        return PageHelper.startPage(pageQuery, p ->
                this.rfidAssetTransferServiceDomain.pageList(pageQuery)
        );
    }

    /**
     * Rfid变更数据分页查询
     *
     * @param pageQuery {@link PageQuery}
     * @return {@link DataGrid}
     */
    public DataGrid<RfidAssetTransferDetail> detailPage(RfidAssetTransferDetailQuery pageQuery) {
        return PageHelper.startPage(pageQuery, p ->
                this.rfidAssetTransferDetailServiceDomain.pageList(pageQuery)
        );
    }


    /**
     * Rfid变更数据添加
     *
     * @param rfidTransferDto {@link RfidAssetTransfer}
     * @return true/false
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean saveTransfer(RfidTransferDto rfidTransferDto) {
        //保存信息
        RfidAssetTransfer rfidAssetTransfer = null;
        if (Objects.nonNull(rfidTransferDto.getId())) {
            rfidAssetTransfer = rfidAssetTransferServiceDomain.getById(rfidTransferDto.getId());
            rfidAssetTransferDetailServiceDomain.remove(Wrappers.<RfidAssetTransferDetail>lambdaQuery()
                    .eq(RfidAssetTransferDetail::getTransferCode, rfidAssetTransfer.getTransferCode()));
        } else {
            rfidAssetTransfer = new RfidAssetTransfer();
            rfidAssetTransfer.setTransferCode(sequenceDomainService.nextDateSequence(RfidAssetTransfer.SEQ, 5));
            rfidAssetTransfer.setCreatedBy(new UserEntry().setId(ApplicationSessions.id()));
        }
        rfidAssetTransfer.setTransferType(TransferType.TRANSFER);
        rfidAssetTransfer.setTransferDate(rfidTransferDto.getTransferDate());
        rfidAssetTransfer.setApplyId(rfidTransferDto.getApplyId());
        rfidAssetTransfer.setApplyName(rfidTransferDto.getApplyName());
        rfidAssetTransfer.setDepartmentId(rfidTransferDto.getDepartmentId());
        rfidAssetTransfer.setNewApplyId(rfidTransferDto.getNewApplyId());
        rfidAssetTransfer.setNewApplyName(rfidTransferDto.getNewApplyName());
        rfidAssetTransfer.setNewDepartmentId(rfidTransferDto.getNewDepartmentId());
        rfidAssetTransfer.setRemark(rfidTransferDto.getRemark());
        rfidAssetTransfer.setStatus(TransferStatus.WAIT_APPROVE);
        List<RfidAssetTransferDetail> rfidAssetTransferDetailList = Lists.newArrayList();
        for (Long assetId : rfidTransferDto.getAssetIds()) {
            RfidAssetTransferDetail rfidAssetTransferDetail = new RfidAssetTransferDetail();
            rfidAssetTransferDetail.setTransferCode(rfidAssetTransfer.getTransferCode());
            rfidAssetTransferDetail.setAssetId(assetId);
            rfidAssetTransferDetailList.add(rfidAssetTransferDetail);
        }
        rfidAssetTransferDetailServiceDomain.saveBatch(rfidAssetTransferDetailList);
        return this.rfidAssetTransferServiceDomain.saveOrUpdate(rfidAssetTransfer);
    }


    /**
     * Rfid变更数据添加
     *
     * @param rfidChangerDto {@link RfidAssetTransfer}
     * @return true/false
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean saveChange(RfidChangerDto rfidChangerDto) {
        //保存信息
        RfidAssetTransfer rfidAssetTransfer = null;
        if (Objects.nonNull(rfidChangerDto.getId())) {
            rfidAssetTransfer = rfidAssetTransferServiceDomain.getById(rfidChangerDto.getId());
            rfidAssetTransferDetailServiceDomain.remove(Wrappers.<RfidAssetTransferDetail>lambdaQuery()
                    .eq(RfidAssetTransferDetail::getTransferCode, rfidAssetTransfer.getTransferCode()));
        } else {
            rfidAssetTransfer = new RfidAssetTransfer();
            rfidAssetTransfer.setTransferCode(sequenceDomainService.nextDateSequence(RfidAssetTransfer.SEQ, 5));
            rfidAssetTransfer.setCreatedBy(new UserEntry().setId(ApplicationSessions.id()));
        }
        rfidAssetTransfer.setTransferType(TransferType.CHANGE);
        rfidAssetTransfer.setTransferDate(rfidChangerDto.getTransferDate());
        rfidAssetTransfer.setLocationId(rfidChangerDto.getLocationId());
        rfidAssetTransfer.setNewLocationId(rfidChangerDto.getNewLocationId());

        Location location = locationDomainService.getById(rfidChangerDto.getLocationId());
//        rfidAssetTransfer.setDepartmentId(location.getDepartmentId());
//        Location newLocation = locationDomainService.lambdaQuery().eq(Location::get, rfidChangerDto.getNewLocationId()).one();
//        rfidAssetTransfer.setNewDepartmentId(newLocation.getDepartmentId());

        rfidAssetTransfer.setStatus(TransferStatus.WAIT_APPROVE);
        List<RfidAssetTransferDetail> rfidAssetTransferDetailList = Lists.newArrayList();
        for (Long assetId : rfidChangerDto.getAssetIds()) {
            RfidAssetTransferDetail rfidAssetTransferDetail = new RfidAssetTransferDetail();
            rfidAssetTransferDetail.setTransferCode(rfidAssetTransfer.getTransferCode());
            rfidAssetTransferDetail.setAssetId(assetId);
            rfidAssetTransferDetailList.add(rfidAssetTransferDetail);
        }
        rfidAssetTransferDetailServiceDomain.saveBatch(rfidAssetTransferDetailList);
        return this.rfidAssetTransferServiceDomain.saveOrUpdate(rfidAssetTransfer);
    }

    public boolean approve(AssetTransferAuditDto assetTransferAuditDto) {
        RfidAssetTransfer rfidAssetTransfer = this.rfidAssetTransferServiceDomain.getById(assetTransferAuditDto.getId());
        if (Objects.isNull(rfidAssetTransfer)) {
            throw new MaginaException("单据数据不存在！");
        }
        if (!rfidAssetTransfer.getStatus().equals(TransferStatus.WAIT_APPROVE)) {
            throw new MaginaException("单据状态异常！");
        }
        rfidAssetTransfer.setAuditAt(LocalDateTime.now());
        rfidAssetTransfer.setAuditBy(ApplicationSessions.id());
        rfidAssetTransfer.setAuditName(ApplicationSessions.name());
        rfidAssetTransfer.setStatus(assetTransferAuditDto.getStatus());
        if (assetTransferAuditDto.getStatus().equals(TransferStatus.PASS)) {
            List<RfidAssetTransferDetail> rfidAssetTransferDetailList = rfidAssetTransferDetailServiceDomain.list(Wrappers.<RfidAssetTransferDetail>lambdaQuery()
                    .eq(RfidAssetTransferDetail::getTransferCode, rfidAssetTransfer.getTransferCode()));
            //变更资产
            List<AssetChangeLogDto> assetChangeLogDtos = Lists.newArrayList();
            List<RfidAsset> rfidAssets = Lists.newArrayList();
            for (RfidAssetTransferDetail rfidAssetTransferDetail : rfidAssetTransferDetailList) {
                RfidAsset rfidAsset = this.rfidAssetServiceDomain.getById(rfidAssetTransferDetail.getAssetId());
                assetChangeLogDtos.add(buildAssetChangeLogs(rfidAsset, rfidAssetTransfer));
                if (rfidAssetTransfer.getTransferType().equals(TransferType.TRANSFER)) {
                    rfidAsset.setApplyId(rfidAssetTransfer.getNewApplyId());
                    rfidAsset.setApplyName(rfidAssetTransfer.getNewApplyName());
                    rfidAsset.setApplyAt(rfidAssetTransfer.getCreatedAt());
                    rfidAsset.setDepartmentId(rfidAssetTransfer.getNewDepartmentId());
                } else {
                    rfidAsset.setLocationId(rfidAssetTransfer.getNewLocationId());
                }
                rfidAssets.add(rfidAsset);
            }
            //更新资产
            this.rfidAssetServiceDomain.updateBatchById(rfidAssets);

            //保存变更日志
            ExecutorUtils.doAfterCommit(
                    () -> applicationEventPublisher.publishEvent(new AssetChangeEvent(assetChangeLogDtos))
            );

        }
        this.rfidAssetTransferServiceDomain.updateById(rfidAssetTransfer);

        return true;
    }

    public boolean remove(Long id) {
        RfidAssetTransfer rfidAssetTransfer = this.rfidAssetTransferServiceDomain.getById(id);
        if (rfidAssetTransfer.getStatus().equals(TransferStatus.PASS)) {
            throw new MaginaException("当前状态不允许关闭！");
        }
        //删除信息
        rfidAssetTransfer.setStatus(TransferStatus.CLOSE);
        return this.rfidAssetTransferServiceDomain.updateById(rfidAssetTransfer);
    }

    public RfidAssetTransfer getByCode(String transferCode) {
        RfidAssetTransferQuery rfidAssetTransferQuery = new RfidAssetTransferQuery();
        rfidAssetTransferQuery.setTransferCode(transferCode);
        List<RfidAssetTransfer> rfidAssetTransfers = this.rfidAssetTransferServiceDomain.pageList(rfidAssetTransferQuery);
        RfidAssetTransfer rfidAssetTransfer = CollectionUtils.isEmpty(rfidAssetTransfers) ? null : rfidAssetTransfers.get(0);

        if (Objects.nonNull(rfidAssetTransfer)) {
            RfidAssetTransferDetailQuery rfidAssetTransferDetailQuery = new RfidAssetTransferDetailQuery();
            rfidAssetTransferDetailQuery.setTransferCode(transferCode);
            List<RfidAssetTransferDetail> rfidAssetTransferDetails = this.rfidAssetTransferDetailServiceDomain.pageList(rfidAssetTransferDetailQuery);
            rfidAssetTransfer.setTransferDetails(rfidAssetTransferDetails);
        }
        return rfidAssetTransfer;
    }

    /**
     * 保存变更日志
     *
     * @param rfidAsset
     * @param rfidAssetTransfer
     * @return
     */
    private AssetChangeLogDto buildAssetChangeLogs(RfidAsset rfidAsset, RfidAssetTransfer rfidAssetTransfer) {
        AssetOperatType assetOperatType = rfidAssetTransfer.getTransferType().equals(TransferType.TRANSFER) ? AssetOperatType.TRANSFER : AssetOperatType.CHANGE;
        AssetChangeLogDto assetChangeLogDto = AssetChangeLogDto.builder()
                .assetId(rfidAsset.getId())
                .changeCode(rfidAssetTransfer.getTransferCode())
                .createdBy(ApplicationSessions.id())
                .source(AssetChangeSource.TRANSFER)
                .operatType(assetOperatType)
                .build();
        if (assetOperatType.equals(AssetOperatType.CHANGE)) {
            assetChangeLogDto.setBefore(rfidAssetTransfer.getLocation());
            assetChangeLogDto.setAfter(rfidAssetTransfer.getNewLocation());
        } else {
            assetChangeLogDto.setBefore(rfidAssetTransfer.getDepartmentName() + ":" + rfidAssetTransfer.getApplyName());
            assetChangeLogDto.setAfter(rfidAssetTransfer.getNewDepartmentName() + ":" + rfidAssetTransfer.getNewApplyName());
        }
        return assetChangeLogDto;
    }

}
