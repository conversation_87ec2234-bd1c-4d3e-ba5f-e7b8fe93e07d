package com.hightop.benyin.share.infrastructure.enums;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Getter
public enum DictCodeEnums {
    /**
     * 实体物流，一般用于异地
     */
    knowledge_type("3500","23500","知识库类型");
    /**
     * 字典编号
     */
    String code;
    /**
     * 字典ID
     */
    String codeId;
    /**
     * 字典描述
     */
    String desc;
}
