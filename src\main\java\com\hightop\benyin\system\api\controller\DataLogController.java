package com.hightop.benyin.system.api.controller;

import com.hightop.benyin.system.api.vo.query.DataLogQuery;
import com.hightop.benyin.system.application.service.DataLogService;
import com.hightop.benyin.system.infrastructure.entity.DataLog;
import com.hightop.fario.base.web.RestResponse;
import com.hightop.fario.common.mybatis.DataGrid;
import com.hightop.magina.standard.behavior.operation.IgnoreOperationLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 数据操作日志管理rest接口
 *
 * <AUTHOR>
 * @date 2024-10-12 16:36:01
 */
@RequestMapping("/data-log")
@RestController
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Api(tags = "数据操作日志管理")
public class DataLogController {
    DataLogService dataLogService;

    @PostMapping
    @ApiOperation("分页查询")
    @IgnoreOperationLog
    public RestResponse<DataGrid<DataLog>> page(@RequestBody DataLogQuery pageQuery) {
        switch (pageQuery.getBusinessType()) {
            case USER:
                return RestResponse.ok(this.dataLogService.userLogPage(pageQuery));
            case DEPT:
                return RestResponse.ok(this.dataLogService.deptLogPage(pageQuery));
            case LOCATION:
                return RestResponse.ok(this.dataLogService.locationLogPage(pageQuery));
            default:
                return RestResponse.ok(this.dataLogService.readerLogPage(pageQuery));
        }
    }

}
