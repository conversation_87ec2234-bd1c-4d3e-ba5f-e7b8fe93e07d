package com.hightop.benyin.rfid.domain.event;

import com.hightop.benyin.rfid.application.vo.dto.AssetChangeLogDto;
import com.hightop.benyin.rfid.infrastructure.enums.AssetOperatType;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.List;

/**
 * 资产变更日志 事件
 *
 * <AUTHOR>
 * @date 2024/10/16 14:55
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@AllArgsConstructor
@NoArgsConstructor
public class AssetChangeEvent {

    /**
     * 资产变更日志列表
     */
    List<AssetChangeLogDto> assetChangeLogDtos;


}
