package com.hightop.benyin.system.api.vo.query;

import com.hightop.fario.common.mybatis.PageQuery;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldDefaults;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@FieldDefaults(level = AccessLevel.PRIVATE)
public class LocationQuery extends PageQuery {

    @ApiModelProperty("位置id")
    Long id;

    @ApiModelProperty("位置编码")
    String code;

    @ApiModelProperty("位置名称")
    String name;

    @ApiModelProperty("位置层级")
    Integer type;

    @ApiModelProperty("启停状态1 启用 0 停用")
    Boolean isEnable;

    @ApiModelProperty("查所有")
    Boolean isAll = false;


    @ApiModelProperty("用户id")
    Long userId;

    @ApiModelProperty("是否叶子节点")
    private Boolean isLeaf;

    @ApiModelProperty("多选关联的部门id")
    private List<Long> departmentIds;

    @ApiModelProperty("关联的部门id")
    private Long departmentId;

    @ApiModelProperty("关联的部门公司编码")
    private String companyCode;

    @ApiModelProperty("启用时间-起始")
    String startEnableDate;

    @ApiModelProperty("启用时间-终止")
    String endEnableDate;

    @ApiModelProperty("停用时间-起始")
    String startDisableDate;

    @ApiModelProperty("停用时间-终止")
    String endDisableDate;


}
