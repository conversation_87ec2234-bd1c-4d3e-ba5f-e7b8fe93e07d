package com.hightop.benyin.system.api.vo.excel;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelIgnore;
import com.hightop.benyin.share.application.vo.ExcelBaseInfo;
import com.hightop.magina.standard.ums.user.privacy.UserSex;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

@Data
@ApiModel("用户信息导入模板")
public class UserExcel extends ExcelBaseInfo {

    @Excel(name = "用户账号", width = 30, orderNum = "1")
    @Size(min = 1, max = 64, message = "名字长度在{min}至{max}之间")
    @ApiModelProperty("用户账号")
    @NotBlank
    String code;

    @Excel(name = "用户姓名", width = 30, orderNum = "2")
    @Size(min = 1, max = 64, message = "账号长度在{min}至{max}之间")
    @ApiModelProperty("用户姓名")
    @NotBlank
    String name;

    @Excel(name = "部门编码", width = 50, orderNum = "3")
    @ApiModelProperty("部门编码")
    String departmentCode;

    @Excel(name = "部门名称", width = 50, orderNum = "4")
    @ApiModelProperty("部门名称")
    String departmentName;

    @Excel(name = "位置编码", width = 50, orderNum = "5")
    @ApiModelProperty("位置编码")
    String locationCode;

    @Excel(name = "所在位置", width = 50, orderNum = "6")
    @ApiModelProperty("所在位置")
    String location;

    @Excel(name = "性别", width = 30, orderNum = "7")
    @ApiModelProperty("性别")
    String sex;

    @Excel(name = "手机号", width = 30, orderNum = "8")
    @ApiModelProperty("手机号码")
    String mobileNumber;

    @Excel(name = "身份证号", width = 30, orderNum = "9")
    @ApiModelProperty("身份证号")
    String identityCardNumber;

    @Excel(name = "邮箱", width = 30, orderNum = "10")
    @ApiModelProperty("邮箱")
    String email;

//
//    @Excel(name = "生效日期yyyy-MM-dd", width = 50, orderNum = "9" , format = "yyyy-MM-dd")
//    @ApiModelProperty("生效日期")
//    LocalDateTime createdAt;

    @ExcelIgnore
    @ApiModelProperty("部门id")
    Long departmentId;

    @ExcelIgnore
    @ApiModelProperty("位置id")
    Long locationId;


    @ExcelIgnore
    @ApiModelProperty("id")
    Long id;

    @ExcelIgnore
    @ApiModelProperty("性别枚举")
    UserSex sexEnum;

}
