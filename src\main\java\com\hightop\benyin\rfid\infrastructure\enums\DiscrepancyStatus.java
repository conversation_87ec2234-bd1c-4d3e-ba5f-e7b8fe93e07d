package com.hightop.benyin.rfid.infrastructure.enums;

import com.hightop.magina.core.custom.entry.EnumEntry;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

/**
 * 支付凭证状态
 * <AUTHOR>
 * @date 2024/5/15 13:42
 */
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Getter
public enum DiscrepancyStatus implements EnumEntry<String> {
    /**
     * 盘点中
     */
    EXECUTING("盘点中"),
    /**
     * 正常
     */
    NORMAL("正常"),
    /**
     * 盘亏-未找到
     */
    WITHOUT("盘亏"),
    /**
     * 盘盈-空标签
     */
    OVERFLOW("盘盈"),
    /**
     * 迁出-异动
     */
    CHANGE("异动");

    /**
     * 状态名称
     */
    String name;

    @Override
    public String getCode() {
        // 以枚举名称作为标识
        return super.name();
    }

    @Override
    public String getName() {
        return this.name;
    }

}
