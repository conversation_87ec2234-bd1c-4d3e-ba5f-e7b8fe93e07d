package com.hightop.benyin.share.application.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.experimental.FieldDefaults;

/**
 * cos桶信息
 * @Author: X.S
 * @date 2023/10/26 11:24
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@Accessors(chain = true)
public class CosBucket {
    @ApiModelProperty("存储桶")
    String bucket;
    /**
     * 地区码
     */
    @ApiModelProperty("地区")
    String region;
    /**
     * 存储前缀
     */
    @ApiModelProperty("存储目录前缀(前缀+文件名构成对象key)")
    String prefix;
}
