package com.hightop.benyin.rfid.application.vo.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotNull;

/**
 * @Description: 资产管理DTO
 * @Author: X.S
 * @Date: 2023/12/22 17:16
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@ApiModel("资产管理DTO")
@AllArgsConstructor
@NoArgsConstructor
public class AssetChangeDto {

    /**
     * 流水类型
     */
    @ApiModelProperty("流水类型")
    Integer inOutType;

    /**
     * 资产id
     */
    @ApiModelProperty("资产id")
    String assetCode;
    /**
     * 领用人id
     */
    @ApiModelProperty("领用人id")
    Long userId;
    /**
     * 领用人部门
     */
    @ApiModelProperty("领用人部门")
    Long departmentId;
    /**
     * 备注
     */
    @ApiModelProperty("备注")
    String remark;
}
