package com.hightop.benyin.system.api.vo.dto;

import com.hightop.benyin.system.api.vo.dto.DepartmentInfoAddDto;
import com.hightop.benyin.system.infrastructure.entity.DepartmentInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotNull;

/**
 * 部门修改dto
 *
 * <AUTHOR>
 * @date 2022/10/09 15:48
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@FieldDefaults(level = AccessLevel.PRIVATE)
public class DepartmentInfoUpdateDto extends DepartmentInfoAddDto {
    @ApiModelProperty("id")
    @NotNull(message = "id不能为空")
    Long id;

    @Override
    public DepartmentInfo toDepartment() {
        return super.toDepartment().setId(this.id);
    }
}
