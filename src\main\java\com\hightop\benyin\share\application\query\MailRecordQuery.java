package com.hightop.benyin.share.application.query;

import com.hightop.benyin.share.infrastructure.enums.MainChannel;
import com.hightop.fario.common.mybatis.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldDefaults;

import java.util.List;

/**
 * 邮件发送记录查询DTO
 * <AUTHOR>
 * @date 2024-10-12 16:36:01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@FieldDefaults(level = AccessLevel.PRIVATE)
@ApiModel("邮件发送记录查询DTO")
public class MailRecordQuery extends PageQuery {

    @ApiModelProperty("id")
    Long id;

    @ApiModelProperty("业务id")
    Long businessId;

    @ApiModelProperty("发送渠道")
    MainChannel channel;

    @ApiModelProperty("收件人ids")
    List<Long> sendTos;

    @ApiModelProperty("收件人")
    String sendTo;

    @ApiModelProperty("主题")
    String subject;

    @ApiModelProperty("发送人")
    String createdBy;

    @ApiModelProperty("登记起始时间")
    String startDate;

    @ApiModelProperty("登记截止时间")
    String endDate;

}
