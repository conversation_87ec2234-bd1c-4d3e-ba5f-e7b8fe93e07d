package com.hightop.benyin.rfid.application.vo.query;

import com.hightop.benyin.rfid.infrastructure.enums.TakeRangeEnums;
import com.hightop.fario.common.mybatis.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldDefaults;

import java.util.List;

/**
 * 资产盘点查询DTO
 *
 * <AUTHOR>
 * @date 2024-10-12 16:36:01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@FieldDefaults(level = AccessLevel.PRIVATE)
@ApiModel("资产盘点查询DTO")
public class RfidAssetTakeDetailQuery extends PageQuery {

    @ApiModelProperty("盘点编码")
    String takeCode;

    @ApiModelProperty("是否贴标签")
    Boolean hasTag;


    @ApiModelProperty("是否盘点扫描")
    Boolean isTake;

    @ApiModelProperty("是否绑定标签")
    Boolean isBind;

    @ApiModelProperty("是否绑定基站")
    Boolean isBindReader;

    @ApiModelProperty("盘点类型")
    String takeType;

    @ApiModelProperty("盘点范围")
    TakeRangeEnums takeRange;

    @ApiModelProperty("状态")
    List<String> status;


    @ApiModelProperty("盘点状态")
    List<String> takeStatus;


    @ApiModelProperty("扫描状态")
    List<String> scanStatus;

    @ApiModelProperty("处理类型")
    List<String> processType;


    @ApiModelProperty("起始时间")
    String startDate;

    @ApiModelProperty("截止时间")
    String endDate;

    @ApiModelProperty("rfid编码")
    String rfidCode;

    @ApiModelProperty("资产编码")
    String code;

    @ApiModelProperty("资产名称")
    String name;

    @ApiModelProperty("资产型号")
    String model;

    @ApiModelProperty("设备id")
    String deviceId;

    @ApiModelProperty("基站id")
    Long readerId;

    @ApiModelProperty("使用状态")
    List<String> useState;

    @ApiModelProperty("资产类型")
    List<String> assetType;

    @ApiModelProperty("保管人")
    List<Long> applyIds;

    @ApiModelProperty("部门")
    List<Long> departmentIds;

    @ApiModelProperty("责任人")
    List<Long> managerIds;

    @ApiModelProperty("责任部门")
    List<Long> managerDeptIds;

    @ApiModelProperty("位置")
    List<Long> locations;
}
