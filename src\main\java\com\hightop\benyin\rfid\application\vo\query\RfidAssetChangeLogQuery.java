package com.hightop.benyin.rfid.application.vo.query;

import com.baomidou.mybatisplus.annotation.TableField;
import com.hightop.benyin.rfid.infrastructure.enums.AssetChangeSource;
import com.hightop.benyin.rfid.infrastructure.enums.AssetOperatType;
import com.hightop.fario.common.mybatis.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldDefaults;

import java.util.List;

/**
 * RFID基站查询DTO
 *
 * <AUTHOR>
 * @date 2024-10-12 16:36:01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@FieldDefaults(level = AccessLevel.PRIVATE)
@ApiModel("RFIDy变更日志查询DTO")
public class RfidAssetChangeLogQuery extends PageQuery {

    @ApiModelProperty("资产ID")
    Long assetId;

    @ApiModelProperty("编码")
    String rfidCode;

    @ApiModelProperty("资产名称")
    String assetName;

    @ApiModelProperty("资产编码")
    String assetCode;

    @ApiModelProperty("基站编码")
    String readerCode;

    @ApiModelProperty("所属单位")
    String departmentName;

    @ApiModelProperty("位置")
    List<String> locations;

    @ApiModelProperty("变更类型")
    String operationType;

    @ApiModelProperty("变更来源")
    String source;

    @ApiModelProperty("变更单号")
    String changeCode;

    @ApiModelProperty("变更内容")
    String content;

    @ApiModelProperty("开始日期")
    String startDate;
    @ApiModelProperty("结束日期")
    String endDate;

}
