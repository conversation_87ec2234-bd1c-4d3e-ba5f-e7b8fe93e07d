package com.hightop.benyin.share.application.service;

import com.github.yulichang.toolkit.MPJWrappers;
import com.hightop.benyin.share.infrastructure.enums.DictCodeEnums;
import com.hightop.magina.standard.code.dictionary.api.DictItemVo;
import com.hightop.magina.standard.code.dictionary.dict.Dict;
import com.hightop.magina.standard.code.dictionary.dict.DictDomainService;
import com.hightop.magina.standard.code.dictionary.item.DictItem;
import com.hightop.magina.standard.code.dictionary.item.DictItemDomainService;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * 字典扩展服务
 *
 * @Author: X.S
 * @Date: 2023/12/6 15:11
 */
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Service
public class DictExtendService {
    DictDomainService dictDomainService;
    DictItemDomainService dictItemDomainService;

    /**
     * 根据code 获取value对应的每个 标签
     */
    public List<DictItemVo> getCodeMatchValueLabels(DictCodeEnums enums, Set<String> values) {
        Dict dict = this.dictDomainService.getByCode(enums.getCode());
        if (Objects.isNull(dict)) {
            return new ArrayList<>();
        }
        if (Objects.isNull(values)) {
            return new ArrayList<>();
        }
        return this.dictItemDomainService.selectJoinList(
                DictItemVo.class,
                MPJWrappers.<DictItem>lambdaJoin()
                        .select(DictItem::getValue, DictItem::getLabel)
                        .in(DictItem::getValue,values)
        );
    }

    /**
     * 字典项查询所属子集
     *
     * @param dictCode
     * @param itemValue
     * @return: {@link List< DictItemVo>}
     * @Author: X.S
     * @Date: 2023/12/6 15:42
     */
    public List<DictItemVo> itemChildrenList(String dictCode, String itemValue) {
        Dict dict = this.dictDomainService.getByCode(dictCode);
        if (Objects.isNull(dict)) {
            return new ArrayList<>();
        }
        DictItem dictItem = this.dictItemDomainService.lambdaQuery()
            .eq(DictItem::getDictId, dict.getId())
            .eq(DictItem::getValue, itemValue)
            .one();
        if (Objects.isNull(dictItem)) {
            return new ArrayList<>();
        }
        return this.dictItemDomainService.selectJoinList(
            DictItemVo.class,
            MPJWrappers.<DictItem>lambdaJoin()
                .select(DictItem::getValue, DictItem::getLabel)
                .eq(DictItem::getParentId, dictItem.getId())
                .eq(DictItem::getIsEnable, true)
                .orderByAsc(DictItem::getSort)
                .orderByAsc(DictItem::getValue)
        );
    }
}
