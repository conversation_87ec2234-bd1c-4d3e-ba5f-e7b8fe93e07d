package com.hightop.benyin.share.socket.util;

import com.google.common.collect.Lists;
import com.hightop.benyin.share.socket.common.SocketMessage;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

@Slf4j
public class MsgUtil {

    /**
     * RFID字符串长度
     */
    public static final int RFID_STR_LENGTH = 24;

    /**
     * RFID十六进制字符串长度
     */
    public static final int RFID_HEX_LENGTH = 12;

    /**
     * 协议头
     */
    public static final int HEAD_ONE = 0xFE;
    /**
     * 协议头
     */
    public static final int HEAD_TWO = 0xEF;

    /**
     * 协议头字符串
     */
    public static final String HEAD_STR = "FEEF";

    /**
     * 回执消息
     */
    public static final String RESULT_SUCCESS = "01";
    public static final String scan_end = "14";


    /**
     * 校验消息
     *
     * @param data
     * @return
     */
    public static boolean validateMessage(byte[] data, String message) {
        if (data.length < 5) {
            log.error("格式错误:{}", message);
            return false;
        }
        if (!message.startsWith(HEAD_STR)) {
            log.error("协议头错误:{}", message);
            return false;
        }
        return true;
    }

    /**
     * 获取SocketMessage列表
     *
     * @param data
     * @param msgStr
     * @return
     */
    public static List<SocketMessage> getSocketMessageList(byte[] data, String msgStr) {
        List<SocketMessage> messageList = Lists.newArrayList();
        log.info("消息长度:{}", data.length);
        for (int i = 4; i < data.length; ) {
            int headOne = MsgUtil.convertToHex(data[i - 4]);
            int headTwo = MsgUtil.convertToHex(data[i - 3]);
            byte[] bodylength = new byte[2];
            bodylength[0] = data[i - 1];
            bodylength[1] = data[i];
            int length = MsgUtil.convertToHex(bodylength[0]) * 256 + MsgUtil.convertToHex(bodylength[1]);
            if (headOne == 0xFE && headTwo == 0xEF) {
                byte[] firstdata = new byte[length + 5];
                int k = i - 4;
                for (int j = 0; j < 5 + length; j++) {
                    firstdata[j] = data[k++];
                }
                SocketMessage socketMessage = new SocketMessage(firstdata);
                messageList.add(socketMessage);
                i = i + length + 5;
            } else {
                i++;
            }
        }
        return messageList;
    }

    /**
     * 将字符串消息按照指定长度分割成List
     *
     * @param msg
     * @param length
     * @return
     */
    public static List<String> splitString(String msg, int length) {
        List<String> result = new ArrayList<>();
        for (int i = 0; i < msg.length(); i += length) {
            result.add(msg.substring(i, Math.min(i + length, msg.length())));
        }
        return result;
    }

    /**
     * 将16进制字符串转换为byte数组
     *
     * @param msg
     * @return
     */
    public static byte[] convertMessage(String msg) {
        byte[] byteArray = new byte[msg.length() / 2];
        for (int i = 0; i < byteArray.length; i++) {
            // 提取每两个字符组成的子字符串
            String substring = msg.substring(i * 2, i * 2 + 2);
            byteArray[i] = (byte) Integer.parseInt(substring, 16); // 以16进制解析
        }
        return byteArray;
    }

    /**
     * 将byte数组转换为16进制字符串
     *
     * @param data
     * @return
     */
    public static String convertMessage(byte[] data) {
        StringBuilder hexString = new StringBuilder();
        for (byte b : data) {
            hexString.append(String.format("%02X", b));
        }
        return hexString.toString();
    }

    /**
     * 将16进制字符串转换为10进制整数
     *
     * @param hexString
     * @return
     */
    public static Integer stringToInteger(String hexString) {
        return Integer.parseInt(hexString, 16);
    }

    /**
     * 将10进制整数转换16进制字符串
     *
     * @param decimal
     * @return
     */
    public static String integerToHex(Integer decimal) {
        return Integer.toHexString(decimal);
    }

    /**
     * 十六进制2位
     *
     * @param decimal
     * @return
     */
    public static String integerToHexTwo(Integer decimal) {
        String hex = Integer.toHexString(decimal).toUpperCase();
        // 如果hex长度不足两位，用0填充
        hex = hex.length() < 2 ? "0" + hex : hex;
        return hex;
    }

    /**
     * 将字符串长度转换为四位16进制字符串
     * @param number
     * @return
     */
    public static String converFourToHex(Integer number) {
        String hexString = Integer.toHexString(number);
        if (hexString.length() < 4) {
            while (hexString.length() < 4) {
                hexString = "0" + hexString;
            }
        }
        return hexString;
    }

    /**
     * 十六进制4位
     *
     * @param value
     * @return
     */
    public static String intToHex(int value) {
        // 提取 2 个字节
        byte byte1 = (byte) (value >> 8); // 高字节
        byte byte2 = (byte) (value);       // 低字节

        // 格式化字节为 16 进制字符串
        String hex = String.format("%02X%02X", byte1 & 0xFF, byte2 & 0xFF);
        return hex;
    }

    /**
     * 十六进制4位
     *
     * @param fourLengStrHex
     * @return
     */
    public static Integer stringHexToInt(String fourLengStrHex) {
        byte[] bytes = MsgUtil.convertMessage(fourLengStrHex);
        return bytes[0]  * 256+ bytes[1];
    }

    /**
     * 将byte数组转换为16进制字符串
     *
     * @param b
     * @return
     */
    public static int convertToHex(byte b) {
        return Integer.parseInt(String.format("%02X", b), 16);
    }


    /**
     * 将16进制字符串转换为字符串
     * @param hex
     * @return
     */
    public static String hexToString(String hex) {
        StringBuilder sb = new StringBuilder();

        for (int i = 0; i < hex.length(); i += 2) {
            String str = hex.substring(i, i + 2);
            int decimal = Integer.parseInt(str, 16);
            sb.append((char) decimal);
        }

        return sb.toString();
    }

    public static void main(String[] args) {
//        String w = "FFFF";
//        BigInteger bigInteger = new BigInteger(w, 16);
//        System.out.println("转换后的数字: " + bigInteger.toString());
//
//        String hex = MsgUtil.intToHex(0);
//        System.out.println(hex);
//
//        List<String> list = Lists.newArrayList("123","345");
//        String data = String.join("", list);
//        byte[] head = new byte[2];
//        head[0] = (byte) 0x00;
//        head[1] = (byte) 0x0C;
//        int  length = head[0]*256 + head[1];
//        System.out.println(length);
//
//        String msgStr = "FEEFB20024E280699500004003751728B4E280699500004003751728F0E28069950000500375173A73FEEFB20018E28069950000500375183E03E28069950000500083338426";
//
//        byte[] data = MsgUtil.convertMessage(msgStr);
//        List<SocketMessage> strList = MsgUtil.getSocketMessageList(data, msgStr);
//        strList.forEach(v -> {
//            log.info("消息{}", v.toString());
//        });
//
//        int number = 254;
//        String hexString = Integer.toHexString(number);
//
//        if (hexString.length() < 4) {
//            while (hexString.length() < 4) {
//                hexString = "0" + hexString;
//            }
//        }

//        byte[] bodylength = new byte[2];
//        bodylength[0] = (byte) 0x00;
//        bodylength[1] =(byte) 0x90;
//        int length = MsgUtil.convertToHex(bodylength[0]) * 256 + MsgUtil.convertToHex(bodylength[1]);

//        String params="01FEDCBA000000000000000091FEDCBA000000000000000092FEDCBA000000000000000093000202FEDCBA000000000000000089FEDCBA000000000000000090000103FEDCBA000000000000000087";
//       int len = 3;
//        Integer firstParamIndex = len*MsgUtil.RFID_STR_LENGTH;
//        String type = params.substring(0,2);
//        if(len>0){
//            String rfidStr = params.substring(2,firstParamIndex+2);
//            List<String> rfidList = MsgUtil.splitString(rfidStr, MsgUtil.RFID_STR_LENGTH);
//            log.info("type:{},数量{}", type,rfidList.size());
//        }
//
//        //处理2层数据
//        String lengthStr =  params.substring(firstParamIndex+2,firstParamIndex+6);
//        byte[] lenthBytes = MsgUtil.convertMessage(lengthStr);
//        int  length2 = lenthBytes[0]  * 256+ lenthBytes[1];
//        if(length2>0){
//            String type2 =  params.substring(firstParamIndex+6,firstParamIndex+8);
//            int endIndex = firstParamIndex+8+(length2*RFID_STR_LENGTH);
//            String secendParam =  params.substring(firstParamIndex+8,endIndex);
//            List<String> rfidLists = MsgUtil.splitString(secendParam, MsgUtil.RFID_STR_LENGTH);
//            log.info("type:{},数量{}", type2,rfidLists.size());
//
//        }
//        //处理3层数据
//        int firstParamIndex3 =firstParamIndex+8+(length2*RFID_STR_LENGTH);
//        String length3Str =  params.substring(firstParamIndex3,firstParamIndex3+4);
//        int  length3 = lenthBytes[0]  * 256+ lenthBytes[1];
//
//        if(length3>0){
//            String type3 =  params.substring(firstParamIndex3+4,firstParamIndex3+6);
//            String secendParam =  params.substring(firstParamIndex3+6,params.length());
//            List<String> rfidLists = MsgUtil.splitString(secendParam, MsgUtil.RFID_STR_LENGTH);
//            log.info("type:{},数量{}", type3,rfidLists.size());
//
//        }
        String str="0004";

        Integer code = MsgUtil.stringHexToInt(str);
        System.out.println(code);

    }

}
