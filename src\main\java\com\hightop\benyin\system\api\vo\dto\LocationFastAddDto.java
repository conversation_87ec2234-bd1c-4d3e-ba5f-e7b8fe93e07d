package com.hightop.benyin.system.api.vo.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@ApiModel("快速添加位置")
public class LocationFastAddDto {

    @ApiModelProperty("类型1楼栋2楼层3房间")
    @NotNull(message = "位置类型不能为空")
    Integer type;

    @ApiModelProperty("数量")
    @NotEmpty(message = "数量")
    List<LocationAddDto> locations;

    @ApiModelProperty("上级id")
    Long parentId;
}
