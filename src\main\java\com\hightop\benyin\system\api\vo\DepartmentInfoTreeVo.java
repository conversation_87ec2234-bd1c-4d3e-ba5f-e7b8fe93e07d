package com.hightop.benyin.system.api.vo;

import com.hightop.benyin.system.infrastructure.entity.DepartmentInfo;
import com.hightop.magina.core.custom.entry.TreeEntry;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldDefaults;

import java.util.List;

/**
 * 部门管理树节点
 *
 * <AUTHOR>
 * @date 2022/09/13 21:17
 * @since 2.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@FieldDefaults(level = AccessLevel.PRIVATE)
public class DepartmentInfoTreeVo extends DepartmentInfo implements TreeEntry<Long, DepartmentInfoTreeVo> {

    @ApiModelProperty("子部门")
    List<DepartmentInfoTreeVo> children;
}
