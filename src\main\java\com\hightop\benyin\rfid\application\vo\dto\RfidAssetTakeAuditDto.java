package com.hightop.benyin.rfid.application.vo.dto;

import com.hightop.benyin.rfid.infrastructure.enums.DiscrepancyStatus;
import com.hightop.benyin.rfid.infrastructure.enums.TakeTypeEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * 盘点DTO
 *
 * @Author: X.S
 * @date 2024/09/29 13:58
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@ApiModel("盘点DTO")
public class RfidAssetTakeAuditDto {

    @ApiModelProperty(value = "审核状态")
    @NotNull(message = "审核状态不能为空")
    DiscrepancyStatus status;

    @ApiModelProperty(value = "id")
    @NotNull(message = "请选择盘点记录")
    Long id;

}