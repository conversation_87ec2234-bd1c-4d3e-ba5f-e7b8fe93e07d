package com.hightop.benyin.share.socket.service.impl;

import com.github.yulichang.toolkit.SpringContentUtils;
import com.hightop.benyin.rfid.domain.service.RfidAssetServiceDomain;
import com.hightop.benyin.rfid.domain.service.RfidReaderServiceDomain;
import com.hightop.benyin.rfid.infrastructure.entity.RfidReader;
import com.hightop.benyin.rfid.infrastructure.enums.ReaderStatus;
import com.hightop.benyin.share.domain.event.ApiLogEvent;
import com.hightop.benyin.share.infrastructure.enums.MessageType;
import com.hightop.benyin.share.socket.enums.CommandType;
import com.hightop.benyin.share.socket.enums.ResultEnums;
import com.hightop.benyin.share.socket.service.CommandHandler;
import com.hightop.benyin.share.socket.util.MsgUtil;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

/**
 * 设置WIFI和服务器的参数-回执处理
 */
@Service
@Slf4j
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class SetNetSettingsHandler implements CommandHandler {
    RfidReaderServiceDomain rfidReaderServiceDomain;
    RfidAssetServiceDomain rfidAssetServiceDomain;

    @Override
    public void handle(String clientName, String deviceId, Integer length, String params) {
        log.info("设置WIFI和服务器的参数-回执处理, clientName: {}, 结果: {}", clientName, ResultEnums.getName(MsgUtil.stringToInteger(params)));

    }
}
