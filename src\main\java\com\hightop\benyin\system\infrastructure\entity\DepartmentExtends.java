package com.hightop.benyin.system.infrastructure.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.hightop.benyin.configurer.annotation.RecordLog;
import com.hightop.benyin.configurer.annotation.RecordLogField;
import com.hightop.benyin.configurer.enums.DataType;
import com.hightop.benyin.system.infrastructure.enums.BusinessType;
import com.hightop.magina.standard.cipher.CipherText;
import com.hightop.magina.standard.ums.department.Department;
import com.hightop.magina.standard.ums.user.bind.UserBind;
import com.hightop.magina.standard.ums.user.bind.UserEntry;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.experimental.FieldDefaults;
import net.sf.jsqlparser.expression.Alias;
import net.sf.jsqlparser.schema.Table;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Comparator;
import java.util.Objects;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@Accessors(chain = true)
@TableName(DepartmentExtends.TABLE_NAME)
@ApiModel
@RecordLog(businessType = BusinessType.DEPT)
public class DepartmentExtends implements Serializable {
    @TableId(value = Department.ID_FIELD, type = IdType.NONE)
    @ApiModelProperty("id")
    Long id;

    @TableField("parent_id")
    @RecordLogField(value = "上级部门",dataType = DataType.DEPT)
    @ApiModelProperty("父id")
    Long parentId;

    @TableField("name")
    @ApiModelProperty("名称")
    @RecordLogField(value = "部门名称")
    CipherText name;

    @TableField("code")
    @RecordLogField(value = "部门名称")
    @ApiModelProperty("编码")
    CipherText code;

    @TableField("manager_id")
    @RecordLogField(value = "负责人",dataType = DataType.USER)
    @ApiModelProperty("负责人id")
    Long  managerId;

    @TableField("dept_type")
    @ApiModelProperty("部门类型")
    Integer deptType;

    @TableField(exist = false)
    @ApiModelProperty("部门负责人")
    String  managerName;

    @TableField("is_enable")
    @ApiModelProperty("启停状态")
    @RecordLogField(value = "启停状态",dataType = DataType.ENABLE)
    Boolean isEnable;

    @TableField("sort")
    @ApiModelProperty("排序号")
    Integer sort;

    /**
     * 是否可用 类似逻辑删除的状态标识字段
     */
    @TableField("is_available")
    @JsonIgnore
    Boolean isAvailable;

    @TableField(value = "expire_at", updateStrategy = FieldStrategy.IGNORED)
    @ApiModelProperty("失效时间")
    LocalDateTime expireAt;

    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @ApiModelProperty("创建时间")
    LocalDateTime createdAt;

    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty("更新时间")
    LocalDateTime updatedAt;

    @TableField(value = "updated_by")
    @ApiModelProperty("更新人")
    @UserBind
    UserEntry updatedBy;
    /**
     * id全路径
     */
    @TableField(Department.FULL_ID_PATH_FIELD)
    @JsonIgnore
    String fullIdPath;
    /**
     * 部门树全路径
     */
    @TableField(exist = false)
    String departmentPath;
    /**
     * 顶级节点父id
     */
    public static final Long TOP = 0L;
    /**
     * 默认比较器
     */
    public static final Comparator<DepartmentExtends> COMPARATOR =
            // 先按照排序号排序再按照编码排序
            Comparator.comparingInt(DepartmentExtends::getSort).thenComparing(it -> it.getCode().getValue());
    /**
     * id字段名称
     */
    public static final String ID_FIELD = "id";
    /**
     * id路径字段名称
     */
    public static final String FULL_ID_PATH_FIELD = "full_id_path";
    /**
     * 表名称
     */
    static final String TABLE_NAME = "st_department";

    /**
     * jsql部门表名
     *
     * @param alias  别名
     * @param withAs 是否要as关键字
     * @return {@link Table}
     */
    public static Table table(String alias, boolean withAs) {
        return new Table().withName(DepartmentExtends.TABLE_NAME).withAlias(new Alias(alias, withAs));
    }

    /**
     * 是否是不可用的部门
     *
     * @param department {@link Department}
     * @return true/false
     */
    public static boolean isDisable(Department department) {
        return Department.isNotExists(department) || !department.getIsEnable();
    }

    /**
     * 是否是不存在的部门
     *
     * @param department {@link Department}
     * @return true/false
     */
    public static boolean isNotExists(Department department) {
        return Objects.isNull(department) || !department.getIsAvailable();
    }


}
