package com.hightop.benyin.rfid.application.vo.dto;

import com.hightop.benyin.rfid.infrastructure.enums.TransferStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotNull;

/**
 * @Description: 资产变更审核DTO
 * @Author: X.S
 * @Date: 2023/12/22 17:16
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@ApiModel("资产变更审核DTO")
public class AssetTransferAuditDto {

    @ApiModelProperty("审核状态")
    @NotNull(message = "审核状态不能为空")
    TransferStatus status;

    @ApiModelProperty("id")
    @NotNull(message = "审核单据不能为空")
    Long id;

}
