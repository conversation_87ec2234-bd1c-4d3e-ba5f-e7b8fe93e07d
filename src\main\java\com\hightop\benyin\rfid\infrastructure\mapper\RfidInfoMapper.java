package com.hightop.benyin.rfid.infrastructure.mapper;

import com.github.yulichang.base.MPJBaseMapper;
import com.hightop.benyin.rfid.application.vo.query.RfidInfoQuery;
import com.hightop.benyin.rfid.infrastructure.entity.RfidInfo;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * RFID管理mapper
 *
 * <AUTHOR>
 * @date 2024-10-12 16:36:01
 */
public interface RfidInfoMapper extends MPJBaseMapper<RfidInfo> {

    /**
     * 获取扫描列表
     *
     * @return
     */
    public List<RfidInfo> getScanList(@Param("qo") RfidInfoQuery query);

    @Delete("TRUNCATE TABLE b_rfid_info")
    void clear();
}
