package com.hightop.benyin.system.api.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelIgnore;
import com.hightop.magina.standard.cipher.CipherText;
import com.hightop.magina.standard.ums.user.basic.UserState;
import com.hightop.magina.standard.ums.user.basic.UserType;
import com.hightop.magina.standard.ums.user.privacy.UserSex;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.time.LocalDateTime;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class UserManageExtendVo {
    @ExcelIgnore
    @ApiModelProperty("用户id")
    Long id;

    @ApiModelProperty("用户账号")
    @Excel(name = "用户账号", width = 30, orderNum = "1")
    String code;
    @ApiModelProperty("用户姓名")
    @Excel(name = "用户姓名", width = 30, orderNum = "2")
    String name;

    @ApiModelProperty("部门名称")
    @Excel(name = "部门名称", width = 30, orderNum = "4")
    String departmentName;

    @ApiModelProperty("部门编码")
    @Excel(name = "部门编码", width = 30, orderNum = "3")
    String departmentCode;


    @ApiModelProperty("位置编码")
    @Excel(name = "位置编码", width = 30, orderNum = "5")
    String locationCode;

    @ApiModelProperty("所在位置")
    @Excel(name = "所在位置", width = 30, orderNum = "6")
    String location;

    @ApiModelProperty("性别")
    @ExcelIgnore
    UserSex sex;

    @ApiModelProperty("性别")
    @Excel(name = "性别", width = 30, orderNum = "7")
    String sexStr;

    @ApiModelProperty("手机号")
    @ExcelIgnore
    CipherText mobileNumber;

    @ApiModelProperty("手机号")
    @Excel(name = "手机号", width = 30, orderNum = "8")
    String mobileNumberStr;

    @ApiModelProperty("身份证号码")
    @ExcelIgnore
    CipherText identityCardNumber;

    @ApiModelProperty("身份证号码")
    @Excel(name = "身份证号码", width = 30, orderNum = "9")
    String identityCardNumberStr;

    @ApiModelProperty("邮箱")
    @ExcelIgnore
    CipherText email;

    @ApiModelProperty("邮箱")
    @Excel(name = "邮箱", width = 30, orderNum = "10")
    String emailStr;

    @ApiModelProperty("部门")
    Long departmentId;

    @ApiModelProperty(value = "所在位置", required = true)
    Long locationId;

    @ExcelIgnore
    @ApiModelProperty("用户类型")
    UserType type;

    @ExcelIgnore
    @ApiModelProperty("用户状态")
    UserState state;

    @ApiModelProperty("账户启停状态")
    @Excel(name = "启停状态", width = 30, orderNum = "11", replace = {"启用_true", "停用_false"})
    Boolean isAvailable;


    @ApiModelProperty("启用时间")
    @Excel(name = "启用时间", width = 30, orderNum = "12", format = "yyyy/MM/dd HH:mm:ss")
    LocalDateTime createdAt;

    @ApiModelProperty("停用时间")
    @Excel(name = "停用时间", width = 30, orderNum = "13", format = "yyyy/MM/dd HH:mm:ss")
    LocalDateTime accountExpireAt;

    @ExcelIgnore
    @ApiModelProperty("是否内置账号")
    Boolean isBuildIn;

    @ApiModelProperty("最后登录时间")
    LocalDateTime lastLoginAt;
    /**
     * 更新人
     */
    @ExcelIgnore
    @ApiModelProperty("更新人")
    String updatedByName;

    /**
     * 更新时间
     */
    @ExcelIgnore
    @ApiModelProperty("更新时间")
    LocalDateTime updatedAt;

    public String getEmailStr() {
        return email == null ? null : email.getValue();
    }

    public String getMobileNumberStr() {
        return mobileNumber == null ? null : mobileNumber.getValue();
    }

    public String getIdentityCardNumberStr() {
        return identityCardNumber == null ? null : identityCardNumber.getValue();
    }

    public String getSexStr() {
        return sex == null ? null : sex.getName();
    }
}
