package com.hightop.benyin.system.api.vo;

import com.hightop.benyin.system.infrastructure.entity.Location;
import com.hightop.magina.core.custom.entry.TreeEntry;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class LocationVo extends Location implements TreeEntry<Long, LocationVo> {

    @ApiModelProperty("子部门")
    private List<LocationVo> children;

    @ApiModelProperty("关联的部门完整路径")
    private List<String> departmentFullPaths;

    @ApiModelProperty("关联的部门id")
    private List<Long> departIds;

    @Override
    public Integer getSort() {
        return super.getSort();
    }
}
