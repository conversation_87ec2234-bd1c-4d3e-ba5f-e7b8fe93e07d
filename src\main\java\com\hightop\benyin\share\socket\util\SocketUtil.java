package com.hightop.benyin.share.socket.util;

import com.github.yulichang.toolkit.SpringContentUtils;
import com.hightop.benyin.rfid.application.service.RfidReaderService;
import com.hightop.benyin.rfid.infrastructure.enums.ReaderStatus;
import com.hightop.benyin.share.infrastructure.enums.MessageType;
import com.hightop.benyin.share.socket.common.ConnectException;
import com.hightop.benyin.share.socket.common.SocketMessage;
import com.hightop.benyin.share.socket.enums.ErrorType;
import com.hightop.benyin.share.socket.common.SocketStation;
import com.hightop.magina.core.component.ApplicationContexts;
import com.hightop.magina.core.exception.MaginaException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;

import java.net.Socket;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
public class SocketUtil {

    //保存所有连接通讯类
    public static ConcurrentHashMap<String, SocketStation> COLLECT_MAP = new ConcurrentHashMap<>();


    /**
     * @description: 获取基站连接对象
     * <AUTHOR>
     * @date 2023/6/19 16:51
     * @version 1.0
     */
    public static SocketStation getSocketStation(String deviceId) {
        return COLLECT_MAP.values().stream().filter(v -> {
            return v.getDeviceId() != null && v.getDeviceId().equals(deviceId);
        }).findFirst().orElse(null);
    }


    /**
     * @description: 获取基站连接对象
     * <AUTHOR>
     * @date 2023/6/19 16:51
     * @version 1.0
     */
    public static Boolean isConnect(String deviceId) {
        SocketStation socketStation = SocketUtil.getSocketStation(deviceId);
        if (socketStation != null) {
            return socketStation.getIsConnect();
        }
        return false;
    }


    /**
     * @description: 给基站设备设备id
     * <AUTHOR>
     * @date 2023/6/19 16:51
     * @version 1.0
     */
    public static void bindDevice(String client, String deviceId) {
        if (!COLLECT_MAP.containsKey(client)) {
            log.error("{}未连接，无法绑定设备id！", client);
            return;
        }
        COLLECT_MAP.get(client).setDeviceId(deviceId);
    }

    /**
     * @description: 给所有连接发送业务消息
     * <AUTHOR>
     * @date 2023/6/19 16:51
     * @version 1.0
     */
    public static boolean sendToALL(SocketMessage msg) {
        for (String client : COLLECT_MAP.keySet()) {
            SocketStation socketStation = COLLECT_MAP.get(client);
            send(socketStation, msg);
        }
        return true;
    }


    /**
     * @description: 给所有连接发送业务消息
     * <AUTHOR>
     * @date 2023/6/19 16:51
     * @version 1.0
     */
    public static boolean sendToAssign(List<String> deviceIds, SocketMessage msg) {
        for (String deviceId : deviceIds) {
            send(deviceId, msg);
        }
        return true;
    }


    /**
     * @description: 给某个ip发送无回调消息
     * <AUTHOR>
     * @date 2023/6/19 16:53
     * @version 1.0
     */
    public static boolean sendScan(String deviceId, SocketMessage msg) {
        return send(deviceId, msg);
    }


    /**
     * @description: 给某个ip发送
     * <AUTHOR>
     * @date 2023/6/19 16:53
     * @version 1.0
     */
    public static boolean send(String deviceId, SocketMessage msg) {
        int retries = 0;
        boolean result = false;
        SocketStation socketStation = null;
        while (retries < 5) {
            try {
                socketStation = COLLECT_MAP.entrySet().stream().filter(entry -> {
                    return entry.getValue().getDeviceId() != null && entry.getValue().getDeviceId().equals(deviceId);
                }).findFirst().map(entry -> entry.getValue()).orElse(null);
                if (socketStation != null) {
                    result = true;
                    break;
                }
                result = false;
                Thread.sleep(2000);
            } catch (Exception e) {
                e.printStackTrace();
                result = false;
            }
            retries++;
        }
        if (socketStation == null) {
            throw new MaginaException("设备异常，请检查！");
        }
        return send(socketStation, msg);
    }


    /**
     * @description: 给某个ip发送
     * <AUTHOR>
     * @date 2023/6/19 16:53
     * @version 1.0
     */
    public static boolean send(SocketStation socketStation, SocketMessage msg) {
        int retries = 0;
        Boolean result = false;
        while (retries < 5) {
            try {
                socketStation.send(msg);
                result = true;
                break;
            } catch (Exception e) {
                e.printStackTrace();
                result = false;
            }
            retries++;
        }
        if (!result) {
            ApplicationContext applicationContext = SpringContentUtils.getApplicationContext();
            if (socketStation == null) {
                log.error("{}未连接!", socketStation.getDeviceId());
                // 发布日志保存事件
                applicationContext.publishEvent(msg.getApiLogEvent(socketStation.getDeviceId(), MessageType.SEND, false, ErrorType.CONNECT_ERROR));
            } else {
                applicationContext.publishEvent(msg.getApiLogEvent(socketStation.getDeviceId(), MessageType.SEND, false, ErrorType.UNKNOWN));
            }
        }
        return result;
    }

    /**
     * @description: 获取当前连接数据
     * @return: void
     * <AUTHOR>
     * @date: 2023/6/19
     */
    public static int getConnectSize() {
        return COLLECT_MAP.size();
    }

    /**
     * @description: 获取当前所有连接的Ip地址
     * @param:
     * @return: void
     * <AUTHOR>
     * @date: 2023/6/19
     */
    public static Set<String> getConnectIpAddress() {
        return COLLECT_MAP.keySet();
    }

    /**
     * @description: 添加连接对象
     * <AUTHOR>
     * @date 2023/6/19 15:14
     * @version 1.0
     */
    public static void stationConnect(SocketStation socketStation, int maxConnectSize) throws Exception {
        Socket socket = socketStation.getSocket();
        String client = socket.getInetAddress().getHostAddress() + ":" + socket.getPort();
        //判断是否超出最大连接数量，超出后断开连接
        if (maxConnectSize > COLLECT_MAP.size()) {
            SocketStation oriSocketStation = COLLECT_MAP.get(client);
            if (oriSocketStation != null) {
                try {
                    oriSocketStation.disconnect();
                } catch (Exception e) {
                    e.printStackTrace();
                }
                COLLECT_MAP.remove(client);
            }
            socketStation.setIsConnect(true);
            COLLECT_MAP.put(client, socketStation);
        } else {
            socket.close();
            throw new ConnectException(maxConnectSize);
        }
    }

    /**
     * @description: 移除连接对象
     * <AUTHOR>
     * @date 2023/6/19 15:14
     * @version 1.0
     */
    public static void breakStation(String client) {
        SocketStation socketStation = COLLECT_MAP.get(client);
        RfidReaderService rfidReaderService = ApplicationContexts.getBean(RfidReaderService.class);
        if (rfidReaderService != null) {
            rfidReaderService.setCollectState(socketStation.getDeviceId(), ReaderStatus.BREAKDOWN);
        }
        if (socketStation != null) {
            try {
                log.info("移除连接对象:{}", client);
                socketStation.disconnect();
                COLLECT_MAP.remove(client);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * @description: 移除所有连接对象
     * <AUTHOR> @date 2023/6/19 15:14
     * @version 1.0
     */
    public static void breakAllStation() {
        for (String ipAddress : COLLECT_MAP.keySet()) {
            breakStation(ipAddress);
        }
        RfidReaderService rfidReaderService = ApplicationContexts.getBean(RfidReaderService.class);
        if (rfidReaderService != null) {
            rfidReaderService.setAllClose();
        }
    }

//    public static void send(String client, String message) {
//        Socket socket = SocketUtil.getSocket(client);
//        try {
//            socket.getOutputStream().write(message.getBytes());
//        } catch (IOException e) {
//            log.error("{}", e.getMessage());
//        }
//    }
}
