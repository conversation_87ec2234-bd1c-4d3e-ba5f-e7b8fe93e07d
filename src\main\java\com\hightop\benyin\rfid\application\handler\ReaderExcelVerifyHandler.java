package com.hightop.benyin.rfid.application.handler;

import cn.afterturn.easypoi.excel.entity.result.ExcelVerifyHandlerResult;
import cn.afterturn.easypoi.handler.inter.IExcelVerifyHandler;
import com.hightop.benyin.rfid.application.vo.excel.Readerxcel;
import com.hightop.benyin.rfid.domain.service.RfidReaderServiceDomain;
import com.hightop.benyin.rfid.infrastructure.entity.RfidReader;
import com.hightop.benyin.system.domain.service.LocationDomainService;
import com.hightop.benyin.system.infrastructure.entity.Location;
import com.hightop.fario.base.util.StringUtils;
import com.hightop.magina.core.component.ApplicationContexts;

import java.util.Objects;
import java.util.StringJoiner;

public class ReaderExcelVerifyHandler implements IExcelVerifyHandler<Readerxcel> {
    @Override
    public ExcelVerifyHandlerResult verifyHandler(Readerxcel data) {
        LocationDomainService locationDomainService = ApplicationContexts.getBean(LocationDomainService.class);
        RfidReaderServiceDomain rfidReaderServiceDomain = ApplicationContexts.getBean(RfidReaderServiceDomain.class);
        StringJoiner joiner = new StringJoiner(",");
        Location location = locationDomainService.lambdaQuery().eq(Location::getCode, data.getLocationCode()).one();
        if (Objects.isNull(location)) {
            joiner.add("位置编码有误");
        } else {
            data.setLocationId(location.getId());
        }
        RfidReader rfidReader = rfidReaderServiceDomain.lambdaQuery().eq(RfidReader::getCode, data.getCode()).one();
        if (rfidReader==null) {
            rfidReader = rfidReaderServiceDomain.lambdaQuery().eq(RfidReader::getDeviceId, data.getDeviceId()).one();
        }
        if(rfidReader!=null){
            data.setOriReader(rfidReader);
        }
        if(StringUtils.isNotBlank(data.getIsEnableStr())){
            data.setIsEnable(data.getIsEnableStr().equals("启用")?true:false);
        }

        if(StringUtils.isNotBlank(data.getScanStatusStr())){
            data.setScanStatus(data.getScanStatusStr().equals("开启")?2:1);
        }
        if(StringUtils.isNotBlank(data.getBellStatusStr())){
            data.setBellStatus(data.getBellStatusStr().equals("开启")?2:1);
        }

        if(StringUtils.isNotBlank(data.getEnergyStatusStr())){
            data.setEnergyStatus(data.getEnergyStatusStr().equals("开启")?1:2);
        }

        String errorMsg = joiner.toString();
        if (StringUtils.isNotBlank(errorMsg)) {
            return new ExcelVerifyHandlerResult(false, errorMsg);
        }
        return new ExcelVerifyHandlerResult(true);
    }
}
