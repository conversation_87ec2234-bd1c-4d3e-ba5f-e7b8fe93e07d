package com.hightop.benyin.rfid.infrastructure.enums;

import com.hightop.magina.core.custom.entry.EnumEntry;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

/**
 * 盘点类型状态
 *
 * <AUTHOR>
 * @date 2024/5/15 13:42
 */
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Getter
public enum TakeTypeEnums implements EnumEntry<String> {

    /**
     * 临时盘点
     */
    TEMP("临时盘点"),
    /**
     * 月度盘点
     */
    MONTH("月度盘点"),
    /**
     * 季度盘点
     */
    QUARTER("季度盘点"),
    /**
     * 年度盘点
     */
    YEAR("年度盘点");

    /**
     * 状态名称
     */
    String name;

    @Override
    public String getCode() {
        // 以枚举名称作为标识
        return super.name();
    }

    @Override
    public String getName() {
        return this.name;
    }

}
