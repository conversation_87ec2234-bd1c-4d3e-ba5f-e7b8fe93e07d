package com.hightop.benyin.rfid.api;

import com.hightop.benyin.rfid.application.service.RfidAssetTakeDetailService;
import com.hightop.benyin.rfid.application.service.RfidAssetTakeService;
import com.hightop.benyin.rfid.application.service.RfidInfoService;
import com.hightop.benyin.rfid.application.vo.dto.RfidAssetTakeAuditDto;
import com.hightop.benyin.rfid.application.vo.dto.RfidAssetTakeDto;
import com.hightop.benyin.rfid.application.vo.po.AssetTakeScanResultVo;
import com.hightop.benyin.rfid.application.vo.po.AssetTakeTotalVo;
import com.hightop.benyin.rfid.application.vo.po.RfidAssetTakeDetailInfoVo;
import com.hightop.benyin.rfid.application.vo.po.RfidAssetTakeDetailVo;
import com.hightop.benyin.rfid.application.vo.query.RfidAssetTakeDetailQuery;
import com.hightop.benyin.rfid.application.vo.query.RfidAssetTakeQuery;
import com.hightop.benyin.rfid.application.vo.query.RfidAssetTakeReaderQuery;
import com.hightop.benyin.rfid.application.vo.query.RfidTakeDetailQuery;
import com.hightop.benyin.rfid.domain.service.RfidReaderServiceDomain;
import com.hightop.benyin.rfid.infrastructure.entity.*;
import com.hightop.benyin.share.application.dto.MailSendDto;
import com.hightop.benyin.share.infrastructure.entity.MailRecord;
import com.hightop.benyin.share.infrastructure.util.DictUtil;
import com.hightop.benyin.share.socket.util.ReaderControlTool;
import com.hightop.fario.base.util.CollectionUtils;
import com.hightop.fario.base.web.Operation;
import com.hightop.fario.base.web.RestResponse;
import com.hightop.fario.common.core.util.ExecutorUtils;
import com.hightop.fario.common.mybatis.DataGrid;
import com.hightop.magina.standard.behavior.operation.IgnoreOperationLog;
import com.hightop.magina.standard.code.dictionary.api.DictItemVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 资产盘点管理rest接口
 *
 * <AUTHOR>
 * @date 2024-10-12 16:36:01
 */
@RequestMapping("/take")
@RestController
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Api(tags = "资产盘点管理")
public class RfidAssetTakeController {
    RfidAssetTakeService rfidAssetTakeService;
    RfidAssetTakeDetailService rfidAssetTakeDetailService;
    RfidInfoService rfidInfoService;
    RfidReaderServiceDomain rfidReaderServiceDomain;
    RedisTemplate<String, String> redisTemplate;

    @PostMapping("/page")
    @ApiOperation("盘点数据分页查询")
    @IgnoreOperationLog
    public RestResponse<DataGrid<RfidAssetTake>> page(@RequestBody RfidAssetTakeQuery pageQuery) {
        return RestResponse.ok(this.rfidAssetTakeService.page(pageQuery));
    }

    @GetMapping("/{id}")
    @ApiOperation("盘点数据明细查询")
    @IgnoreOperationLog
    public RestResponse<RfidAssetTake> getOne(@PathVariable("id") Long id) {
        return RestResponse.ok(this.rfidAssetTakeService.getById(id));
    }

    @GetMapping("/getTakeType")
    @ApiOperation("获取盘点类型")
    @IgnoreOperationLog
    public RestResponse<List<DictItemVo>> getTakeType() {
        return RestResponse.ok(this.rfidAssetTakeService.getTakeType());
    }


    @PostMapping
    @ApiOperation("新增盘点")
    public RestResponse<RfidAssetTake> add(@Validated @RequestBody RfidAssetTakeDto storageTakeDto) {
        RfidAssetTake rfidAssetTake = this.rfidAssetTakeService.storageTake(storageTakeDto);
        return RestResponse.ok(rfidAssetTake);
    }

    @PostMapping("approve")
    @ApiOperation("审核盘点")
    public RestResponse<Void> approve(@Validated @RequestBody RfidAssetTakeAuditDto rfidAssetTakeAuditDto) {
        return Operation.UPDATE.response(this.rfidAssetTakeService.approve(rfidAssetTakeAuditDto));
    }

    @PostMapping("startScan/{code}")
    @ApiOperation("开始扫描")
    public RestResponse<Void> startScan(@PathVariable("code") String code) {
        RfidAssetTakeReaderQuery rfidAssetTakeReaderQuery = new RfidAssetTakeReaderQuery();
        rfidAssetTakeReaderQuery.setTakeCode(code);
        List<RfidAssetTakeReader> rfidAssetTakeReaders = this.rfidAssetTakeService.takeSchedule(rfidAssetTakeReaderQuery);
        rfidAssetTakeReaders = rfidAssetTakeReaders.stream().filter(v -> !v.getStatus().equals(DictUtil.ADD)).collect(Collectors.toList());
        List<RfidAssetTakeReader> readerNotList = rfidAssetTakeReaders.stream().filter(v -> v.getStatus().equals(DictUtil.SUB)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(readerNotList)) {
            throw new RuntimeException("发现设备异常，无法执行扫描，请确保设备正常后重试！");
        }

        //忽略基站故障与扫描完成的基站
        for (RfidAssetTakeReader rfidAssetTakeReader : rfidAssetTakeReaders) {
            if (rfidAssetTakeReader.getStatus().equals(DictUtil.SUB)
                    || rfidAssetTakeReader.getStatus().equals(DictUtil.ENABLE)) {
                continue;
            }
            rfidAssetTakeService.setReaderStatus(DictUtil.SCANING, rfidAssetTakeReader);
            RfidReader rfidReader = rfidReaderServiceDomain.getById(rfidAssetTakeReader.getReaderId());
            //发送扫描命令
            ExecutorUtils.run(() -> ReaderControlTool.sendScan(redisTemplate, rfidAssetTakeReader.getDeviceId(), rfidReader, DictUtil.TAKE_SCAN, code));
        }
        return Operation.UPDATE.response(Boolean.TRUE);
    }

    @PutMapping("/stopScan/{code}")
    @ApiOperation("停止扫描")
    @IgnoreOperationLog
    public RestResponse<Void> stopScan(@PathVariable @ApiParam("code") String code) {
        return Operation.UPDATE.response(this.rfidAssetTakeService.stopScan(code));
    }

    @PutMapping("reTakeScan/{id}")
    @ApiOperation("单基站重新盘点扫描")
    public RestResponse<Void> reTakeScan(@PathVariable("id") Long id) {
        return Operation.UPDATE.response(this.rfidAssetTakeService.reTakeScan(id));
    }

    @GetMapping("isComplete/{bindCode}/{readerId}")
    @ApiOperation("单个基站是否完成扫描")
    @IgnoreOperationLog
    public RestResponse<Boolean> isCompleted(
            @PathVariable @ApiParam("bindCode") String bindCode, @PathVariable @ApiParam("count") Long readerId) {
        return RestResponse.ok(this.rfidInfoService.isCompleted(DictUtil.TAKE_SCAN, bindCode, readerId));
    }

    @GetMapping("isComplete/{bindCode}")
    @ApiOperation("是否完成扫描")
    @IgnoreOperationLog
    public RestResponse<AssetTakeScanResultVo> isComplete(@PathVariable @ApiParam("bindCode") String bindCode) {
        return RestResponse.ok(this.rfidInfoService.isCompleted(DictUtil.TAKE_SCAN, bindCode));
    }

    @PutMapping("searchLose/{id}")
    @ApiOperation("搜索遗失资产")
    public RestResponse<Void> searchLose(@PathVariable("id") Long id) {
        return Operation.UPDATE.response(this.rfidAssetTakeService.searchLose(id));
    }

    @PutMapping("searchSchedule/{code}")
    @ApiOperation("全局搜索是否完成")
    public RestResponse<Boolean> searchSchedule(@PathVariable("code") String code) {
        return RestResponse.ok(this.rfidAssetTakeService.searchSchedule(code));
    }

    @PostMapping("/schedule")
    @ApiOperation("资产盘点进度查询")
    public RestResponse<List<RfidAssetTakeReader>> schedule(@RequestBody RfidAssetTakeReaderQuery query) {
        return RestResponse.ok(this.rfidAssetTakeService.takeSchedule(query));
    }

    @GetMapping("/noticeInfo/{id}")
    @ApiOperation("获取资产通知详情")
    public RestResponse<MailRecord> noticeInfo(@PathVariable("id") Long id) {
        return RestResponse.ok(this.rfidAssetTakeDetailService.noticeInfo(id));
    }

    @GetMapping("/processInfo/{id}")
    @ApiOperation("获取资产处理详情")
    public RestResponse<RfidAssetTakeDetail> getProcessInfo(@PathVariable("id") Long id) {
        return RestResponse.ok(this.rfidAssetTakeDetailService.getProcessInfo(id));
    }

    @GetMapping("/detail/{id}")
    @ApiOperation("盘点资产详情查询")
    @IgnoreOperationLog
    public RestResponse<RfidAssetTakeDetailInfoVo> getTakeDetailsInfo(@PathVariable("id") Long id) {
        return RestResponse.ok(this.rfidAssetTakeDetailService.getTakeDetailsInfo(id));
    }

    @PutMapping("notice")
    @ApiOperation("通知处理异常资产")
    public RestResponse<Void> notice(@Validated @RequestBody MailSendDto mailSendDto) {
        return Operation.UPDATE.response(this.rfidAssetTakeDetailService.notice(mailSendDto));
    }

    @PutMapping("process")
    @ApiOperation("处理异常资产")
    public RestResponse<Void> process(@Validated @RequestBody RfidAssetTakeDetailInfoVo rfidAssetTakeDetailInfoVo) {
        return Operation.UPDATE.response(this.rfidAssetTakeDetailService.process(rfidAssetTakeDetailInfoVo));
    }


    @PostMapping("/instore/{id}")
    @ApiOperation("登记报溢资产")
    public RestResponse<Void> stash(@Validated @RequestBody RfidAsset rfidasset, @PathVariable("id") Long id) {
        return Operation.UPDATE.response(this.rfidAssetTakeDetailService.instore(rfidasset, id));
    }


    @PutMapping("complete/{id}")
    @ApiOperation("完成本次盘点")
    public RestResponse<Void> complete(@PathVariable("id") Long id) {
        return Operation.UPDATE.response(this.rfidAssetTakeService.complete(id));
    }

    @DeleteMapping("/{id}")
    @ApiOperation("删除盘点数据")
    public RestResponse<Void> delete(@PathVariable @ApiParam("id") Long id) {
        return Operation.DELETE.response(this.rfidAssetTakeService.delete(id));
    }


    @PostMapping("/scanList")
    @ApiOperation("资产盘点扫描结果查询")
    @IgnoreOperationLog
    public RestResponse<DataGrid<RfidAssetTakeDetailVo>> findAssetTakeScanList(@RequestBody RfidTakeDetailQuery query) {
        return RestResponse.ok(this.rfidAssetTakeDetailService.findAssetTakeScanList(query));
    }


    @PutMapping("updateDetail")
    @ApiOperation("修改盘点明细")
    public RestResponse<Void> updateDetail(@RequestBody RfidAssetTakeDetail rfidAssetTakeDetail) {
        return Operation.UPDATE.response(this.rfidAssetTakeDetailService.update(rfidAssetTakeDetail));
    }

    @PostMapping("/detailPage")
    @ApiOperation("盘点明细分页查询")
    @IgnoreOperationLog
    public RestResponse<DataGrid<RfidAssetTakeDetail>> detailPage(@RequestBody RfidAssetTakeDetailQuery pageQuery) {
        return RestResponse.ok(this.rfidAssetTakeDetailService.detailPage(pageQuery));
    }

    @PostMapping("/detailTotal")
    @ApiOperation("盘点资产明细统计")
    @IgnoreOperationLog
    public RestResponse<AssetTakeTotalVo> getTakeDetailsInfo(@RequestBody RfidAssetTakeDetailQuery pageQuery) {
        return RestResponse.ok(this.rfidAssetTakeDetailService.detailTotal(pageQuery));
    }

    @PutMapping("/refresh/{code}")
    @ApiOperation("刷新异常资产数据")
    @IgnoreOperationLog
    public RestResponse<Void> getTakeDetailsInfo(@PathVariable @ApiParam("code") String code) {
        return Operation.UPDATE.response(this.rfidAssetTakeDetailService.refresh(code));
    }

    @ApiOperation("/导出盘点明细数据")
    @PostMapping("/exportDetail")
    @IgnoreOperationLog
    public RestResponse<Void> exportDetail(HttpServletResponse response, @RequestBody RfidAssetTakeDetailQuery pageQuery) {
        Boolean b = rfidAssetTakeDetailService.downloadDetailData(response, pageQuery);
        if (!b) {
            return new RestResponse(500, "导出失败，请联系技术人员！", null, null);
        }
        return RestResponse.message("导出成功");
    }

    @DeleteMapping("/clearCache/{code}")
    @ApiOperation("清理扫描数据")
    @IgnoreOperationLog
    public RestResponse<Void> clearCache(@PathVariable @ApiParam("code") String code) {
        return Operation.DELETE.response(this.rfidAssetTakeService.clearCache(code));
    }

    @DeleteMapping("clear")
    @ApiOperation("清理盘点数据")
    public RestResponse<Void> delete() {
        return Operation.UPDATE.response(this.rfidAssetTakeService.clear());
    }

}
