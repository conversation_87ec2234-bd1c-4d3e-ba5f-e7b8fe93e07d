package com.hightop.benyin.rfid.infrastructure.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.*;
import com.hightop.benyin.rfid.infrastructure.enums.TransferStatus;
import com.hightop.benyin.rfid.infrastructure.enums.TransferType;
import com.hightop.magina.standard.ums.user.bind.UserBind;
import com.hightop.magina.standard.ums.user.bind.UserEntry;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 资产变更表
 *
 * <AUTHOR>
 * @date 2023-11-15 17:19:37
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@TableName("b_rfid_asset_transfer")
@ApiModel
public class RfidAssetTransfer {
    public static final String SEQ = "T";

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @ApiModelProperty("id")
    Long id;

    @TableField("transfer_code")
    @ApiModelProperty("变更单号")
    String transferCode;

    @TableField("transfer_type")
    @ApiModelProperty("变更类型")
    TransferType transferType;

    @TableField("department_id")
    @ApiModelProperty("部门id")
    Long departmentId;

    @TableField("department_name")
    @ApiModelProperty("原部门d")
    Long departmentName;

    @TableField("location_id")
    @ApiModelProperty("位置编码")
    Long locationId;

    @TableField(value = "location")
    @ApiModelProperty("原位置")
    String location;

    @TableField("apply_id")
    @ApiModelProperty("原领用人id")
    Long applyId;

    @TableField("apply_name")
    @ApiModelProperty("原领用人姓名")
    String applyName;

    @TableField("new_department_id")
    @ApiModelProperty("新部门id")
    Long newDepartmentId;

    @TableField("new_department_name")
    @ApiModelProperty("新部门名称")
    Long newDepartmentName;

    @TableField("new_location_id")
    @ApiModelProperty("位置编码")
    Long newLocationId;

    @TableField(value = "new_location")
    @ApiModelProperty("位置")
    String newLocation;

    @TableField("new_apply_id")
    @ApiModelProperty("领用人id")
    Long newApplyId;

    @TableField("new_apply_name")
    @ApiModelProperty("领用人姓名")
    String newApplyName;

    @TableField(value = "transfer_date")
    @ApiModelProperty("变更时间")
    String transferDate;

    @TableField(value = "status")
    @ApiModelProperty("状态")
    TransferStatus status;

    @TableField("remark")
    @Excel(name = "备注", width = 30, orderNum = "10")
    @ApiModelProperty("备注")
    String remark;

    @TableField("audit_at")
    @ApiModelProperty("审核时间")
    LocalDateTime auditAt;

    @TableField("audit_by")
    @ApiModelProperty("审核人")
    Long auditBy;

    @TableField("audit_name")
    @ApiModelProperty("审核人")
    String auditName;

    @TableField("created_by")
    @ApiModelProperty("创建人")
    @UserBind
    UserEntry createdBy;

    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @ApiModelProperty("创建时间")
    LocalDateTime createdAt;

    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty("更新时间")
    LocalDateTime updatedAt;

    @TableField("deleted")
    @ApiModelProperty("是否删除  0未删除  1删除")
    @TableLogic
    Integer deleted;

    @TableField(exist = false)
    @ApiModelProperty("资产id")
    List<Long> assetIds;

    @TableField(exist = false)
    @ApiModelProperty("资产变更明细")
    List<RfidAssetTransferDetail> transferDetails;

    @TableField(exist = false)
    @ApiModelProperty("变更数量")
    Integer num;

}
