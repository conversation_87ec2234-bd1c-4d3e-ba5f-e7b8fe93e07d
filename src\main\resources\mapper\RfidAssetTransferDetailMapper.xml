<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hightop.benyin.rfid.infrastructure.mapper.RfidAssetTransferDetailMapper">

    <select id="pageList" resultType="com.hightop.benyin.rfid.infrastructure.entity.RfidAssetTransferDetail">
        select t.*,t2.full_name location,t3.full_name location newLocation,tt.code ,tt.name ,tt.rfid_code,tt.model,tt.price,tt.asset_type,
               t1.transfer_date,t1.transfer_type,t1.remark,t1.status
               ,t4.name departmentName,t5.name newDepartmentName,t1.apply_name,t1.new_apply_name
        from  b_rfid_asset_transfer_detail t
        left join  b_rfid_asset tt on tt.id = t.asset_id
        left join b_rfid_asset_transfer t1 on t1.transfer_code = t.transfer_code
        left join st_location t2 on t2.id = t1.location_id and t1.transfer_type='CHANGE'
        left join st_location t3 on t3.id = t1.new_location_id and t1.transfer_type='CHANGE'
        left join st_department t4 on t4.id = t1.department_id
        left join st_department t5 on t5.id = t1.new_department_id
        <where>
            <if test="qo.transferCode!= null and qo.transferCode!= ''">
                and t.transfer_code like  concat ('%',#{qo.transferCode},'%')
            </if>
            <if test="qo.rfidCode!= null and qo.rfidCode!= ''">
                and tt.rfid_code like  concat ('%',#{qo.rfidCode},'%')
            </if>
            <if test="qo.assetCode!= null and qo.assetCode!= ''">
                and tt.code like  concat ('%',#{qo.assetCode},'%')
            </if>

            <if test="qo.assetName!= null and qo.assetName!= ''">
                and tt.name like  concat ('%',#{qo.assetName},'%')
            </if>

            <if test="qo.applyName!= null and qo.applyName!= ''">
                and t1.apply_name like  concat ('%',#{qo.applyName},'%')
            </if>
            <if test="qo.newApplyName!= null and qo.newApplyName!= ''">
                and t1.new_apply_name like  concat ('%',#{qo.newApplyName},'%')
            </if>

            <if test="null != qo.startDate and '' != qo.startDate ">
                and t.created_at &gt;= concat(#{qo.startDate},' 00:00:00')
            </if>
            <if test="null != qo.endDate and '' != qo.endDate ">
                and t.created_at &lt;= concat(#{qo.endDate},' 23:59:59')
            </if>

            <if test="qo.transferType!= null and qo.transferType!= ''">
                and t1.transfer_type =  #{qo.transferType}
            </if>

            <if test="qo.status!= null and qo.status!= ''">
                and t1.status =#{qo.status}
            </if>
            <if test="null != qo.startAuditDate and '' != qo.startAuditDate ">
                and t1.audit_at &gt;= concat(#{qo.startAuditDate},' 00:00:00')
            </if>
            <if test="null != qo.endAuditDate and '' != qo.endAuditDate ">
                and t1.audit_at &lt;= concat(#{qo.endAuditDate},' 23:59:59')
            </if>
            <if test="null != qo.startTransferDate and '' != qo.startTransferDate ">
                and t1.transfer_date &gt;= #{qo.startTransferDate}
            </if>
            <if test="null != qo.endTransferDate and '' != qo.endTransferDate ">
                and t1.transfer_date &lt;= #{qo.endTransferDate}
            </if>

            <if test="null!=qo.departmentIds and !qo.departmentIds.isEmpty()">
                AND t1.department_id in
                <foreach collection="qo.departmentIds" item="id" separator=","  open="(" close=")">
                    #{id}
                </foreach>
            </if>

            <if test="null!=qo.newDepartmentIds and !qo.newDepartmentIds.isEmpty()">
                AND t1.new_department_id in
                <foreach collection="qo.newDepartmentIds" item="id" separator=","  open="(" close=")">
                    #{id}
                </foreach>
            </if>

            <if test="null!=qo.locations and !qo.locations.isEmpty()">
                AND t1.location_id in
                <foreach collection="qo.locations" item="id" separator=","  open="(" close=")">
                    #{id}
                </foreach>
            </if>

            <if test="null!=qo.newLocations and !qo.newLocations.isEmpty()">
                AND t1.new_location_id in
                <foreach collection="qo.newLocations" item="id" separator=","  open="(" close=")">
                    #{id}
                </foreach>
            </if>

        </where>
        order by t.created_at desc
    </select>

</mapper>