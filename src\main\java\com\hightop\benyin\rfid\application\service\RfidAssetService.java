package com.hightop.benyin.rfid.application.service;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.yulichang.toolkit.MPJWrappers;
import com.google.common.collect.Lists;
import com.hightop.benyin.rfid.application.handler.AssetExcelVerifyHandler;
import com.hightop.benyin.rfid.application.vo.dto.AssetChangeLogDto;
import com.hightop.benyin.rfid.application.vo.dto.AssetReaderDto;
import com.hightop.benyin.rfid.application.vo.dto.AssetTrajectoryDto;
import com.hightop.benyin.rfid.application.vo.excel.AssetExcel;
import com.hightop.benyin.rfid.application.vo.po.AssetTotalVo;
import com.hightop.benyin.rfid.application.vo.query.RfidAssetForTakeQuery;
import com.hightop.benyin.rfid.application.vo.query.RfidAssetQuery;
import com.hightop.benyin.rfid.domain.event.AssetChangeEvent;
import com.hightop.benyin.rfid.domain.event.AssetTrajectoryEvent;
import com.hightop.benyin.rfid.domain.service.RfidAssetApplyServiceDomain;
import com.hightop.benyin.rfid.domain.service.RfidAssetFlowServiceDomain;
import com.hightop.benyin.rfid.domain.service.RfidAssetServiceDomain;
import com.hightop.benyin.rfid.domain.service.RfidReaderServiceDomain;
import com.hightop.benyin.rfid.infrastructure.entity.RfidAsset;
import com.hightop.benyin.rfid.infrastructure.entity.RfidAssetApply;
import com.hightop.benyin.rfid.infrastructure.entity.RfidAssetFlow;
import com.hightop.benyin.rfid.infrastructure.entity.RfidReader;
import com.hightop.benyin.rfid.infrastructure.enums.*;
import com.hightop.benyin.share.domain.service.SequenceDomainService;
import com.hightop.benyin.share.infrastructure.util.DictUtil;
import com.hightop.benyin.share.infrastructure.util.DownloadResponseUtil;
import com.hightop.benyin.system.api.vo.UserManageExtendVo;
import com.hightop.benyin.system.api.vo.query.UserExtendPageQuery;
import com.hightop.benyin.system.domain.service.AssetTypeDomainService;
import com.hightop.benyin.system.domain.service.LocationDomainService;
import com.hightop.benyin.system.domain.service.UserInfoDomainService;
import com.hightop.benyin.system.infrastructure.entity.*;
import com.hightop.fario.base.util.CollectionUtils;
import com.hightop.fario.base.util.StringUtils;
import com.hightop.fario.common.core.util.ExecutorUtils;
import com.hightop.fario.common.mybatis.DataGrid;
import com.hightop.fario.common.mybatis.PageQuery;
import com.hightop.fario.common.mybatis.util.PageHelper;
import com.hightop.magina.casual.session.ApplicationSessions;
import com.hightop.magina.core.exception.MaginaException;
import com.hightop.magina.standard.code.dictionary.api.DictItemApiService;
import com.hightop.magina.standard.code.dictionary.api.DictItemVo;
import com.hightop.magina.standard.code.dictionary.bind.DictItemEntry;
import com.hightop.magina.standard.ums.department.Department;
import com.hightop.magina.standard.ums.department.DepartmentDomainService;
import com.hightop.magina.standard.ums.user.bind.UserEntry;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Rfid资产管理服务
 *
 * <AUTHOR>
 * @date 2024-10-12 16:36:01
 */
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Service
public class RfidAssetService {

    RfidAssetServiceDomain rfidAssetServiceDomain;
    RfidReaderServiceDomain rfidReaderServiceDomain;
    UserInfoDomainService userInfoDomainService;
    SequenceDomainService sequenceDomainService;
    DepartmentDomainService departmentDomainService;
    RfidAssetFlowServiceDomain rfidAssetFlowServiceDomain;
    LocationDomainService locationDomainService;
    ApplicationEventPublisher applicationEventPublisher;
    RfidAssetApplyServiceDomain rfidAssetApplyServiceDomain;
    DictItemApiService dictItemApiService;
    RfidReaderService rfidReaderService;
    AssetTypeDomainService assetTypeDomainService;


    /**
     * Rfid资产管理分页查询
     *
     * @param pageQuery {@link PageQuery}
     * @return {@link DataGrid}
     */
    public DataGrid<RfidAsset> page(RfidAssetQuery pageQuery) {
        if (!pageQuery.getHasCondition()) {
            pageQuery.setParentCode(DictUtil.ROOT_CODE);
        }
        pageQuery.setCreatedUserId(ApplicationSessions.id());
        List<DictItemVo> dictItemVoList = dictItemApiService.getItemList(DictUtil.DICT_FINANCIAL_CLASSIFY);

        List<Long> childIds = Lists.newArrayList();
        DataGrid<RfidAsset> dataGrid = PageHelper.startPage(pageQuery, p ->
                this.queryList(pageQuery)
        ).peek(p -> {

            if (p.getType().equals(1)) {
                pageQuery.setParentCode(p.getCode());
                List<RfidAsset> childassets = this.queryList(pageQuery);
                if (CollectionUtils.isNotEmpty(childassets)) {
                    childIds.addAll(childassets.stream().map(RfidAsset::getId).collect(Collectors.toList()));
                }
                p.setChildren(childassets);
            }
            if (CollectionUtils.isNotEmpty(p.getFinancialClassify())) {
                if (CollectionUtils.isNotEmpty(p.getFinancialClassify())) {
                    List<String> financialClassify = new ArrayList<>();
                    for (Integer v : p.getFinancialClassify()) {
                        DictItemVo dictItemVo = dictItemVoList.stream().filter(d -> d.getValue().equals(v.toString())).findFirst().orElse(null);
                        financialClassify.add(dictItemVo.getLabel());
                    }
                    p.setFinancialClassifys(String.join(",", financialClassify));
                }
            }
        });
        List<RfidAsset> rfidAssets = Lists.newArrayList();
        for (RfidAsset rfidAsset : dataGrid.getRows()) {
            if (!childIds.contains(rfidAsset.getId())) {
                rfidAssets.add(rfidAsset);
            }
        }
        dataGrid.setRows(rfidAssets);
        return dataGrid;
    }

    public Boolean hasCondition(RfidAssetQuery pageQuery) {
        JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(pageQuery));
        Set<Map.Entry<String, Object>> paramSet = jsonObject.entrySet().stream().filter(entry -> entry.getValue() != null).collect(Collectors.toSet());
        List<String> ignoreParams = Lists.newArrayList("inStatus", "pageNumber", "pageSize", "desc");
        if (paramSet.size() <= ignoreParams.size()) {
            return false;
        }
        return true;
    }

    /**
     * 数量统计
     *
     * @param pageQuery
     * @return
     */
    public AssetTotalVo getTotal(RfidAssetQuery pageQuery) {

        List<RfidAsset> rfidAssets = this.queryList(pageQuery);
        AssetTotalVo assetTotalVo = new AssetTotalVo();
        assetTotalVo.setNumber(rfidAssets.size());
        assetTotalVo.setAddNum(rfidAssets.stream().filter(v -> v.getDataSource().equals(DictUtil.DISABLE)).count());
        assetTotalVo.setImpotNum(rfidAssets.stream().filter(v -> v.getDataSource().equals(DictUtil.ENABLE)).count());
        assetTotalVo.setTagNum(rfidAssets.stream().filter(v -> v.getHasTag()).count());
        assetTotalVo.setBindNum(rfidAssets.stream().filter(v -> StringUtils.isNotBlank(v.getRfidCode())).count());
        return assetTotalVo;
    }

    /**
     * 负责人列表
     *
     * @param pageQuery
     * @return
     */
    public List<UserManageExtendVo> getManagerList(UserExtendPageQuery pageQuery) {
        List<UserManageExtendVo> userManageExtendVos = this.rfidAssetServiceDomain.selectJoinList(UserManageExtendVo.class,
                MPJWrappers.lambdaJoin()
                        .distinct()
                        .selectAs(RfidAsset::getManagerId, UserManageExtendVo::getId)
                        .selectAs(UserInfo::getName, UserManageExtendVo::getName)
                        .leftJoin(UserInfo.class, UserInfo::getId, RfidAsset::getManagerId)
                        .like(StringUtils.isNotBlank(pageQuery.getName()), RfidAsset::getManagerName, pageQuery.getName())
                        .like(StringUtils.isNotBlank(pageQuery.getCode()), UserInfo::getCode, pageQuery.getCode())
                        .eq(pageQuery.getDepartmentId() != null, UserInfo::getDepartmentId, pageQuery.getDepartmentId())
                        .in(CollectionUtils.isNotEmpty(pageQuery.getDepartmentIds()), UserInfo::getDepartmentId, pageQuery.getDepartmentIds())
                        .in(CollectionUtils.isNotEmpty(pageQuery.getLocations()), UserInfo::getLocationId, pageQuery.getLocations())
                        .isNotNull(RfidAsset::getManagerId)
        );
        return userManageExtendVos;
    }

    /**
     * 登记人列表
     *
     * @param pageQuery
     * @return
     */
    public List<UserManageExtendVo> getCreatorList(UserExtendPageQuery pageQuery) {
        List<UserManageExtendVo> userManageExtendVos = this.rfidAssetServiceDomain.selectJoinList(UserManageExtendVo.class,
                MPJWrappers.lambdaJoin()
                        .distinct()
                        .selectAs(RfidAsset::getCreatedBy, UserManageExtendVo::getId)
                        .selectAs(UserInfo::getName, UserManageExtendVo::getName)
                        .leftJoin(UserInfo.class, UserInfo::getId, RfidAsset::getCreatedBy)
                        .like(StringUtils.isNotBlank(pageQuery.getName()), UserInfo::getName, pageQuery.getName())
                        .like(StringUtils.isNotBlank(pageQuery.getCode()), UserInfo::getCode, pageQuery.getCode())
                        .eq(pageQuery.getDepartmentId() != null, UserInfo::getDepartmentId, pageQuery.getDepartmentId())
                        .in(CollectionUtils.isNotEmpty(pageQuery.getDepartmentIds()), UserInfo::getDepartmentId, pageQuery.getDepartmentIds())
                        .in(CollectionUtils.isNotEmpty(pageQuery.getLocations()), UserInfo::getLocationId, pageQuery.getLocations())
        );
        return userManageExtendVos;
    }

    /**
     * 基站资产数量统计
     *
     * @param pageQuery
     * @return
     */
    public AssetTotalVo getListTotal(RfidAssetQuery pageQuery) {
        AssetTotalVo assetTotalVo = this.rfidAssetServiceDomain.getAssetTotal(pageQuery);
        assetTotalVo.setWaitBind(this.rfidAssetServiceDomain.getNotBindTotal(pageQuery));
        assetTotalVo.setWaitRerader(this.rfidAssetServiceDomain.getNotReaderTotal(pageQuery));
        return assetTotalVo;
    }

    public List<RfidAsset> queryList(RfidAssetQuery pageQuery) {
        return this.rfidAssetServiceDomain.getAssetList(pageQuery);
    }

    public RfidAsset getById(Long id) {
        RfidAssetQuery rfidAssetQuery = new RfidAssetQuery();
        rfidAssetQuery.setId(id);
        List<RfidAsset> rfidAssets = this.queryList(rfidAssetQuery);
        RfidAsset rfidAsset = CollectionUtils.isNotEmpty(rfidAssets) ? rfidAssets.get(0) : null;
//        if (rfidAsset != null && rfidAsset.getType().equals(1)) {
//            RfidAssetQuery pageQueryChild = new RfidAssetQuery();
//            pageQueryChild.setParentCode(rfidAsset.getCode());
//            List<RfidAsset> childassets = this.queryList(pageQueryChild);
//            rfidAsset.setChildren(childassets);
//        }
        if (rfidAsset != null && CollectionUtils.isNotEmpty(rfidAsset.getFinancialClassify())) {
            List<DictItemVo> dictItemVoList = dictItemApiService.getItemList(DictUtil.DICT_FINANCIAL_CLASSIFY);
            List<String> financialClassify = new ArrayList<>();
            for (Integer v : rfidAsset.getFinancialClassify()) {
                DictItemVo dictItemVo = dictItemVoList.stream().filter(d -> d.getValue().equals(v.toString())).findFirst().orElse(null);
                financialClassify.add(dictItemVo.getLabel());
            }
            rfidAsset.setFinancialClassifys(String.join(",", financialClassify));
        }
        return rfidAsset;
    }

    /**
     * 查询有效盘点资产
     *
     * @param pageQuery
     * @return
     */
    public List<RfidAsset> getTakeAssetList(RfidAssetForTakeQuery pageQuery) {
        return this.rfidAssetServiceDomain.selectJoinList(RfidAsset.class, MPJWrappers.lambdaJoin()
                        .distinct()
                        .selectAll(RfidAsset.class)
                        .selectAs(Department::getName, RfidAsset::getDepartmentName)
                        .selectAs(DepartmentExtends::getName, RfidAsset::getManagerDeptName)
                        .selectAs(DepartmentInfo::getName, RfidAsset::getCreateDeptName)
                        .selectAs(Location::getFullName, RfidAsset::getLocation)
                        .selectAs(UserInfo::getName, RfidAsset::getCreatedByName)
                        .selectAs(RfidAsset::getId, RfidAsset::getAssetId)
                        .selectAs(RfidReader::getDeviceId, RfidAsset::getReaderDeviceId)

                        .leftJoin(RfidReader.class, RfidReader::getId, RfidAsset::getReaderId)
                        .leftJoin(Location.class, Location::getId, RfidAsset::getLocationId)
                        .leftJoin(Department.class, Department::getId, RfidAsset::getDepartmentId)
                        .leftJoin(DepartmentExtends.class, DepartmentExtends::getId, RfidAsset::getManagerDeptId)
                        .leftJoin(UserInfo.class, UserInfo::getId, RfidAsset::getCreatedBy)
                        .leftJoin(DepartmentInfo.class, DepartmentInfo::getId, RfidAsset::getCreateDept)
//                        .ne(RfidAsset::getUseState, AssetApplyStatus.DISABLE)
                        .and(v -> v.eq(RfidAsset::getIsTake, true)
                                .or().eq(RfidAsset::getIsTakeStatis, true)
                        ).and(CollectionUtils.isNotEmpty(pageQuery.getDepartmentIds()), v -> v.isNotNull(RfidAsset::getDepartmentId).in(RfidAsset::getDepartmentId, pageQuery.getDepartmentIds())
                                .or().in(RfidAsset::getManagerDeptId, pageQuery.getDepartmentIds()))
                        .in(CollectionUtils.isNotEmpty(pageQuery.getLocations()), RfidAsset::getLocationId, pageQuery.getLocations())
                        .in(CollectionUtils.isNotEmpty(pageQuery.getApplyIds()), RfidAsset::getApplyId, pageQuery.getApplyIds())
                        .eq(pageQuery.getApplyId() != null, RfidAsset::getApplyId, pageQuery.getApplyId())
                        .eq(pageQuery.getManagerId() != null, RfidAsset::getManagerId, pageQuery.getManagerId())
        );
    }

    /**
     * Rfid资产管理 -登记
     *
     * @param rfidAsset {@linkrfidAsset
     *                  }
     * @return true/false
     */
    @Transactional(rollbackFor = Exception.class)
    public String stash(RfidAsset rfidAsset) {
        RfidAsset rfidAssetOld = null;
        if (rfidAsset.getId() == null) {
            rfidAsset.setCreatedBy(new UserEntry().setId(ApplicationSessions.id()));
        } else {
            rfidAsset.setUpdatedBy(ApplicationSessions.id());
            rfidAssetOld = this.rfidAssetServiceDomain.getById(rfidAsset.getId());
        }
        if (StringUtils.isBlank(rfidAsset.getSignCode())) {
            String code = sequenceDomainService.nextDateSequence(DictUtil.SEQ_ASSET_APPLY, 4);
            rfidAsset.setSignCode(code);
        }

        if (StringUtils.isNotBlank(rfidAsset.getOriRfidCode())) {
            Long count = rfidAssetServiceDomain.lambdaQuery()
                    .eq(RfidAsset::getOriRfidCode, rfidAsset.getOriRfidCode())
                    .ne(rfidAsset.getId() != null, RfidAsset::getId, rfidAsset.getId())
                    .count();
            if (count > 0L) {
                throw new MaginaException("原资产标签号已存在，请勿重复登记！");
            }
        }
        if (StringUtils.isNotBlank(rfidAsset.getRfidCode())) {
            Long count = rfidAssetServiceDomain.lambdaQuery()
                    .eq(RfidAsset::getRfidCode, rfidAsset.getRfidCode())
                    .ne(rfidAsset.getId() != null, RfidAsset::getId, rfidAsset.getId())
                    .count();
            if (count > 0L) {
                throw new MaginaException("资产RFID标签号已存在，请勿重复登记！");
            }
        }
        if (Objects.nonNull(rfidAsset.getLocationId())) {
//            RfidReader rfidReader = rfidReaderServiceDomain.lambdaQuery()
//                    .eq(RfidReader::getLocationId, rfidAsset.getLocationId()).one();
//            if (rfidReader == null) {
//                throw new MaginaException("资产编号[" + rfidAsset.getCode() + "]所在位置未找到基站数据！");
//            }
//            rfidAsset.setReaderId(rfidReader.getId());
        }
        if (rfidAsset.getId() == null && rfidAsset.getStatus() == null) {
            rfidAsset.setStatus(AssetApplyStatus.STASH);
        } else {
            rfidAsset.setInStatus(true);
        }
        if (rfidAsset.getApplyId() != null) {
            UserInfo UserInfo = userInfoDomainService.getById(rfidAsset.getApplyId());
            rfidAsset.setApplyName(UserInfo.getName());
        }

        if (rfidAsset.getManagerId() != null) {
            UserInfo UserInfo = userInfoDomainService.getById(rfidAsset.getManagerId());
            rfidAsset.setManagerName(UserInfo.getName());
        }

        //清理子资产
        rfidAssetServiceDomain.lambdaUpdate()
                .set(RfidAsset::getParentCode, DictUtil.ROOT_CODE)
                .eq(RfidAsset::getParentCode, rfidAsset.getCode()).update();

        if (CollectionUtils.isNotEmpty(rfidAsset.getChildrenIds())) {
            //标记上级资产
            rfidAsset.setType(DictUtil.HAS_CHILD);

            List<RfidAsset> childAssets = rfidAssetServiceDomain.lambdaQuery()
                    .in(RfidAsset::getId, rfidAsset.getChildrenIds()).list();
            childAssets.forEach(p -> {
                p.setParentCode(rfidAsset.getCode());
            });
            rfidAssetServiceDomain.updateBatchById(childAssets);
        }
        if (StringUtils.isNotBlank(rfidAsset.getParentCode())) {
            rfidAssetServiceDomain.lambdaUpdate()
                    .set(RfidAsset::getType, DictUtil.HAS_CHILD)
                    .eq(RfidAsset::getCode, rfidAsset.getParentCode()).update();
        }
        if (rfidAsset.getAssetType() != null) {
            String assetType = rfidAsset.getAssetType();
            AssetType assetTypeEntity =assetTypeDomainService.getByCode(assetType);
            if(assetTypeEntity!=null){
                rfidAsset.setHasTag(assetTypeEntity.getHasTag());
                rfidAsset.setIsTake(assetTypeEntity.getIsTake());
                rfidAsset.setIsScan(assetTypeEntity.getIsScan());
                rfidAsset.setIsStatis(assetTypeEntity.getIsStatis());
                rfidAsset.setIsTakeStatis(assetTypeEntity.getIsTakeStatis());
                rfidAsset.setIsReport(assetTypeEntity.getIsReport());
            }
            rfidAsset.setAssetParentType(assetType.substring(0, 3));
        }
        if (rfidAssetOld != null) {
            AssetChangeLogDto assetChangeLogDto = buildAssetLog(rfidAsset, rfidAssetOld);
            if (assetChangeLogDto != null) {
                ExecutorUtils.doAfterCommit(
                        () -> applicationEventPublisher.publishEvent(new AssetChangeEvent(Lists.newArrayList(assetChangeLogDto)))
                );
            }
        }
        if (rfidAsset.getDataSource() == null) {
            rfidAsset.setDataSource(0);
        }
        if (StringUtils.isBlank(rfidAsset.getAcquireDate())) {
            rfidAsset.setAcquireDate(DateUtil.formatDate(new Date()));
        }

        if (rfidAsset.getAcquireMode()==null) {
            rfidAsset.setAcquireMode(new DictItemEntry().setValue(DictUtil.BUY));
        }

        if (rfidAsset.getAssetPurpose()==null) {
            rfidAsset.setAssetPurpose(new DictItemEntry().setValue(DictUtil.UNBLE));
        }
        if (rfidAsset.getFinancialClassify()==null) {
            rfidAsset.setFinancialClassify(Lists.newArrayList(DictUtil.SELF_UNIT));
        }
        if (rfidAsset.getCardType()==null) {
            rfidAsset.setCardType(new DictItemEntry().setValue(DictUtil.S));
        }
        rfidAsset.setInitSalvage(rfidAsset.getPrice());
        rfidAsset.setNowSalvage(rfidAsset.getPrice());
        rfidAsset.setUseState(new DictItemEntry().setValue(DictUtil.STANDBY));
        this.rfidAssetServiceDomain.saveOrUpdate(rfidAsset);

        return rfidAsset.getSignCode();
    }

    /**
     * Rfid资产管理 -登记
     *
     * @param rfidAsset {@linkrfidAsset
     *                  }
     * @return true/false
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean update(RfidAsset rfidAsset) {
        RfidAsset rfidAssetOld = this.rfidAssetServiceDomain.getById(rfidAsset.getId());
        if (rfidAsset.getId() == null) {
            rfidAsset.setCreatedBy(new UserEntry().setId(ApplicationSessions.id()));
        } else {
            rfidAsset.setUpdatedBy(ApplicationSessions.id());
        }

        if (StringUtils.isNotBlank(rfidAsset.getOriRfidCode())) {
            Long count = rfidAssetServiceDomain.lambdaQuery()
                    .eq(RfidAsset::getOriRfidCode, rfidAsset.getOriRfidCode())
                    .ne(rfidAsset.getId() != null, RfidAsset::getId, rfidAsset.getId())
                    .count();
            if (count > 0L) {
                throw new MaginaException("原资产标签号已存在，请勿重复登记！");
            }
        }
        if (StringUtils.isNotBlank(rfidAsset.getRfidCode())) {
            Long count = rfidAssetServiceDomain.lambdaQuery()
                    .eq(RfidAsset::getRfidCode, rfidAsset.getRfidCode())
                    .ne(rfidAsset.getId() != null, RfidAsset::getId, rfidAsset.getId())
                    .count();
            if (count > 0L) {
                throw new MaginaException("资产RFID标签号已存在，请勿重复登记！");
            }
        }
//        if (Objects.nonNull(rfidAsset.getLocationId())) {
//            RfidReader rfidReader = rfidReaderServiceDomain.lambdaQuery()
//                    .eq(RfidReader::getLocationId, rfidAsset.getLocationId()).one();
//            if (rfidReader == null) {
//                throw new MaginaException("资产编号[" + rfidAsset.getCode() + "]所在位置未找到基站数据！");
//            }
//            rfidAsset.setReaderId(rfidReader.getId());
//        }

        if (rfidAsset.getApplyId() != null) {
            UserInfo UserInfo = userInfoDomainService.getById(rfidAsset.getApplyId());
            rfidAsset.setApplyName(UserInfo.getName());
        }
        if (rfidAsset.getManagerId() != null) {
            UserInfo UserInfo = userInfoDomainService.getById(rfidAsset.getManagerId());
            rfidAsset.setManagerName(UserInfo.getName());
        }

        //清理子资产
        rfidAssetServiceDomain.lambdaUpdate()
                .set(RfidAsset::getParentCode, DictUtil.ROOT_CODE)
                .eq(RfidAsset::getParentCode, rfidAsset.getCode()).update();

        if (CollectionUtils.isNotEmpty(rfidAsset.getChildrenIds())) {
            //标记上级资产
            rfidAsset.setType(DictUtil.HAS_CHILD);

            List<RfidAsset> childAssets = rfidAssetServiceDomain.lambdaQuery()
                    .in(RfidAsset::getId, rfidAsset.getChildrenIds()).list();
            childAssets.forEach(p -> {
                p.setParentCode(rfidAsset.getCode());
            });
            rfidAssetServiceDomain.updateBatchById(childAssets);
        }
        if (StringUtils.isNotBlank(rfidAsset.getParentCode())) {
            rfidAssetServiceDomain.lambdaUpdate()
                    .set(RfidAsset::getType, DictUtil.HAS_CHILD)
                    .eq(RfidAsset::getCode, rfidAsset.getParentCode()).update();
        }
        if (rfidAsset.getAssetType() != null) {
            String assetType = rfidAsset.getAssetType();
            AssetType middleType = assetTypeDomainService.getParentByCode(assetType);
            if (middleType != null) {
                rfidAsset.setAssetMiddleType(middleType.getCode());
                AssetType bigType = assetTypeDomainService.getParentByCode(middleType.getCode());
                if (bigType != null) {
                    rfidAsset.setAssetParentType(bigType.getCode());
                }
            }
        }
        if (StringUtils.isNotBlank(rfidAsset.getRfidCode())) {
            rfidAsset.setHasTag(true);
        }

        if (rfidAsset.getApplyId() == null) {
            rfidAsset.setApplyName(null);
            rfidAsset.setDepartmentId(null);
        }
        this.rfidAssetServiceDomain.saveOrUpdate(rfidAsset);

        Long oldReaderId = rfidAssetOld != null ? rfidAssetOld.getReaderId() : null;
        if (rfidAsset.getReaderId() != null) {
            if (oldReaderId == null || !rfidAsset.getReaderId().equals(oldReaderId)) {
                List<Long> readerIds = Lists.newArrayList(rfidAsset.getReaderId());
                if (oldReaderId != null) {
                    readerIds.add(oldReaderId);
                }
                //下发到基站
                ExecutorUtils.doAfterCommit(() -> {
                    rfidReaderService.sendAssetBatch(readerIds);
                });
            }
        }
        return Boolean.TRUE;
    }

    public boolean bind(RfidAsset rfidAsset, String bindCode) {
        if (StringUtils.isNotBlank(rfidAsset.getRfidCode())) {
            long count = rfidAssetServiceDomain.lambdaQuery()
                    .eq(RfidAsset::getRfidCode, rfidAsset.getRfidCode())
                    .ne(RfidAsset::getId, rfidAsset.getId())
                    .count();
            if (count > 0L) {
                throw new MaginaException("RFID标签号已绑定资产，请勿重复绑定！");
            }
        }
        RfidAsset rfidAssetOld = this.rfidAssetServiceDomain.getById(rfidAsset.getId());
        rfidAssetOld.setRfidCode(rfidAsset.getRfidCode());
        rfidAssetOld.setHasTag(rfidAsset.getHasTag());
        rfidAssetOld.setIsTake(rfidAsset.getIsTake());
        rfidAssetOld.setIsScan(rfidAsset.getIsScan());
        rfidAssetOld.setIsStatis(rfidAsset.getIsStatis());
        rfidAssetOld.setIsReport(rfidAsset.getIsReport());
        rfidAssetOld.setIsTakeStatis(rfidAsset.getIsTakeStatis());
        rfidAssetOld.setReaderId(rfidAsset.getReaderId());
        rfidAssetOld.setLocationId(rfidAsset.getLocationId());
        rfidAssetOld.setCurrReaderId(rfidAsset.getReaderId());
        rfidAssetOld.setCurrLocationId(rfidAsset.getLocationId());
        if (rfidAsset.getAssetType() != null) {
            String assetType = rfidAsset.getAssetType();
            rfidAssetOld.setAssetType(assetType);
            AssetType middleType = assetTypeDomainService.getParentByCode(assetType);
            if (middleType != null) {
                rfidAssetOld.setAssetMiddleType(middleType.getCode());
                AssetType bigType = assetTypeDomainService.getParentByCode(middleType.getCode());
                if (bigType != null) {
                    rfidAssetOld.setAssetParentType(bigType.getCode());
                }
            }
        }
        if (StringUtils.isBlank(rfidAsset.getRfidCode())) {
            if (StringUtils.isNotBlank(rfidAssetOld.getRfidCode())) {
                rfidAssetApplyServiceDomain.remove(Wrappers.<RfidAssetApply>lambdaQuery()
                        .eq(RfidAssetApply::getFlowCode, bindCode)
                        .eq(RfidAssetApply::getRfidCode, rfidAssetOld.getRfidCode())
                );
            }
            rfidAssetOld.setRfidCode(null);
            rfidAssetOld.setReaderId(null);
        } else {
            RfidAssetApply rfidAssetApply = rfidAssetApplyServiceDomain.lambdaQuery()
                    .eq(RfidAssetApply::getFlowCode, bindCode)
                    .eq(RfidAssetApply::getRfidCode, rfidAsset.getRfidCode())
                    .one();
            if (rfidAssetApply == null) {
                rfidAssetApply = new RfidAssetApply();
                rfidAssetApply.setFlowCode(bindCode);
                rfidAssetApply.setRfidCode(rfidAsset.getRfidCode());
                rfidAssetApply.setAssetId(rfidAsset.getId());
                rfidAssetApply.setCreatedBy(new UserEntry().setId(ApplicationSessions.id()));
                rfidAssetApplyServiceDomain.save(rfidAssetApply);
            }
            RfidAssetFlow rfidAssetFlow = rfidAssetFlowServiceDomain.lambdaQuery()
                    .eq(RfidAssetFlow::getCode, bindCode).one();
            if (rfidAssetFlow == null) {
                rfidAssetFlow = new RfidAssetFlow();
            }
            UserInfo userInfo = userInfoDomainService.getCurrUser();
            rfidAssetFlow.setStatus(AssetFlowStatus.WAIT_APPROVE);
            rfidAssetFlow.setCode(bindCode);
            rfidAssetFlow.setCreatedBy(new UserEntry().setId(ApplicationSessions.id()));
            rfidAssetFlow.setCreateDept(userInfo.getDepartmentId());
            rfidAssetFlow.setDepartmentId(userInfo.getDepartmentId());
            rfidAssetFlow.setOperateType(AssetBusinessType.BIND);
            if (userInfo.getDepartmentId() != null) {
                Department department = departmentDomainService.getById(userInfo.getDepartmentId());
                rfidAssetFlow.setDepartmentName(department != null ? department.getName().getValue() : null);
            }
            rfidAssetFlow.setLocationId(rfidAsset.getLocationId());
            if (rfidAsset.getLocationId() != null) {
                Location location = locationDomainService.getById(rfidAsset.getLocationId());
                rfidAssetFlow.setLocation(location.getFullName());
            }
            rfidAssetFlow.setReaderId(rfidAsset.getReaderId());

            if (rfidAsset.getReaderId() != null) {
                RfidReader rfidReader = rfidReaderServiceDomain.getById(rfidAsset.getReaderId());
                rfidAssetFlow.setDeviceId(rfidReader.getDeviceId());
            }

            List<RfidAssetApply> rfidAssetApplys = rfidAssetApplyServiceDomain.lambdaQuery()
                    .eq(RfidAssetApply::getFlowCode, bindCode)
                    .ne(RfidAssetApply::getRfidCode, rfidAsset.getRfidCode())
                    .list();
            rfidAssetFlow.setNumber(rfidAssetApplys.size()+1);
            rfidAssetFlow.setStatus(AssetFlowStatus.PASS);
            this.rfidAssetFlowServiceDomain.saveOrUpdate(rfidAssetFlow);
        }
        this.rfidAssetServiceDomain.updateById(rfidAssetOld);
        if (Objects.nonNull(rfidAsset.getReaderId())) {
            ExecutorUtils.doAfterCommit(() -> {
                rfidReaderService.sendAsset(rfidAsset.getReaderId());
            });
        }
        return Boolean.TRUE;
    }

    public AssetChangeLogDto buildAssetLog(RfidAsset rfidAsset, RfidAsset rfidAssetOld) {
        AssetChangeLogDto assetChangeLogDto = AssetChangeLogDto.builder()
                .assetId(rfidAsset.getId())
                .changeCode(rfidAsset.getCode())
                .createdBy(ApplicationSessions.id())
                .source(AssetChangeSource.DATA)
                .operatType(AssetOperatType.UPDATE)
                .build();
        String before = "";
        String after = "";
        if (rfidAsset.getManagerDeptId() == null || rfidAssetOld.getManagerDeptId() == null
                || !rfidAsset.getManagerDeptId().equals(rfidAssetOld.getManagerDeptId())) {
            if (rfidAssetOld.getManagerDeptId() != null) {
                Department department = departmentDomainService.getById(rfidAssetOld.getManagerDeptId());
                before += " 责任部门：" + department.getName().getValue();
            }
            if (rfidAsset.getManagerDeptId() != null) {
                Department department = departmentDomainService.getById(rfidAsset.getManagerDeptId());
                after += " 责任部门：" + department.getName().getValue();
            }
        }
        if (StringUtils.isEmpty(rfidAsset.getManagerName())
                || StringUtils.isEmpty(rfidAssetOld.getManagerName()) || !rfidAsset.getManagerName().equals(rfidAssetOld.getManagerName())) {
            if (StringUtils.isNotBlank(rfidAssetOld.getManagerName())) {
                before += " 责任人：" + rfidAssetOld.getManagerName();
            }
            if (StringUtils.isNotBlank(rfidAsset.getManagerName())) {
                after += " 责任人：" + rfidAsset.getManagerName();
            }
        }
        if (rfidAsset.getReaderId() != null
                || rfidAssetOld.getReaderId() == null || !rfidAsset.getReaderId().equals(rfidAssetOld.getReaderId())) {
            if (Objects.nonNull(rfidAssetOld.getReaderId())) {
                RfidReader rfidReader = rfidReaderServiceDomain.getById(rfidAssetOld.getReaderId());
                before += " 基站编码：" + rfidReader.getCode();
            }
            if (Objects.nonNull(rfidAsset.getReaderId())) {
                RfidReader rfidReader = rfidReaderServiceDomain.getById(rfidAsset.getReaderId());
                after += " 基站编码：" + rfidReader.getCode();
            }
        }
        if (Objects.isNull(rfidAsset.getLocationId())
                || Objects.isNull(rfidAssetOld.getLocationCode()) || !rfidAsset.getLocationId().equals(rfidAssetOld.getLocationId())) {
            if (Objects.nonNull(rfidAssetOld.getLocationCode())) {
                Location location = locationDomainService.getById(rfidAssetOld.getLocationId());
                before += " 位置：" + location.getName();
            }
            if (Objects.nonNull(rfidAsset.getLocationCode())) {
                Location location = locationDomainService.getById(rfidAsset.getLocationId());
                after += " 位置：" + location.getName();
            }
        }
        if (StringUtils.isEmpty(rfidAsset.getRfidCode())
                || StringUtils.isEmpty(rfidAssetOld.getRfidCode()) || !rfidAsset.getRfidCode().equals(rfidAssetOld.getRfidCode())) {
            if (StringUtils.isNotBlank(rfidAssetOld.getRfidCode())) {
                before += " RFID标签号：" + rfidAssetOld.getRfidCode();
            }
            if (StringUtils.isNotBlank(rfidAsset.getLocationCode())) {
                after += " RFID标签号：" + rfidAsset.getRfidCode();
            }
        }
        if (StringUtils.isNotBlank(before) || StringUtils.isNotBlank(after)) {
            assetChangeLogDto.setBefore(before);
            assetChangeLogDto.setAfter(after);
            return assetChangeLogDto;
        }
        return null;
    }


    public boolean delete(Long id) {
        RfidAsset rfidAsset = rfidAssetServiceDomain.getById(id);
        if (!rfidAsset.getStatus().equals(AssetApplyStatus.STASH)) {
            throw new MaginaException("非暂存状态资产不能删除！");
        }
        return this.rfidAssetServiceDomain.removeById(id);
    }

    public boolean addReader(AssetReaderDto assetReaderDto) {
        RfidReader rfidReader = rfidReaderServiceDomain.lambdaQuery()
                .eq(RfidReader::getCode, assetReaderDto.getReaderCode()).one();
        if (rfidReader == null) {
            throw new MaginaException("基站数据不存在！");
        }
        List<RfidAsset> addRfidAssets = rfidAssetServiceDomain.selectJoinList(RfidAsset.class, MPJWrappers.lambdaJoin()
                .selectAll(RfidAsset.class)
                .selectAs(RfidReader::getCode, RfidAsset::getReader)
                .leftJoin(RfidReader.class, RfidReader::getId, RfidAsset::getReaderId)
                .in(RfidAsset::getId, assetReaderDto.getAssetIds())
                .ne(RfidAsset::getReaderId, rfidReader.getId())
        );

        //取消的关联资产
        List<RfidAsset> cancelRfidAssets = rfidAssetServiceDomain.selectJoinList(RfidAsset.class, MPJWrappers.lambdaJoin()
                .selectAll(RfidAsset.class)
                .selectAs(RfidReader::getCode, RfidAsset::getReader)
                .leftJoin(RfidReader.class, RfidReader::getId, RfidAsset::getReaderId)
                .eq(RfidAsset::getReaderId, rfidReader.getId())
                .notIn(RfidAsset::getId, assetReaderDto.getAssetIds()));

        List<AssetChangeLogDto> assetChangeLogDtos = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(addRfidAssets)) {
            assetChangeLogDtos.addAll(addRfidAssets.stream().map(v -> {
                return AssetChangeLogDto.builder()
                        .assetId(v.getId())
                        .changeCode(v.getCode())
                        .createdBy(ApplicationSessions.id())
                        .source(AssetChangeSource.DATA)
                        .operatType(AssetOperatType.UPDATE)
                        .before(" 基站编码：" + v.getReader())
                        .after(" 基站编码：" + rfidReader.getCode())
                        .build();
            }).collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(cancelRfidAssets)) {
            assetChangeLogDtos.addAll(cancelRfidAssets.stream().map(v -> {
                return AssetChangeLogDto.builder()
                        .assetId(v.getId())
                        .changeCode(v.getCode())
                        .createdBy(ApplicationSessions.id())
                        .source(AssetChangeSource.DATA)
                        .operatType(AssetOperatType.UPDATE)
                        .before(" 基站编码：" + v.getCode())
                        .after(" 基站编码：")
                        .build();
            }).collect(Collectors.toList()));
        }

        rfidAssetServiceDomain.lambdaUpdate()
                .set(RfidAsset::getReaderId, null)
                .eq(RfidAsset::getReaderId, rfidReader.getId())
                .update();
        rfidAssetServiceDomain.lambdaUpdate()
                .set(RfidAsset::getReaderId, rfidReader.getId())
                .in(RfidAsset::getId, assetReaderDto.getAssetIds())
                .update();
        //数据下发
        ExecutorUtils.doAfterCommit(() -> {
            rfidReaderService.sendAsset(rfidReader.getId());
            if (CollectionUtils.isNotEmpty(assetChangeLogDtos)) {
                applicationEventPublisher.publishEvent(new AssetChangeEvent(assetChangeLogDtos));
            }
        });
        return Boolean.TRUE;
    }


    /**
     * 领用移除
     *
     * @param id
     * @return
     */
    public boolean remove(Long id) {
        return rfidAssetApplyServiceDomain.removeById(id);
    }

    /**
     * 查询资产总数量和总金额
     *
     * @param departmentIds
     * @return
     */
    public AssetTotalVo getTotal(List<Long> departmentIds) {
        return this.rfidAssetServiceDomain.selectJoinOne(AssetTotalVo.class, MPJWrappers.lambdaJoin()
                .selectCount(RfidAsset::getId, "number")
                .selectSum(RfidAsset::getPrice, "amount")
                .leftJoin(Location.class, Location::getId, RfidAsset::getLocationId)
                .in(CollectionUtils.isNotEmpty(departmentIds), Location::getDepartmentIds, departmentIds)
                .eq(RfidAsset::getInStatus, DictUtil.IN_STOCK)
        );
    }

    /**
     * 导出资产数据
     *
     * @param response
     * @return
     */
    public Boolean downloadAllData(HttpServletResponse response, RfidAssetQuery pageQuery) {
        try {
            //查询数据
            List<RfidAsset> excelList = this.queryList(pageQuery);
            List<DictItemVo> dictItemVoList = dictItemApiService.getItemList(DictUtil.DICT_FINANCIAL_CLASSIFY);
            for (RfidAsset rfidAsset : excelList) {
                if (CollectionUtils.isNotEmpty(rfidAsset.getFinancialClassify())) {
                    List<String> financialClassify = new ArrayList<>();
                    for (Integer v : rfidAsset.getFinancialClassify()) {
                        DictItemVo dictItemVo = dictItemVoList.stream().filter(d -> d.getValue().equals(v.toString())).findFirst().orElse(null);
                        financialClassify.add(dictItemVo.getLabel());
                    }
                    rfidAsset.setFinancialClassifys(String.join(",", financialClassify));
                }
            }
            //页面下载设置
            DownloadResponseUtil.addDownLoadHeader(response, "当前资产数据.xlsx");
            //将并添加信息，实体类结构，类的数据做成excel表格对象workbook
            Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams(), RfidAsset.class, excelList);

            workbook.write(response.getOutputStream());
        } catch (IOException e) {
            e.printStackTrace();
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    /**
     * 下载模板
     *
     * @param response
     * @return
     */
    public Boolean downTemplate(HttpServletResponse response) {
        List<AssetExcel> excelList = new ArrayList<>();
        try {
            //页面下载设置
            DownloadResponseUtil.addDownLoadHeader(response, "资产登记模板.xlsx");
            //将并添加信息，实体类结构，类的数据做成excel表格对象workbook
            Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams(),
                    AssetExcel.class, excelList
            );
            workbook.write(response.getOutputStream());
        } catch (IOException e) {
            e.printStackTrace();
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }


    /**
     * 导入物品
     *
     * @param file
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean importData(MultipartFile file, boolean hasFlow) {
        List<AssetExcel> excels;
        try {
            InputStream in = file.getInputStream();
            ImportParams params = new ImportParams();
            params.setNeedVerify(true);
            params.setVerifyHandler(new AssetExcelVerifyHandler());
            ExcelImportResult<AssetExcel> excelImportResult = ExcelImportUtil.importExcelMore(in, AssetExcel.class, params);
            if (excelImportResult.isVerifyFail()) {
                String errorMsg = excelImportResult.getFailList().stream().map(v -> {
                    return "第" + v.getRowNum() + "行，" + v.getErrorMsg();
                }).collect(Collectors.joining("！"));
                throw new MaginaException(errorMsg);
            }
            excels = excelImportResult.getList();
            Map<String, List<AssetExcel>> stringListMap = excels.stream()
                    .filter(v -> StringUtils.isNotBlank(v.getOriRfidCode()))
                    .collect(Collectors.groupingBy(AssetExcel::getOriRfidCode));
            for (Map.Entry<String, List<AssetExcel>> entry : stringListMap.entrySet()) {
                List<AssetExcel> value = entry.getValue();
                if (value.size() > 1) {
                    throw new MaginaException("原资产标签号" + entry.getKey() + "本次导入存在重复数据");
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new MaginaException("导入失败！原因：" + e.getMessage());
        }

        //存数据库
        if (CollectionUtils.isNotEmpty(excels)) {
            RfidAssetFlow rfidAssetFlow = new RfidAssetFlow();

            List<RfidAsset> rfidAssets = new ArrayList<>();
            String code = sequenceDomainService.nextDateSequence(DictUtil.SEQ_ASSET_APPLY, 4);
            //日志流水记录
            UserInfo userInfo = userInfoDomainService.getCurrUser();
            for (AssetExcel data : excels) {
                RfidAsset rfidAsset = data.getOriRfidAsset() == null ? new RfidAsset() : data.getOriRfidAsset();
                BeanUtils.copyProperties(data, rfidAsset);
                rfidAsset.setPrice(data.getPrice());
                rfidAsset.setCardType(new DictItemEntry().setValue(data.getCardType()));
                rfidAsset.setAcquireMode(new DictItemEntry().setValue(data.getAcquireMode()));
                rfidAsset.setUseState(new DictItemEntry().setValue(data.getUseState()));
                DictItemEntry assetPurpose = new DictItemEntry().setValue(data.getAssetPurpose());
                rfidAsset.setAssetPurpose(assetPurpose);
                rfidAsset.setFinancialClassify(data.getFinancialClassifyList());
                rfidAsset.setEnterDate(data.getAccountDate());
                if (rfidAsset.getId() == null) {
                    rfidAsset.setCreatedBy(new UserEntry().setId(ApplicationSessions.id()));
                    rfidAsset.setCreateDept(userInfo.getDepartmentId());
                    rfidAsset.setStatus(AssetApplyStatus.IN_STOCK);
                    rfidAsset.setDataSource(1);
                }
                if (hasFlow) {
                    rfidAsset.setSignCode(code);
                } else {
                    rfidAsset.setStatus(AssetApplyStatus.IN_STOCK);
                    rfidAsset.setInStatus(true);
                }
                rfidAssets.add(rfidAsset);
                if (StringUtils.isNotBlank(rfidAsset.getParentCode())) {
                    rfidAssetServiceDomain.lambdaUpdate()
                            .set(RfidAsset::getType, DictUtil.HAS_CHILD)
                            .eq(RfidAsset::getCode, rfidAsset.getParentCode()).update();
                }
            }
            //父类处理
            List<String> codes = rfidAssets.stream().filter(v -> StringUtils.isNotBlank(v.getParentCode())).map(RfidAsset::getParentCode).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(codes)) {
                rfidAssets.stream().filter(v -> codes.contains(v.getCode())).forEach(v -> {
                    v.setType(DictUtil.HAS_CHILD);
                });
            }
            if (hasFlow) {
                rfidAssetFlow.setStatus(AssetFlowStatus.WAIT_APPROVE);
                rfidAssetFlow.setCode(code);
//                rfidAssetFlow.setInOutType(DictUtil.IN_STOCK);
                rfidAssetFlow.setCreatedBy(new UserEntry().setId(ApplicationSessions.id()));
                rfidAssetFlow.setCreateDept(userInfo.getDepartmentId());
                rfidAssetFlow.setDepartmentId(userInfo.getDepartmentId());
                rfidAssetFlow.setOperateType(AssetBusinessType.PURCHASE);
                if (userInfo.getDepartmentId() != null) {
                    Department department = departmentDomainService.getById(userInfo.getDepartmentId());
                    rfidAssetFlow.setDepartmentName(department != null ? department.getName().getValue() : null);
                }
                Long amount = rfidAssets.stream().filter(v -> v.getPrice() != null).mapToLong(RfidAsset::getPrice).sum();
                rfidAssetFlow.setAmount(amount);
                rfidAssetFlow.setNumber(rfidAssets.size());
                this.rfidAssetFlowServiceDomain.save(rfidAssetFlow);
            }
            this.rfidAssetServiceDomain.saveOrUpdateBatch(rfidAssets);
        }
        return Boolean.TRUE;
    }

    public Boolean clearAsset() {
        rfidAssetServiceDomain.clearAsset();
        return Boolean.TRUE;
    }
}
