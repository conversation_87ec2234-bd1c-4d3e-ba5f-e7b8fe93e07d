<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hightop.benyin.rfid.infrastructure.mapper.RfidVariationMapper">

    <select id="lastRfidRvariationList" resultType="com.hightop.benyin.rfid.infrastructure.entity.RfidVariation">
        select t.*,
               t1.name assetName,
               t1.code assetCode,
               t1.ori_rfid_code oriRfidCode,
               t1.model,
               t1.apply_name applyName,
        from (select rfid_code, max(id) id from b_rfid_info group by rfid_code) tt
                 left join b_rfid_info t on t.id = tt.id
                 left join b_rfid_asset t1 on t1.rfid_code = t.rfid_code
        <where>
            <if test="qo.rfidCode!= null and qo.rfidCode!= ''">
                and t.rfid_code like  concat ('%',#{qo.rfidCode},'%')
            </if>

            <if test="qo.model!= null and qo.model!= ''">
                and t1.model like  concat ('%',#{qo.model},'%')
            </if>

            <if test="qo.oriRfidCode!= null and qo.oriRfidCode!= ''">
                and t1.ori_rfid_code like  concat ('%',#{qo.oriRfidCode},'%')
            </if>

            <if test="qo.assetName!= null and qo.assetName!= ''">
                and t1.name like  concat ('%',#{qo.assetName},'%')
            </if>

            <if test="qo.assetCode!= null and qo.assetCode!= ''">
                and t1.code like  concat ('%',#{qo.assetCode},'%')
            </if>

            <if test="qo.applyName!= null and qo.applyName!= ''">
                and t1.apply_name like  concat ('%',#{qo.applyName},'%')
            </if>

            <if test="qo.location!= null and qo.location!= ''">
                and t.location like  concat ('%',#{qo.location},'%')
            </if>

            <if test="null != qo.startDate and '' != qo.startDate ">
                and t.created_at &gt;= concat(#{qo.startDate},' 00:00:00')
            </if>
            <if test="null != qo.endDate and '' != qo.endDate ">
                and t.created_at &lt;= concat(#{qo.endDate},' 23:59:59')
            </if>

        </where>
        order by t.rfid_code desc
    </select>

</mapper>