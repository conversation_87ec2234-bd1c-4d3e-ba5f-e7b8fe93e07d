package com.hightop.benyin.share.socket.service.impl;

import com.github.yulichang.toolkit.SpringContentUtils;
import com.hightop.benyin.rfid.application.service.RfidInfoService;
import com.hightop.benyin.rfid.domain.service.RfidAssetServiceDomain;
import com.hightop.benyin.rfid.domain.service.RfidReaderServiceDomain;
import com.hightop.benyin.rfid.infrastructure.entity.RfidAsset;
import com.hightop.benyin.rfid.infrastructure.entity.RfidReader;
import com.hightop.benyin.share.domain.event.ApiLogEvent;
import com.hightop.benyin.share.infrastructure.enums.MessageType;
import com.hightop.benyin.share.socket.enums.CommandType;
import com.hightop.benyin.share.socket.service.CommandHandler;
import com.hightop.benyin.share.socket.util.MsgUtil;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 上报日常扫到增加和减少的标签-命令处理器
 */
@Service
@Slf4j
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class ReportDisHandler implements CommandHandler {
    RfidReaderServiceDomain rfidReaderServiceDomain;
    RfidAssetServiceDomain rfidAssetServiceDomain;
    RfidInfoService rfidInfoService;

    @Override
    public void handle(String clientName, String deviceId, Integer length, String params) {
        log.info("上报日常扫到增加和减少的标签-命令处理器, clientName: {}, params: {}", clientName, params);
        ApplicationContext applicationContext = SpringContentUtils.getApplicationContext();
        RfidReader rfidReader = rfidReaderServiceDomain.lambdaQuery().eq(RfidReader::getIpAddr, clientName).one();
        if (rfidReader == null) {
            log.error("上报日常扫到增加和减少的标签-处理器, clientName: {},  rfidReader is null", clientName);
            ApiLogEvent apiLogEvent = new ApiLogEvent(this, MessageType.SEND, CommandType.DOWNLOAD_TAG,
                    deviceId, null, "ip地址找不到对应的基站！", false);
            applicationContext.publishEvent(apiLogEvent);
            return;
        }
        // 解析参数
        Integer firstParamIndex = length*MsgUtil.RFID_STR_LENGTH;
        String type = params.substring(0,2);
        if(length>0){
            String rfidStr = params.substring(2,firstParamIndex+2);
            List<String> rfidList = MsgUtil.splitString(rfidStr, MsgUtil.RFID_STR_LENGTH);
        }

        //处理2层数据
        String lengthStr =  params.substring(firstParamIndex+2,firstParamIndex+6);
        byte[] lenthBytes = MsgUtil.convertMessage(lengthStr);
        int  length2 = lenthBytes[0]  * 256+ lenthBytes[1];
        if(length2>0){
            String type2 =  params.substring(firstParamIndex+6,firstParamIndex+8);
            String secendParam =  params.substring(firstParamIndex+8,params.length());
            List<String> rfidLists = MsgUtil.splitString(secendParam, MsgUtil.RFID_STR_LENGTH);
        }

    }
}
