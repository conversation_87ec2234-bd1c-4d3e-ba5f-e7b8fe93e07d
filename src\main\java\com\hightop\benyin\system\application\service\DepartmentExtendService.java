package com.hightop.benyin.system.application.service;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.github.yulichang.toolkit.MPJWrappers;
import com.google.common.collect.Lists;
import com.hightop.benyin.share.domain.service.SequenceDomainService;
import com.hightop.benyin.share.infrastructure.util.DictUtil;
import com.hightop.benyin.share.infrastructure.util.DownloadResponseUtil;
import com.hightop.benyin.system.api.vo.DepartmentInfoTreeVo;
import com.hightop.benyin.system.api.vo.excel.DepartmentExcel;
import com.hightop.benyin.system.api.vo.query.DepartmentQuery;
import com.hightop.benyin.system.application.handler.DepartmentExcelVerifyHandler;
import com.hightop.benyin.system.domain.service.DepartmentInfoDomainService;
import com.hightop.benyin.system.domain.service.UserInfoDomainService;
import com.hightop.benyin.system.infrastructure.entity.DepartmentInfo;
import com.hightop.benyin.system.infrastructure.entity.Location;
import com.hightop.benyin.system.infrastructure.entity.UserInfo;
import com.hightop.fario.base.constant.StringConstants;
import com.hightop.fario.base.util.CollectionUtils;
import com.hightop.fario.base.util.StringUtils;
import com.hightop.fario.common.core.util.ExecutorUtils;
import com.hightop.magina.casual.session.ApplicationSessions;
import com.hightop.magina.core.custom.entry.TreeEntry;
import com.hightop.magina.core.exception.MaginaException;
import com.hightop.magina.standard.cipher.CipherText;
import com.hightop.magina.standard.ums.department.user.DepartmentUserDomainService;
import com.hightop.magina.standard.ums.role.Role;
import com.hightop.magina.standard.ums.role.RoleDomainService;
import com.hightop.magina.standard.ums.user.bind.UserEntry;
import com.hightop.magina.standard.ums.user.role.UserRole;
import com.hightop.magina.standard.ums.user.role.UserRoleDomainService;
import com.hightop.magina.standard.ums.user.role.UserRoleManageService;

import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;

/**
 * 部门管理服务
 *
 * <AUTHOR>
 * @date 2022/09/13 14:28
 * @since 2.0.0
 */
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Service
public class DepartmentExtendService {
    DepartmentInfoDomainService departmentInfoDomainService;
    DepartmentUserDomainService departmentUserDomainService;
    SequenceDomainService sequenceDomainService;
    LocationService locationTreeService;
    UserInfoDomainService userInfoDomainService;
    UserRoleManageService userRoleManageService;
    RoleDomainService roleDomainService;
    UserRoleDomainService userRoleDomainService;

    /**
     * 获得部门树
     *
     * @return {@link List}
     */
    public List<DepartmentInfoTreeVo> getDepartmentTree(DepartmentQuery pageQuery) {
        List<DepartmentInfoTreeVo> items = this.getDepartmentList(pageQuery);
        return TreeEntry.generate(items, DepartmentInfo.COMPARATOR, DepartmentInfo.TOP);
    }

    public List<DepartmentInfoTreeVo> getDepartmentList(DepartmentQuery pageQuery) {
        List<DepartmentInfoTreeVo> items = this.departmentInfoDomainService.getDepartmentList(pageQuery);
        if (CollectionUtils.isNotEmpty(items)) {
            List<DepartmentInfoTreeVo> departmentInfoTreeVos = items.stream().filter(item -> item.getParentId().equals(DepartmentInfo.TOP)).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(departmentInfoTreeVos)) {
                for (DepartmentInfoTreeVo departmentInfoTreeVo : items) {
                    while (!departmentInfoTreeVo.getParentId().equals(DepartmentInfo.TOP)) {
                        DepartmentInfoTreeVo parent = this.departmentInfoDomainService.getBaseMapper()
                                .selectJoinOne(
                                        DepartmentInfoTreeVo.class,
                                        MPJWrappers.<DepartmentInfo>lambdaJoin()
                                                .selectAll(DepartmentInfo.class)
                                                .eq(DepartmentInfo::getId, departmentInfoTreeVo.getParentId())
                                );
                        departmentInfoTreeVos.add(parent);
                        departmentInfoTreeVo = parent;
                    }
                }
                departmentInfoTreeVos.addAll(items);
                items = departmentInfoTreeVos.stream().distinct().collect(Collectors.toList());
            }
        }
        return items;
    }

    /**
     * 获得部门树
     *
     * @return {@link List}
     */
    public List<DepartmentInfoTreeVo> getAuthDepartmentTree(DepartmentQuery pageQuery) {
        if (!ApplicationSessions.code().equals(DictUtil.ADMIN)) {
            pageQuery.setCompanyCode(userInfoDomainService.getCurrCompany().getCode().getValue());
            String role = userInfoDomainService.getCurrRoleCode();
            //负责人查看自己负责的部门
            if (DictUtil.LEADER_ROLE.equals(role)) {
                List<String> departmentInfos = userInfoDomainService.getCurrDeptCodes();
                pageQuery.setDepartmentCodes(departmentInfos);
            }

            //员工查看自己部门
            if (DictUtil.STAFF_ROLE.equals(role)) {
                UserInfo userInfo = userInfoDomainService.getCurrUser();
                pageQuery.setId(userInfo.getDepartmentId());
            }
        }

        pageQuery.setIsEnable(true);

        List<DepartmentInfoTreeVo> items = this.departmentInfoDomainService.getDepartmentList(pageQuery);
        items = items.stream().filter(item -> !item.getParentId().equals(DepartmentInfo.TOP)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(items)) {
            List<DepartmentInfoTreeVo> departmentInfoTreeVos = items.stream().filter(item -> item.getDeptType().equals(DictUtil.TOP)).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(departmentInfoTreeVos)) {
                for (DepartmentInfoTreeVo departmentInfoTreeVo : items) {

                    while (!departmentInfoTreeVo.getDeptType().equals(DictUtil.TOP)) {
                        DepartmentInfoTreeVo parent = this.departmentInfoDomainService.getBaseMapper()
                                .selectJoinOne(
                                        DepartmentInfoTreeVo.class,
                                        MPJWrappers.<DepartmentInfo>lambdaJoin()
                                                .selectAll(DepartmentInfo.class)
                                                .eq(DepartmentInfo::getId, departmentInfoTreeVo.getParentId())
                                );
                        departmentInfoTreeVos.add(parent);
                        departmentInfoTreeVo = parent;
                    }
                }
            }
        }
        if (CollectionUtils.isNotEmpty(items)) {
            List<Long> parentIds = items.stream().map(DepartmentInfoTreeVo::getId).distinct().collect(Collectors.toList());
            for (DepartmentInfoTreeVo item : items) {
                //把当前部门最顶级设置为TOP层
                if (item.getDeptType().equals(DictUtil.TOP) && !parentIds.contains(item.getParentId())) {
                    item.setParentId(0L);
                }
            }
            return TreeEntry.generate(items, DepartmentInfo.COMPARATOR, DepartmentInfo.TOP);
        }
        return null;
    }

    /**
     * 递归获取
     * 根据当前部门，获取其子部门
     */
    private static List<DepartmentInfoTreeVo> createChildList(DepartmentInfoTreeVo father, List<DepartmentInfoTreeVo> list) {
        return list.stream().filter(model -> father.getId().equals(model.getParentId()))
                .peek(model -> {
                    List<DepartmentInfoTreeVo> children = createChildList(model, list);
                    model.setChildren(CollectionUtils.isEmpty(children) ? null : children);
                }).sorted(Comparator.comparing(DepartmentInfoTreeVo::getSort))
                .collect(Collectors.toList());
    }

    /**
     * 部门保存
     *
     * @param department {@link DepartmentInfo}
     * @return true/false
     */
    public boolean save(DepartmentInfo department) {
        if (Objects.isNull(department.getParentId())) {
            department.setParentId(DepartmentInfo.TOP);
            department.setDeptType(DictUtil.HAS_CHILD);
        } else {
            DepartmentInfo parent = this.departmentInfoDomainService.getById(department.getParentId());
            String code = sequenceDomainService.nextSequence(parent.getCode().getValue(), 2);
            Long codeCount = this.departmentInfoDomainService.lambdaQuery()
                    .eq(DepartmentInfo::getCode, code)
                    .eq(DepartmentInfo::getIsAvailable, true)
                    .ne(Objects.nonNull(department.getId()), DepartmentInfo::getId, department.getId())
                    .count();
            while (codeCount > 0L) {
                code = sequenceDomainService.nextSequence(parent.getCode().getValue(), 2);
                codeCount = this.departmentInfoDomainService.lambdaQuery()
                        .eq(DepartmentInfo::getCode, code)
                        .eq(DepartmentInfo::getIsAvailable, true)
                        .ne(Objects.nonNull(department.getId()), DepartmentInfo::getId, department.getId())
                        .count();
            }
            department.setCode(new CipherText(code));
        }
        this.checkDepartment(department);
        department.setId(IdWorker.getId());


        this.setFullIdPath(department);

        if (department.getParentId().equals(DepartmentInfo.TOP)) {
            //创建顶级部门和初始化位置
            Location location = new Location();
            location.setCode(department.getCode().getValue());
            location.setName(department.getName().getValue());
            location.setDepartmentIds(Lists.newArrayList(department.getId()));
            location.setSort(1);
            locationTreeService.save(location);
        }
        //需要给应该负责人添加资产负责人角色
        if (department.getManagerId() != null) {
            this.setManagerRole(department.getManagerId(), department.getId(), null);
        }
        return this.departmentInfoDomainService.save(department);
    }


    private void setManagerRole(Long managerId, Long departId, Long originManagerId) {
        Role role = roleDomainService.lambdaQuery().eq(Role::getCode, DictUtil.LEADER_ROLE).one();
        UserRole userRole = userRoleDomainService.lambdaQuery().eq(UserRole::getUserId, managerId).eq(UserRole::getRoleId, role.getId()).one();
        if (userRole == null) {
            userRoleDomainService.save(new UserRole(managerId, role.getId()));
        }
        if (Objects.nonNull(originManagerId) && !originManagerId.equals(managerId)) {
            List<DepartmentInfo> departmentInfos = departmentInfoDomainService.lambdaQuery()
                    .ne(departId != null, DepartmentInfo::getId, departId)
                    .eq(DepartmentInfo::getManagerId, originManagerId).list();
            if (CollectionUtils.isEmpty(departmentInfos)) {
                //清理原有角色
                userRoleManageService.remove(role.getId(), originManagerId);
            }
        }
    }

    /**
     * 部门更新
     *
     * @param department {@link DepartmentInfo}
     * @return true/false
     */
    public boolean updateById(DepartmentInfo department) {
        this.checkDepartment(department);
        // 原始记录
        DepartmentInfo origin = this.departmentInfoDomainService.getById(department.getId());
        Long oriManagerId = origin.getManagerId();
        // 仅当修改了父部门id时修改部门全路径
        if (!Objects.equals(department.getParentId(), origin.getParentId())) {
            // 按照父部门id设置id全路径
            this.setFullIdPath(department);
            department.setUpdatedBy(new UserEntry().setId(ApplicationSessions.id()));

            // 更新子部门id全路径
            List<DepartmentInfo> children = new ArrayList<>();
            BiConsumer<List<DepartmentInfo>, String> recursion = new BiConsumer<List<DepartmentInfo>, String>() {
                @Override
                public void accept(List<DepartmentInfo> list, String prefix) {
                    if (CollectionUtils.isEmpty(list)) {
                        return;
                    }

                    list.forEach(it -> {
                        it.setFullIdPath(prefix + section(it.getId()));
                        it.setUpdatedBy(new UserEntry().setId(ApplicationSessions.id()));

                        // 待更新数据
                        children.add(it);

                        // 递归子节点
                        Optional.ofNullable(
                                        departmentInfoDomainService.lambdaQuery()
                                                .eq(DepartmentInfo::getParentId, it.getId())
                                                .eq(DepartmentInfo::getIsAvailable, true)
                                                .list()
                                )
                                .filter(CollectionUtils::isNotEmpty)
                                .ifPresent(v -> this.accept(v, it.getFullIdPath()));
                    });
                }
            };
            recursion.accept(Collections.singletonList(department), department.getFullIdPath());
            return this.departmentInfoDomainService.updateBatchById(children);
        }
        //需要给应该负责人添加资产负责人角色
        if (department.getManagerId() != null) {
            this.setManagerRole(department.getManagerId(), department.getId(), oriManagerId);
        }
        return this.departmentInfoDomainService.updateById(department);
    }

    /**
     * 部门删除 逻辑删除
     *
     * @param id 部门id
     * @return 是否更新成功
     */
    public boolean removeById(Long id) {
        // 检查是否有下级
        long count =
                this.departmentInfoDomainService.lambdaQuery()
                        .eq(DepartmentInfo::getParentId, id)
                        .eq(DepartmentInfo::getIsAvailable, true)
                        .count();
        if (count > 0) {
            throw new MaginaException("存在子部门不允许删除");
        }

        // 是否存在用户
        if (this.departmentUserDomainService.members(id) > 0) {
            throw new MaginaException("存在部门成员不允许删除");
        }

        // 更新部门为不可用
        return this.departmentInfoDomainService.removeById(id);
    }

    /**
     * 部门启停
     *
     * @param id   部门id
     * @param flag 启停标识
     * @return 是否更新成功
     */
    public boolean updateEnable(Long id, Boolean flag) {
        DepartmentInfo department = this.departmentInfoDomainService.getById(id);
        if (Objects.isNull(department)) {
            throw new MaginaException("部门不存在");
        }
        if (!flag) {
            department.setExpireAt(LocalDateTime.now());
        } else {
            department.setExpireAt(null);
        }
        department.setIsEnable(flag);
        department.setUpdatedBy(new UserEntry().setId(ApplicationSessions.id()));
        return this.departmentInfoDomainService.updateById(department);
    }

    /**
     * 部门编码重复性校验
     *
     * @param department {@link DepartmentInfo}
     */
    protected void checkDepartment(DepartmentInfo department) {
        Long codeCount =
                this.departmentInfoDomainService.lambdaQuery()
                        .eq(DepartmentInfo::getCode, department.getCode())
                        .eq(DepartmentInfo::getIsAvailable, true)
                        .ne(Objects.nonNull(department.getId()), DepartmentInfo::getId, department.getId())
                        .count();

        if (codeCount > 0) {
            throw new MaginaException("部门编码已存在");
        }

        Long levelCount =
                this.departmentInfoDomainService.lambdaQuery()
                        .eq(DepartmentInfo::getParentId, department.getParentId())
                        .eq(DepartmentInfo::getName, department.getName())
                        .eq(DepartmentInfo::getIsAvailable, true)
                        .ne(Objects.nonNull(department.getId()), DepartmentInfo::getId, department.getId())
                        .count();

        if (levelCount > 0) {
            throw new MaginaException("同层级已存在相同部门名称");
        }
    }

    /**
     * 设置id全路径
     *
     * @param department {@link DepartmentInfo}
     */
    protected void setFullIdPath(DepartmentInfo department) {
        // 设置全路径id
        department.setFullIdPath(
                Optional.ofNullable(department.getParentId())
                        .map(this.departmentInfoDomainService::getById)
                        .map(DepartmentInfo::getFullIdPath)
                        .orElse(StringConstants.EMPTY)
                        + section(department.getId())
        );
    }

    /**
     * 路径分段
     *
     * @param id 部门id
     * @return 路径段
     */
    static String section(Long id) {
        return StringConstants.SLASH + id;
    }

    /**
     * 下载模板
     *
     * @param response
     * @return
     */
    public Boolean downTemplate(HttpServletResponse response) {
        List<DepartmentExcel> excelList = new ArrayList<>();
        try {
            //页面下载设置
            DownloadResponseUtil.addDownLoadHeader(response, "部门信息导入模板.xlsx");
            //将并添加信息，实体类结构，类的数据做成excel表格对象workbook
            Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams(),
                    DepartmentExcel.class, excelList
            );
            workbook.write(response.getOutputStream());
        } catch (IOException e) {
            e.printStackTrace();
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    /**
     * 导入用户
     *
     * @param file
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean importData(MultipartFile file) {
        List<DepartmentExcel> excels;
        try {
            InputStream in = file.getInputStream();
            ImportParams params = new ImportParams();
            params.setNeedVerify(true);
            params.setVerifyHandler(new DepartmentExcelVerifyHandler());
            ExcelImportResult<DepartmentExcel> excelImportResult = ExcelImportUtil.importExcelMore(in, DepartmentExcel.class, params);
            if (excelImportResult.isVerifyFail()) {
                String errorMsg = excelImportResult.getFailList().stream().map(v -> {
                    return "第" + v.getRowNum() + "行，" + v.getErrorMsg();
                }).collect(Collectors.joining("！"));
                throw new MaginaException(errorMsg);
            }
            excels = excelImportResult.getList();
            Map<String, List<DepartmentExcel>> stringListMap = excels.stream().collect(Collectors.groupingBy(DepartmentExcel::getCode));
            for (Map.Entry<String, List<DepartmentExcel>> entry : stringListMap.entrySet()) {
                List<DepartmentExcel> value = entry.getValue();
                if (value.size() > 1) {
                    throw new MaginaException("部门编码" + entry.getKey() + "本次导入存在重复数据");
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new MaginaException("导入失败！原因：" + e.getMessage());
        }

        if (CollectionUtils.isEmpty(excels)) {
            throw new MaginaException("导入失败！解析数据为空！");
        }
        //编码与id映射
        Map<String, Long> parentMap = excels.stream().collect(Collectors.toMap(DepartmentExcel::getCode, DepartmentExcel::getId));
        List<DepartmentInfo> departmentInfoList = excels.stream().map(v -> {
            DepartmentInfo departmentInfo = new DepartmentInfo();
            departmentInfo.setId(v.getId());
            departmentInfo.setName(new CipherText(v.getName()));
            departmentInfo.setCode(new CipherText(v.getCode()));
            departmentInfo.setManagerId(v.getManagerId());
            departmentInfo.setSort(v.getSort());
            departmentInfo.setSort(v.getSort());
            if (StringUtils.isNotBlank(v.getParentCode())) {
                if (v.getParentId() == null) {
                    departmentInfo.setParentId(parentMap.get(v.getParentCode()));
                } else {
                    departmentInfo.setParentId(v.getParentId());
                }
            } else {
                departmentInfo.setParentId(DepartmentInfo.TOP);
                departmentInfo.setDeptType(DictUtil.HAS_CHILD);
            }
            departmentInfo.setIsEnable(true);
            departmentInfo.setIsAvailable(true);
            departmentInfo.setUpdatedAt(LocalDateTime.now());
            departmentInfo.setUpdatedBy(new UserEntry().setId(ApplicationSessions.id()));
            return departmentInfo;
        }).collect(Collectors.toList());

        this.departmentInfoDomainService.saveOrUpdateBatch(departmentInfoList);
        List<DepartmentInfo> rootDepartments = departmentInfoList.stream().filter(v -> v.getParentId().equals(DepartmentInfo.TOP)).collect(Collectors.toList());
        // 批量设置id全路径
        ExecutorUtils.doAfterCommit(() -> {
            setFullIdPaths(departmentInfoList);
            //修改为导入部门数据时，不会把部门数据同步存到位置数据里面（修改过）
            /*rootDepartments.forEach(department -> {
                //创建顶级部门和初始化位置
                Location location = new Location();
                location.setCode(department.getCode().getValue());
                location.setName(department.getName().getValue());
                location.setDepartmentIds(Lists.newArrayList(department.getId()));
                location.setSort(1);
                locationTreeService.save(location);
            });*/
        });
        return true;
    }

    public void setFullIdPaths(List<DepartmentInfo> departments) {
        departments.forEach(this::setFullIdPath);
        this.departmentInfoDomainService.updateBatchById(departments);
    }

    public DepartmentInfo getById(Long id) {
        DepartmentInfo departmentInfo = this.departmentInfoDomainService.getById(id);
        departmentInfo.setDepartmentPath(this.getLocalFullPath(departmentInfo.getFullIdPath()));
        return departmentInfo;
    }


    /**
     * 获得位置全路径
     * 如：/1/2/3/4
     *
     * @param fullIdPath 位置全路径
     * @return 位置全路径
     */
    public String getLocalFullPath(String fullIdPath) {
        List<String> departmentIds = Arrays.stream(fullIdPath.split(StringConstants.SLASH))
                // 去掉头
                .skip(1).collect(Collectors.toList());
        List<DepartmentInfo> locationTrees = departmentInfoDomainService.lambdaQuery()
                .in(DepartmentInfo::getId, departmentIds).list();
        StringJoiner joiner = new StringJoiner(StringConstants.SLASH);

        for (DepartmentInfo departmentInfo : locationTrees) {
            joiner.add(departmentInfo.getName().getValue());
        }
        return joiner.toString();
    }

    /**
     * 获得位置全路径
     * 如：/1/2/3/4
     *
     * @param departmentInfos 位置全路径
     * @return 位置全路径
     */
    public List<String> getLocalFullPaths(List<DepartmentInfo> departmentInfos) {
        if (CollectionUtils.isEmpty(departmentInfos)) {
            return null;
        }
        List<String> fullPaths = new ArrayList<>();
        for (DepartmentInfo departmentInfo : departmentInfos) {
            fullPaths.add(this.getLocalFullPath(departmentInfo.getFullIdPath()));
        }
        return fullPaths;
    }

    /**
     * 导出部门信息
     *
     * @param pageQuery
     * @return
     */
    public Workbook downloadData(DepartmentQuery pageQuery) {
        //查询数据
        List<DepartmentInfoTreeVo> excelList = this.getDepartmentList(pageQuery);
        //将并添加信息，实体类结构，类的数据做成excel表格对象workbook
        return ExcelExportUtil.exportExcel(new ExportParams(), DepartmentInfo.class, excelList);
    }

    public Boolean clearDepartment() {
        departmentInfoDomainService.clearDepartment();
        return Boolean.TRUE;
    }

}
