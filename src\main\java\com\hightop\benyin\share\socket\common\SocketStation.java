package com.hightop.benyin.share.socket.common;

import com.github.yulichang.toolkit.SpringContentUtils;
import com.hightop.benyin.share.domain.event.ApiLogEvent;
import com.hightop.benyin.share.infrastructure.enums.MessageType;
import com.hightop.benyin.share.socket.enums.ErrorType;
import com.hightop.benyin.share.socket.event.CommandReceiveEvent;
import com.hightop.benyin.share.socket.util.MsgUtil;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.Socket;
import java.util.List;

/**
 * <AUTHOR> @version 1.0
 * @description: socket 连接对象 主要用于对已连接的客户端收发消息
 * @date 2023/6/19 14:43
 */
@Slf4j
@Getter
@Setter

public class SocketStation {

    /**
     * 已经连接的客户端对象
     */
    private Socket socket;

    /**
     * 这个客户端的名称（IP+端口）
     */
    private String clientName;

    /**
     * 设备id
     */
    private String deviceId;

    /**
     * 是否连接
     */
    private Boolean isConnect = false;

    // 添加持久缓存, 解决粘包/拆包问题
    private final java.io.ByteArrayOutputStream cache = new java.io.ByteArrayOutputStream();

    public SocketStation(Socket socket) {
        this.socket = socket;
        this.isConnect = true;
        this.clientName = socket.getInetAddress().getHostAddress() + ":" + socket.getPort();
    }

    public void handle() throws Exception {
        try {
            String clientName = socket.getInetAddress().getHostAddress() + ":" + socket.getPort();
            log.info(clientName + " 已连接");
            if (!socket.isConnected()) {
                return;
            }
            java.io.InputStream in = socket.getInputStream();
            byte[] buf = new byte[1024];
            int len;
            while ((len = in.read(buf)) != -1) {
                synchronized (cache) {
                    cache.write(buf, 0, len);
                    // 解析缓存中的数据
                    this.processCache();
                }
            }
        } catch (java.io.IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 解析 cache 中的数据, 每次尽可能提取完整消息
     */
    private void processCache() throws Exception {
        byte[] data = cache.toByteArray();
        int index = 0;
        while (data.length - index >= 5) { // 至少能读取到头+长度
            // 校验头
            int head1 = data[index] & 0xFF;
            int head2 = data[index + 1] & 0xFF;
            if (head1 != MsgUtil.HEAD_ONE || head2 != MsgUtil.HEAD_TWO) {
                // 移动指针跳过无效字节
                index++;
                continue;
            }
            int length = (data[index + 3] & 0xFF) * 256 + (data[index + 4] & 0xFF);
            int totalLen = 5 + length;
            if (data.length - index < totalLen) {
                // 数据不足一条完整消息, 等待更多数据
                break;
            }
            byte[] oneMsg = java.util.Arrays.copyOfRange(data, index, index + totalLen);
            this.receive(oneMsg);
            index += totalLen; // 继续解析下一条
        }
        // 清理已消费数据, 将剩余字节写回 cache
        cache.reset();
        if (index < data.length) {
            cache.write(data, index, data.length - index);
        }
    }

    /**
     * @description: 接收数据，对客户端接收的数据进行统一处理
     * 可以编写相应的处理逻辑，我这里是服务器端收到消息后。回复当前连接数量、
     * <AUTHOR> @date 2023/6/19 17:30
     * @version 1.0
     */
    public void receive(byte[] data) throws Exception {
        String message = MsgUtil.convertMessage(data);
        log.info("接收到客户端" + clientName + "消息：" + message);
        boolean msgFlag = MsgUtil.validateMessage(data, message);
        if (!msgFlag) {
            log.error("客户端" + clientName + "发送未知消息：" + message);
            ApplicationContext applicationContext = SpringContentUtils.getApplicationContext();
            if (applicationContext != null) {
                ApiLogEvent apiLogEvent = new ApiLogEvent(this, MessageType.RECEIVE, null,
                        message, deviceId, "无法识别消息", false);
                applicationContext.publishEvent(apiLogEvent);
            }
            return;
        }
        List<SocketMessage> socketMessageList = MsgUtil.getSocketMessageList(data, message);
        ApplicationContext applicationContext = SpringContentUtils.getApplicationContext();
        for (SocketMessage socketMessage : socketMessageList) {
            if (!socketMessage.getErrorType().equals(ErrorType.NO)) {
                log.error("客户端" + clientName + "发送错误消息：" + message);
                // 发布日志保存事件
                if (applicationContext != null) {
                    applicationContext.publishEvent(socketMessage.getApiLogEvent(deviceId, MessageType.RECEIVE, false));
                }
            } else {
                CommandReceiveEvent commandEvent = new CommandReceiveEvent(this, getClientName(), getDeviceId(),
                        socketMessage.getCommand(), socketMessage.getLength(), socketMessage.getData());
                // 发布命令事件
                applicationContext.publishEvent(commandEvent);
            }
        }
    }

    /**
     * @description: 发送数据
     * <AUTHOR> @date 2023/6/19 17:30
     * @version 1.0
     */
    public void send(SocketMessage socketMessage) {
        synchronized (this) {
            Boolean sendResult = false;
            log.info("向客户端" + clientName + "发送消息：" + socketMessage.toString());
            if (socketMessage.getErrorType().equals(ErrorType.NO)) {
                try {
                    socket.getOutputStream().write(socketMessage.toBytes());
                    socket.getOutputStream().flush();
                    // 发送间隔 10ms，防止硬件端连续粘包导致 index 错位
                    try {
                        Thread.sleep(100);
                    } catch (InterruptedException ignored) {
                    }
                    sendResult = true;
                } catch (IOException e) {
                    log.error("{}", e.getMessage());
                }
            }
            // 发布日志保存事件
            ApplicationContext applicationContext = SpringContentUtils.getApplicationContext();
            applicationContext.publishEvent(socketMessage.getApiLogEvent(deviceId, MessageType.SEND, sendResult));
        }
    }
    /**
     * @description: 断开连接
     * <AUTHOR> @date 2023/6/19 17:30
     * @version 1.0
     */
    public void disconnect() throws Exception {
        log.info(clientName + " 已断开");
        this.isConnect = false;
        socket.close();
    }

}
