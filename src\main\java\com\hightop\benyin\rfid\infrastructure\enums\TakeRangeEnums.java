package com.hightop.benyin.rfid.infrastructure.enums;

import com.hightop.magina.core.custom.entry.EnumEntry;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

/**
 * 盘点类型状态
 *
 * <AUTHOR>
 * @date 2024/5/15 13:42
 */
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Getter
public enum TakeRangeEnums implements EnumEntry<String> {

    /**
     * 选择部门盘点
     */
    DEPARTMENT("部门"),
    /**
     * 选择位置盘点
     */
    LOCATION("位置"),
    /**
     * 选择负责人员
     */
    USER("人员"),

    /**
     * 全单位
     */
    ALL("全单位");

    /**
     * 状态名称
     */
    String name;

    @Override
    public String getCode() {
        // 以枚举名称作为标识
        return super.name();
    }

    @Override
    public String getName() {
        return this.name;
    }

}
