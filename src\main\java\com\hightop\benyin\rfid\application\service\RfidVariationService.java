package com.hightop.benyin.rfid.application.service;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import com.github.yulichang.toolkit.MPJWrappers;
import com.google.common.collect.Lists;
import com.hightop.benyin.rfid.application.vo.dto.AssetApplyDto;
import com.hightop.benyin.rfid.application.vo.dto.AssetChangeLogDto;
import com.hightop.benyin.rfid.application.vo.dto.AssetTrajectoryDto;
import com.hightop.benyin.rfid.application.vo.dto.AssetVariationDealDto;
import com.hightop.benyin.rfid.application.vo.query.RfidVariationQuery;
import com.hightop.benyin.rfid.domain.event.AssetTrajectoryEvent;
import com.hightop.benyin.rfid.domain.service.RfidAssetServiceDomain;
import com.hightop.benyin.rfid.domain.service.RfidInfoServiceDomain;
import com.hightop.benyin.rfid.domain.service.RfidReaderServiceDomain;
import com.hightop.benyin.rfid.domain.service.RfidVariationServiceDomain;
import com.hightop.benyin.rfid.infrastructure.entity.RfidAsset;
import com.hightop.benyin.rfid.infrastructure.entity.RfidInfo;
import com.hightop.benyin.rfid.infrastructure.entity.RfidReader;
import com.hightop.benyin.rfid.infrastructure.entity.RfidVariation;
import com.hightop.benyin.rfid.infrastructure.enums.*;
import com.hightop.benyin.share.application.dto.MailSendDto;
import com.hightop.benyin.share.application.service.MailRecordService;
import com.hightop.benyin.share.domain.service.SequenceDomainService;
import com.hightop.benyin.share.infrastructure.entity.MailRecord;
import com.hightop.benyin.share.infrastructure.enums.MainChannel;
import com.hightop.benyin.share.infrastructure.util.DictUtil;
import com.hightop.benyin.share.infrastructure.util.DownloadResponseUtil;
import com.hightop.benyin.system.domain.service.DepartmentInfoDomainService;
import com.hightop.benyin.system.domain.service.LocationDomainService;
import com.hightop.benyin.system.domain.service.UserInfoDomainService;
import com.hightop.benyin.system.infrastructure.entity.AssetType;
import com.hightop.benyin.system.infrastructure.entity.DepartmentInfo;
import com.hightop.benyin.system.infrastructure.entity.Location;
import com.hightop.benyin.system.infrastructure.entity.UserInfo;
import com.hightop.fario.base.util.CollectionUtils;
import com.hightop.fario.base.util.StringUtils;
import com.hightop.fario.common.core.util.ExecutorUtils;
import com.hightop.fario.common.mybatis.DataGrid;
import com.hightop.fario.common.mybatis.PageQuery;
import com.hightop.fario.common.mybatis.util.PageHelper;
import com.hightop.magina.casual.session.ApplicationSessions;
import com.hightop.magina.core.exception.MaginaException;
import com.hightop.magina.standard.code.dictionary.bind.DictItemEntry;
import com.hightop.magina.standard.code.property.Property;
import com.hightop.magina.standard.code.property.PropertyDomainService;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Rfid异动数据管理服务
 *
 * <AUTHOR>
 * @date 2024-10-12 16:36:01
 */
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Service
public class RfidVariationService {

    RfidVariationServiceDomain rfidVariationServiceDomain;
    RfidAssetServiceDomain rfidAssetServiceDomain;
    SequenceDomainService sequenceDomainService;
    RfidAssetFlowService rfidAssetFlowService;
    RfidInfoServiceDomain rfidInfoServiceDomain;
    RfidReaderServiceDomain rfidReaderServiceDomain;
    LocationDomainService locationDomainService;
    RfidAssetService rfidAssetService;
    UserInfoDomainService userInfoDomainService;
    MailRecordService mailRecordService;
    DepartmentInfoDomainService departmentInfoDomainService;
    PropertyDomainService propertyDomainService;
    RfidReaderService rfidReaderService;
    ApplicationEventPublisher applicationEventPublisher;

    /**
     * Rfid异动数据分页查询
     *
     * @param pageQuery {@link PageQuery}
     * @return {@link DataGrid}
     */
    public DataGrid<RfidVariation> page(RfidVariationQuery pageQuery) {
        return PageHelper.startPage(pageQuery, p ->
                this.getRfidVariationList(pageQuery)
        );
    }

    private List<RfidVariation> getRfidVariationList(RfidVariationQuery pageQuery) {
        return this.rfidVariationServiceDomain.selectJoinList(RfidVariation.class, MPJWrappers.lambdaJoin()
                        .selectAll(RfidVariation.class)

                        .selectAs(RfidVariation::getChangeLocationId, RfidVariation::getChangeLocationId)
                        .selectAs(RfidVariation::getChangeLocation, RfidVariation::getChangeLocation)
                        .selectAs(RfidVariation::getChangeReader, RfidVariation::getChangeReader)
                        .selectAs(RfidVariation::getChangeReaderId, RfidVariation::getChangeReaderId)
                        .selectAs(RfidAsset::getApplyName, RfidVariation::getApplyName)
                        .selectAs(RfidInfo::getType, RfidVariation::getType)
                        .selectAs(RfidInfo::getRfidCode, RfidVariation::getRfidCode)
                        .selectAs(RfidInfo::getDepartmentName, RfidVariation::getDepartmentName)
                        .selectAs(RfidInfo::getAssetLocation, RfidVariation::getAssetLocation)
                        .selectAs(RfidInfo::getLocation, RfidVariation::getLocation)
                        .selectAs(RfidInfo::getAssetId, RfidVariation::getAssetId)
                        .selectAs(RfidInfo::getReaderId, RfidVariation::getReaderId)
                        .selectAs(RfidInfo::getDeviceId, RfidVariation::getDeviceId)
                        .selectAs(RfidInfo::getLocationId, RfidVariation::getLocationId)
                        .selectAs(RfidAsset::getCode, RfidVariation::getAssetCode)
                        .selectAs(RfidAsset::getName, RfidVariation::getAssetName)
                        .selectAs(RfidAsset::getModel, RfidVariation::getModel)
                        .selectAs(RfidAsset::getPrice, RfidVariation::getPrice)
                        .selectAs(RfidAsset::getAssetType, RfidVariation::getAssetType)
                        .selectAs(AssetType::getName, RfidVariation::getAssetTypeName)

                        .leftJoin(RfidInfo.class, RfidInfo::getId, RfidVariation::getInfoId)
                        .leftJoin(RfidAsset.class, RfidAsset::getId, RfidInfo::getAssetId)
                        .leftJoin(RfidReader.class, RfidReader::getId, RfidInfo::getReaderId)
                        .leftJoin(Location.class, Location::getId, RfidInfo::getLocationId)
                        .leftJoin(AssetType.class, AssetType::getCode, RfidAsset::getAssetType)

                        .eq(StringUtils.isNotBlank(pageQuery.getType()), RfidInfo::getType, pageQuery.getType())
                        .like(StringUtils.isNotBlank(pageQuery.getReaderCode()), RfidReader::getCode, pageQuery.getReaderCode())
                        .like(StringUtils.isNotBlank(pageQuery.getDeviceId()), RfidInfo::getDeviceId, pageQuery.getDeviceId())
                        .like(StringUtils.isNotBlank(pageQuery.getRfidCode()), RfidAsset::getRfidCode, pageQuery.getRfidCode())
                        .like(StringUtils.isNotBlank(pageQuery.getAssetCode()), RfidAsset::getCode, pageQuery.getAssetCode())
                        .like(StringUtils.isNotBlank(pageQuery.getAssetName()), RfidAsset::getName, pageQuery.getAssetName())
//                        .in(CollectionUtils.isNotEmpty(pageQuery.getDepartmentIds()), RfidInfo::getDepartmentId, pageQuery.getDepartmentIds())
                        .like(StringUtils.isNotBlank(pageQuery.getLocation()), RfidInfo::getLocation, pageQuery.getLocation())
                        .in(CollectionUtils.isNotEmpty(pageQuery.getProcessType()), RfidVariation::getProcessType, pageQuery.getProcessType())
                        .in(CollectionUtils.isNotEmpty(pageQuery.getTypes()), RfidInfo::getType, pageQuery.getTypes())
                        .in(CollectionUtils.isNotEmpty(pageQuery.getStatus()), RfidVariation::getStatus, pageQuery.getStatus())
                        .ge(StringUtils.isNotBlank(pageQuery.getStartDate()), RfidVariation::getCreatedAt, pageQuery.getStartDate() + " 00:00:00")
                        .le(StringUtils.isNotBlank(pageQuery.getEndDate()), RfidVariation::getCreatedAt, pageQuery.getEndDate() + " 23:59:59")
                        .like(StringUtils.isNotBlank(pageQuery.getAssetCode()), RfidAsset::getCode, pageQuery.getAssetCode())
                        .like(StringUtils.isNotBlank(pageQuery.getAssetName()), RfidAsset::getName, pageQuery.getAssetName())
                        .orderByDesc(RfidVariation::getCreatedAt)
        );
    }

    /**
     * 查询最后一次的RFID异动数据
     *
     * @param pageQuery {@link PageQuery}
     * @return {@link DataGrid}
     */
    public DataGrid<RfidVariation> lastRfidRvariationPage(RfidVariationQuery pageQuery) {
        return PageHelper.startPage(pageQuery, p ->
                this.rfidVariationServiceDomain.lastRfidRvariationList(pageQuery));
    }


    public List<DepartmentInfo> getChangeDept(Long infoId) {
        RfidInfo rfidInfo = this.rfidInfoServiceDomain.getById(infoId);
        List<Long> deptIds = rfidInfo.getDepartmentIds();
        return departmentInfoDomainService.lambdaQuery().in(DepartmentInfo::getId, deptIds).list();
    }


    @Transactional(rollbackFor = Exception.class)
    public Boolean instore(RfidAsset rfidAsset, Long id) {
        //保存资产
        rfidAsset.setDataSource(2);
        rfidAsset.setInStatus(true);
        rfidAsset.setUseState(new DictItemEntry().setValue(DictUtil.STANDBY));
        rfidAsset.setStatus(AssetApplyStatus.IN_STOCK);
        rfidAsset.setId(null);
        rfidAssetService.stash(rfidAsset);
        RfidVariation rfidVariation = rfidVariationServiceDomain.getById(id);
        RfidInfo rfidInfo = rfidInfoServiceDomain.getById(rfidVariation.getInfoId());
        rfidInfo.setAssetId(rfidAsset.getId());
        rfidInfoServiceDomain.updateById(rfidInfo);
        rfidVariation.setProcessType(ProcessType.INSTORE);
        rfidVariation.setStatus(VariationStatus.SUCCESS);
        return rfidVariationServiceDomain.updateById(rfidVariation);
    }


    /**
     * 批量处理
     *
     * @param assetVariationDealDto
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deal(AssetVariationDealDto assetVariationDealDto) {
        // 成功的情况，更新RFID资产信息
        RfidVariation rfidVariation = this.rfidVariationServiceDomain.selectJoinOne(RfidVariation.class, MPJWrappers.lambdaJoin()
                .selectAll(RfidVariation.class)
                .selectAs(RfidInfo::getType, RfidVariation::getType)
                .selectAs(RfidInfo::getAssetId, RfidVariation::getAssetId)
                .selectAs(RfidInfo::getReaderId, RfidVariation::getReaderId)
                .selectAs(RfidInfo::getLocationId, RfidVariation::getLocationId)
                .selectAs(RfidInfo::getKeeperId, RfidVariation::getApplyId)
                .leftJoin(RfidInfo.class, RfidInfo::getId, RfidVariation::getInfoId)
                .eq(RfidVariation::getId, assetVariationDealDto.getId())
                .orderByAsc(RfidVariation::getCreatedAt)
        );
        return this.dealRfidAsset(rfidVariation, assetVariationDealDto);
    }


    @Transactional(rollbackFor = Exception.class)
    public Boolean notice(MailSendDto mailSendDto) {
        mailRecordService.send(mailSendDto);
        RfidVariation rfidVariation = rfidVariationServiceDomain.getById(mailSendDto.getBusinessId());
        rfidVariation.setStatus(VariationStatus.NOTICE);
        rfidVariation.setUpdatedBy(ApplicationSessions.id());
        rfidVariation.setProcessType(ProcessType.NOTICE);
        Property property = propertyDomainService.getByCode(DictUtil.ABNORMAL_OVERTIME);
        int days = property == null ? 1 : Integer.parseInt(property.getValue());
        rfidVariation.setExpireTime(LocalDateTime.now().plusMinutes(days));

        return rfidVariationServiceDomain.updateById(rfidVariation);
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean complete(Long id) {
        RfidVariation rfidVariation = rfidVariationServiceDomain.getById(id);
        rfidVariation.setStatus(VariationStatus.SUCCESS);
        rfidVariation.setUpdatedBy(ApplicationSessions.id());

        rfidInfoServiceDomain.lambdaUpdate()
                .set(RfidInfo::getStatus, DictUtil.DEAL)
                .eq(RfidInfo::getId, rfidVariation.getInfoId()).update();

        return rfidVariationServiceDomain.updateById(rfidVariation);
    }

    public MailRecord noticeInfo(Long id) {
        // 成功的情况，更新RFID资产信息
        RfidVariation rfidVariation = this.rfidVariationServiceDomain.selectJoinOne(RfidVariation.class, MPJWrappers.lambdaJoin()
                .selectAll(RfidVariation.class)
                .selectAs(RfidInfo::getType, RfidVariation::getType)
                .selectAs(RfidInfo::getRfidCode, RfidVariation::getRfidCode)
                .selectAs(RfidAsset::getName, RfidVariation::getAssetName)
                .selectAs(RfidAsset::getCode, RfidVariation::getAssetCode)
                .selectAs(RfidInfo::getType, RfidVariation::getType)
                .selectAs(RfidInfo::getAssetId, RfidVariation::getAssetId)
                .selectAs(RfidInfo::getReaderId, RfidVariation::getReaderId)
                .selectAs(RfidInfo::getLocationId, RfidVariation::getLocationId)
                .selectAs(RfidInfo::getLocation, RfidVariation::getLocation)
                .selectAs(RfidInfo::getAssetLocation, RfidVariation::getAssetLocation)
                .selectAs(RfidInfo::getKeeperId, RfidVariation::getApplyId)
                .selectAs(RfidInfo::getManagerId, RfidVariation::getManagerId)
                .leftJoin(RfidInfo.class, RfidInfo::getId, RfidVariation::getInfoId)
                .leftJoin(RfidAsset.class, RfidAsset::getId, RfidInfo::getAssetId)
                .in(RfidVariation::getId, id)
        );
        MailRecord mailRecord = new MailRecord();

        mailRecord.setChannel(MainChannel.ABNORMAL);
        mailRecord.setBusinessId(rfidVariation.getId());
        //迁出
        if (rfidVariation.getType().getValue().equals(DictUtil.SUB_SCAN)) {
            Long userId = rfidVariation.getApplyId() != null ? rfidVariation.getApplyId() : rfidVariation.getManagerId();
            UserInfo userInfo = null;
            if (userId != null) {
                userInfo = this.userInfoDomainService.getUserFullInfo(userId);
                mailRecord.setSendTo(Lists.newArrayList(userId));
                mailRecord.setSendToName(userInfo.getName() + "(" + userInfo.getEmail().getValue() + ")");
            }
            //未找到该资产，需要通知责任人
            mailRecord.setSubject(DictUtil.TAKE_CH_SUBJECT);
            String content = String.format(DictUtil.TAKE_CH_CONTENT,
                    rfidVariation.getAssetName(), rfidVariation.getAssetCode(), rfidVariation.getRfidCode(), userInfo.getName(), userInfo.getMobileNumber() == null ? "无" : userInfo.getMobileNumber().getValue(), rfidVariation.getAssetLocation()
                    , rfidVariation.getLocation());
            mailRecord.setContent(content);
        }
        //迁入
        if (rfidVariation.getType().getValue().equals(DictUtil.ADD_SCAN)) {
            //找部门资产负责人 报溢才会有
            Long locationId = rfidVariation.getLocationId();
            Location location = this.locationDomainService.getById(locationId);
            if (location != null) {
                List<Long> departmentIds = location.getDepartmentIds();
                List<DepartmentInfo> departmentInfos = this.departmentInfoDomainService.lambdaQuery()
                        .in(DepartmentInfo::getId, departmentIds)
                        .list();

                List<Long> userIds = departmentInfos.stream().map(DepartmentInfo::getManagerId).collect(Collectors.toList());
                List<String> sendTos = Lists.newArrayList();
                for (Long uid : userIds) {
                    UserInfo user = this.userInfoDomainService.getUserFullInfo(uid);
                    sendTos.add(user.getName() + "(" + user.getEmail().getValue() + ")");
                }
                mailRecord.setSendTo(userIds);
                mailRecord.setSendToName(String.join(",", sendTos));
            }
            if (rfidVariation.getAssetId() == null) {
                //发现新标签 未关联资产
                mailRecord.setSubject(DictUtil.TAKE_SURPLUS_SUBJECT);
                String content = String.format(DictUtil.TAKE_SURPLUS_CONTENT, rfidVariation.getRfidCode(), rfidVariation.getLocation());
                mailRecord.setContent(content);
            } else {
                //发现新标签 已关联资产
                mailRecord.setSubject(DictUtil.TAKE_OVER_SUBJECT);
                String content = String.format(DictUtil.TAKE_SURPLUS_CONTENT, rfidVariation.getRfidCode(), rfidVariation.getLocation());
                mailRecord.setContent(content);
            }
        }
        return mailRecord;
    }


    private boolean dealRfidAsset(RfidVariation rfidVariation, AssetVariationDealDto assetVariationDealDto) {
        List<Long> readerSendIds = Lists.newArrayList();

        rfidVariation.setProcessType(assetVariationDealDto.getProcessType());
        rfidVariation.setStatus(VariationStatus.SUCCESS);

        if (assetVariationDealDto.getProcessType().equals(ProcessType.CHANGE)||
                assetVariationDealDto.getProcessType().equals(ProcessType.TURNOVER)||
                assetVariationDealDto.getProcessType().equals(ProcessType.VARIATION)) {
            Long readerId = rfidVariation.getReaderId();
            RfidAsset rfidAsset = this.rfidAssetServiceDomain.getById(rfidVariation.getAssetId());
            //更新资产信息
            Long oriReaderId = rfidAsset.getReaderId();

            if (assetVariationDealDto.getChangeApplyDeptId() != null) {
                if (assetVariationDealDto.getChangeApplyId() == null) {
                    throw new MaginaException("请选择对应保管人员！");
                }
                UserInfo userInfo = userInfoDomainService.getById(assetVariationDealDto.getChangeApplyId());
                rfidAsset.setDepartmentId(userInfo.getDepartmentId());
                rfidAsset.setApplyId(userInfo.getId());
                rfidAsset.setApplyName(userInfo.getName());
                rfidAsset.setApplyAt(LocalDateTime.now());
            }
            rfidVariation.setReaderId(assetVariationDealDto.getChangeReaderId() == null ? rfidVariation.getReaderId() : assetVariationDealDto.getChangeReaderId());
            rfidVariation.setLocationId(assetVariationDealDto.getChangeLocationId() == null ? rfidVariation.getLocationId() : assetVariationDealDto.getChangeLocationId());
            rfidVariation.setChangeApplyDeptId(assetVariationDealDto.getChangeApplyDeptId());
            rfidVariation.setChangeApplyId(assetVariationDealDto.getChangeApplyId());
            rfidVariation.setChangeLocationId(assetVariationDealDto.getChangeLocationId());
            rfidVariation.setChangeReaderId(assetVariationDealDto.getChangeReaderId());
            if(assetVariationDealDto.getChangeLocationId()!=null){
                Location location = this.locationDomainService.getById(assetVariationDealDto.getChangeLocationId());
                rfidVariation.setChangeLocation(location.getFullName());
            }
            if(assetVariationDealDto.getChangeReaderId()!=null){
                RfidReader rfidReader = this.rfidReaderServiceDomain.getById(assetVariationDealDto.getChangeReaderId());
                rfidVariation.setChangeReader(rfidReader.getDeviceId());
            }
            if (readerId != null) {
                //查找是否有未处理的迁出上报
                List<RfidVariation> outVariations = this.rfidVariationServiceDomain.selectJoinList(RfidVariation.class, MPJWrappers.lambdaJoin()
                        .selectAll(RfidVariation.class)
                        .leftJoin(RfidInfo.class, RfidInfo::getId, RfidVariation::getInfoId)
                        .eq(RfidInfo::getAssetId, rfidAsset.getId())
                        .eq(RfidInfo::getType, DictUtil.SUB_SCAN)
                        .eq(RfidVariation::getStatus, VariationStatus.WAIT)
                );

                if (CollectionUtils.isNotEmpty(outVariations)) {
                    outVariations.forEach(outVariation -> {
                        outVariation.setStatus(VariationStatus.SUCCESS);
                        outVariation.setUpdatedBy(ApplicationSessions.id());
                        outVariation.setRemark("已迁入" + rfidVariation.getChangeReader() + "号基站！");
                        this.rfidVariationServiceDomain.updateById(outVariation);
                        //更新RFID上报信息表
                        rfidInfoServiceDomain.lambdaUpdate()
                                .set(RfidInfo::getStatus, DictUtil.DEAL)
                                .eq(RfidInfo::getId, outVariation.getInfoId()).update();
                        readerSendIds.add(oriReaderId);
                    });
                }
            }
            readerSendIds.add(readerId);

            rfidAsset.setReaderId( assetVariationDealDto.getChangeReaderId());
            rfidAsset.setLocationId( assetVariationDealDto.getChangeLocationId());
            this.rfidAssetServiceDomain.updateById(rfidAsset);
        }
        //报损
        if (assetVariationDealDto.getProcessType().equals(ProcessType.DAMAGE)) {
            RfidAsset rfidAsset = this.rfidAssetServiceDomain.getById(rfidVariation.getAssetId());
            rfidAsset.setUseState(new DictItemEntry().setValue(DictUtil.DAMAGE));
            rfidAsset.setBreakageAt(LocalDate.now());
            rfidAsset.setUpdatedBy(ApplicationSessions.id());
            this.rfidAssetServiceDomain.updateById(rfidAsset);
        }
        rfidVariation.setUpdatedBy(ApplicationSessions.id());
        rfidInfoServiceDomain.lambdaUpdate().
                set(RfidInfo::getStatus, DictUtil.DEAL)
                .eq(RfidInfo::getId, rfidVariation.getInfoId()).
                update();
        this.rfidVariationServiceDomain.saveOrUpdate(rfidVariation);
        List<AssetApplyDto> assetApplyDtos = this.buildFlowDtos(rfidVariation);

        ExecutorUtils.doAfterCommit(() -> {
            //处理业务记录
            if (CollectionUtils.isNotEmpty(assetApplyDtos)) {
                assetApplyDtos.forEach(assetApplyDto -> {
                    rfidAssetFlowService.apply(assetApplyDto);
                });
            }
            rfidReaderService.sendAssetBatch(readerSendIds);
        });
        return true;
    }


    /**
     * 构建业务申请流程
     *
     * @param rfidVariation
     * @return
     */
    public List<AssetApplyDto> buildFlowDtos(RfidVariation rfidVariation) {
        List<AssetApplyDto> assetApplyDtos = Lists.newArrayList();

        AssetApplyDto assetApplyDto = new AssetApplyDto();
        assetApplyDto.setAutoAudit(true);
        switch (rfidVariation.getProcessType()) {
            case CHANGE: //变更
                if (rfidVariation.getChangeApplyId() != null) {
                    assetApplyDto.setOperateType(AssetBusinessType.APPLY);
                    assetApplyDto.setApplyId(rfidVariation.getChangeApplyId());
                    //原保管人不为空 需要多走一条退库流程
                    if (rfidVariation.getApplyId() != null && rfidVariation.getApplyId().equals(rfidVariation.getChangeApplyId())) {
                        AssetApplyDto returnApply = new AssetApplyDto();
                        returnApply.setAutoAudit(true);
                        returnApply.setOperateType(AssetBusinessType.RETURN);
                        returnApply.setIds(Lists.newArrayList(rfidVariation.getAssetId()));
                        returnApply.setApplyAt(LocalDateTime.now());
                        returnApply.setApplyId(rfidVariation.getApplyId());
                        returnApply.setApproveId(ApplicationSessions.id());
                        returnApply.setApproveName(ApplicationSessions.name());
                        returnApply.setRemark("异动单" + rfidVariation.getCode() + "发起");
                        assetApplyDtos.add(returnApply);
                    }
                }
                break;
            case DAMAGE:     //损坏
                assetApplyDto.setOperateType(AssetBusinessType.BREAKAGE);
                assetApplyDto.setApplyId(ApplicationSessions.id());
                break;
            case INSTORE:     //报溢
                assetApplyDto.setOperateType(AssetBusinessType.OVERFLOW);
                assetApplyDto.setApplyId(ApplicationSessions.id());
                break;
            default:
                return null;
        }
        assetApplyDto.setIds(Lists.newArrayList(rfidVariation.getAssetId()));
        assetApplyDto.setApplyAt(LocalDateTime.now());
        assetApplyDto.setApproveId(ApplicationSessions.id());
        assetApplyDto.setApproveName(ApplicationSessions.name());
        assetApplyDto.setRemark("异动单" + rfidVariation.getCode() + "发起");
        assetApplyDtos.add(assetApplyDto);
        return assetApplyDtos;
    }


    /**
     * 保存变更日志
     *
     * @param rfidAsset
     * @param rfidVariation
     * @return
     */
    private AssetChangeLogDto buildAssetChangeLogs(RfidAsset rfidAsset, RfidVariation rfidVariation) {
        AssetChangeLogDto assetChangeLogDto = AssetChangeLogDto.builder()
                .assetId(rfidAsset.getId())
                .changeCode(rfidVariation.getCode())
                .createdBy(ApplicationSessions.id())
                .source(AssetChangeSource.VARIATION)
                .operatType(AssetOperatType.CHANGE)
                .build();

        Location newLocation = locationDomainService.getById(rfidVariation.getLocationId());
        if (rfidVariation.getType().equals(DictUtil.ADD)) {
            if (Objects.nonNull(rfidAsset.getLocationId())) {
                Location location = locationDomainService.getById(rfidAsset.getLocationId());
                assetChangeLogDto.setBefore(location.getLocation());
            }
            assetChangeLogDto.setAfter(newLocation.getLocation());
        } else {
            if (Objects.nonNull(rfidAsset.getLocationId())) {
                Location location = locationDomainService.getById(rfidAsset.getLocationId());
                assetChangeLogDto.setBefore(newLocation.getLocation());
            }
            assetChangeLogDto.setAfter(null);
        }
        return assetChangeLogDto;
    }


    /**
     * Rfid异动数据添加
     *
     * @param rfidInfoList {@link RfidVariation}
     * @return true/false
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean save(List<RfidInfo> rfidInfoList) {
        List<RfidVariation> rfidVariations = Lists.newArrayList();

        for (RfidInfo rfidInfo : rfidInfoList) {

            //是否有其他上报
            List<RfidInfo> rfidInfoOthers = rfidInfoServiceDomain.lambdaQuery()
                    .eq(RfidInfo::getRfidCode, rfidInfo.getRfidCode())
                    .ne(RfidInfo::getId, rfidInfo.getId())
                    .in(RfidInfo::getType, Lists.newArrayList(DictUtil.ADD_SCAN, DictUtil.SUB_SCAN))
                    .in(RfidInfo::getStatus, Lists.newArrayList(DictUtil.REPORT, DictUtil.VARIATION))
                    .orderByDesc(RfidInfo::getLastReportTime).list();

            RfidVariation rfidVariation = rfidVariationServiceDomain.lambdaQuery()
                    .eq(RfidVariation::getRfidCode, rfidInfo.getRfidCode())
                    .eq(RfidVariation::getStatus, VariationStatus.WAIT)
                    .one();
            if (rfidVariation == null) {
                rfidVariation = new RfidVariation();
                rfidVariation.setInfoId(rfidInfo.getId());
                rfidVariation.setRfidCode(rfidInfo.getRfidCode());
                rfidVariation.setCode(sequenceDomainService.nextDateSequence("CH", 4));
            }
            //是否有其他上报
            if (CollectionUtils.isNotEmpty(rfidInfoOthers)) {
                if(rfidInfo.getType().getValue().equals(DictUtil.ADD_SCAN) ){
                    rfidVariation.setScanLocationId(rfidInfo.getLocationId());
                    rfidVariation.setScanLocation(rfidInfo.getLocation());
                    rfidVariation.setScanReaderId(rfidInfo.getReaderId());
                    rfidVariation.setScanReader(rfidInfo.getDeviceId());
                    rfidVariation.setLastInfoId(rfidInfo.getId());
                }
                //第二次上报都是位置变动
                rfidVariation.setAbnormalType(AbnormalType.VARIATION);
                if (rfidInfoOthers.size() > 1) {
                    //如果有多个上报 则将之前其他上报的状态改为忽略
                    for (int i = 0; i < rfidInfoOthers.size(); i++) {
                        if (i < rfidInfoOthers.size() - 1) {
                            RfidInfo rfidInfo1 = rfidInfoOthers.get(i);
                            rfidInfo1.setStatus(4);
                            rfidInfoServiceDomain.updateById(rfidInfo1);
                        }
                    }
                }
            } else {
                if (rfidInfo.getType().getValue().equals(DictUtil.ADD_SCAN)) {
                    //日常是否扫描
                    if (rfidInfo.getIsScan()) {
                        rfidVariation.setAbnormalType(AbnormalType.VARIATION);
                        rfidVariation.setScanLocationId(rfidInfo.getLocationId());
                        rfidVariation.setScanLocation(rfidInfo.getLocation());
                        rfidVariation.setScanReaderId(rfidInfo.getReaderId());
                        rfidVariation.setScanReader(rfidInfo.getDeviceId());
                    } else {
                        //日常不扫描 报迁出未知 因为不会报迁出
                        rfidVariation.setAbnormalType(AbnormalType.UNKNOWN);
                    }
                }
                //日常不扫描 不会报迁出 所以只考虑要扫描的迁出情况  第一次设置是迁出未知
                if (rfidInfo.getType().getValue().equals(DictUtil.SUB_SCAN)) {
                    rfidVariation.setAbnormalType(AbnormalType.OUT_UNKNOWN);
                }
            }

            //迁出记录轨迹
            if(rfidInfo.getType().getValue().equals(DictUtil.SUB_SCAN)){
                AssetTrajectoryDto assetTrajectoryDto= AssetTrajectoryDto.builder()
                        .assetId(rfidInfo.getAssetId())
                        .rfidCode(rfidInfo.getRfidCode())
                        .locationId(rfidInfo.getLocationId())
                        .location(rfidInfo.getLocation())
                        .deviceId(rfidInfo.getDeviceId())
                        .readerId(rfidInfo.getReaderId())
                        .trajectorySource(TrajectorySource.VARIATION)
                        .trajectoryType(TrajectoryType.OUT)
                        .build();
                List<AssetTrajectoryDto> assetTrajectoryDtos = Lists.newArrayList(assetTrajectoryDto);
                AssetTrajectoryEvent assetTrajectoryEvent= new AssetTrajectoryEvent().setAssetTrajectoryDtos(assetTrajectoryDtos);
                applicationEventPublisher.publishEvent(assetTrajectoryEvent);
            }
            rfidVariation.setStatus(VariationStatus.WAIT);
            rfidVariation.setAbnormalAnalyze(this.getAbnormalAnalyze(rfidVariation,rfidInfo));
            rfidVariations.add(rfidVariation);
            rfidInfoServiceDomain.lambdaUpdate()
                    .set(RfidInfo::getStatus, DictUtil.VARIATION)
                    .eq(RfidInfo::getId, rfidVariation.getInfoId()).update();
            if(rfidVariation.getLastInfoId() != null){
                rfidInfoServiceDomain.lambdaUpdate()
                        .set(RfidInfo::getStatus, DictUtil.VARIATION)
                        .eq(RfidInfo::getId, rfidVariation.getLastInfoId()).update();
            }
        }
        return this.rfidVariationServiceDomain.saveOrUpdateBatch(rfidVariations);
    }


    /**
     * 获取异常分析
     *
     * @param rfidInfo
     * @return
     */
    public String getAbnormalAnalyze(RfidVariation rfidVariation, RfidInfo rfidInfo) {
        String abnormalAnalyze = "";

        RfidAsset rfidAsset = rfidAssetServiceDomain.selectJoinOne(RfidAsset.class, MPJWrappers.lambdaJoin()
                .select(RfidAsset::getIsTake, RfidAsset::getIsTakeStatis, RfidAsset::getRfidCode, RfidAsset::getName, RfidAsset::getCode)
                        .selectAs(Location::getFullName,RfidAsset::getCurrLocation)
                        .leftJoin(Location.class, Location::getId, RfidAsset::getCurrLocationId)
                .eq(RfidAsset::getId, rfidInfo.getAssetId()));

//        Long userId = rfidInfo.getKeeperId() != null ? rfidInfo.getKeeperId() : rfidInfo.getManagerId();
//        UserInfo userInfo = this.userInfoDomainService.getUserFullInfo(userId);
//        String mobileNumber = userInfo.getMobileNumber() == null ? "无" : userInfo.getMobileNumber().getValue();
        switch (rfidVariation.getAbnormalType()) {
            case VARIATION:
                abnormalAnalyze = String.format(DictUtil.VARIATION_MOVE_HEAD,
                        rfidAsset.getName(),rfidInfo.getAssetLocation(),rfidVariation.getScanLocation());
                break;
            case UNKNOWN:
                abnormalAnalyze = String.format(DictUtil.VARIATION_UNKNOW_HEAD,
                        rfidAsset.getName(),
                        rfidInfo.getAssetLocation(),
                        rfidAsset.getCurrLocation());
                break;
            case OUT_UNKNOWN:
                abnormalAnalyze = String.format(DictUtil.VARIATION_LOST_HEAD,
                        rfidInfo.getAssetLocation(), rfidInfo.getAssetLocation(),
                        rfidAsset.getCurrLocation());
                break;
        }
        return abnormalAnalyze;
    }


    /**
     * 导出资产流程
     *
     * @param response
     * @return
     */
    public Boolean downloadData(HttpServletResponse response, RfidVariationQuery pageQuery) throws IOException {
        try {
            //查询数据
            List<RfidVariation> excelList = this.getRfidVariationList(pageQuery);
            //页面下载设置
            DownloadResponseUtil.addDownLoadHeader(response, "异动资产数据.xlsx");
            //将并添加信息，实体类结构，类的数据做成excel表格对象workbook
            Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams(), RfidVariation.class, excelList);
            workbook.write(response.getOutputStream());
        } catch (IOException e) {
            e.printStackTrace();
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }


    public Boolean clear() {
        rfidVariationServiceDomain.clear();
        return Boolean.TRUE;
    }

}
