package com.hightop.benyin.system.api.vo;

import com.hightop.benyin.system.infrastructure.entity.AssetType;
import com.hightop.magina.core.custom.entry.TreeEntry;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldDefaults;

import java.util.List;

/**
 * 部门管理树节点
 *
 * <AUTHOR>
 * @date 2022/09/13 21:17
 * @since 2.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AssetTypeTreeVo extends AssetType implements TreeEntry<Long, AssetTypeTreeVo> {

    @ApiModelProperty("子类型")
    List<AssetTypeTreeVo> children;
}
