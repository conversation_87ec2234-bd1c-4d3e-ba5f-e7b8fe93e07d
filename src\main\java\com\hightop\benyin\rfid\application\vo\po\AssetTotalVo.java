package com.hightop.benyin.rfid.application.vo.po;

import com.hightop.fario.common.jackson.annotation.JsonAmount;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@ApiModel("资产DTO")
public class AssetTotalVo {

    @ApiModelProperty("金额")
    @JsonAmount
    Long amount;

    @ApiModelProperty("总数量")
    Integer number;

    @ApiModelProperty("应贴数量")
    long waitBind;

    @ApiModelProperty("已绑数量")
    long waitRerader;


    @ApiModelProperty("导入数量")
    long impotNum;

    @ApiModelProperty("新增数量")
    long addNum;

    @ApiModelProperty("扫描数量")
    long scanNum;

    @ApiModelProperty("应贴数量")
    long tagNum;

    @ApiModelProperty("已绑数量")
    long bindNum;
}
