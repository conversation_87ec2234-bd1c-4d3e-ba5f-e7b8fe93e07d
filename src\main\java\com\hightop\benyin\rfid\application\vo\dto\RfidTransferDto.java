package com.hightop.benyin.rfid.application.vo.dto;

import com.hightop.benyin.rfid.infrastructure.enums.TransferType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * 盘点DTO
 *
 * @Author: X.S
 * @date 2024/09/29 13:58
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@ApiModel("资产变更DTO")
public class RfidTransferDto {

    @ApiModelProperty(value = "盘点类型")
    @NotNull(message = "资产明细不能为空")
    List<Long> assetIds;

    @ApiModelProperty(value = "id")
    Long id;

    @ApiModelProperty(value = "变更人")
    @NotNull(message = "变更人不能为空")
    Long applyId;

    @ApiModelProperty(value = "接手人")
    String applyName;

    @ApiModelProperty("变更人部门id")
    Long departmentId;

    @ApiModelProperty(value = "接手人")
    @NotNull(message = "接手人不能为空")
    Long newApplyId;

    @ApiModelProperty(value = "接手人")
    String newApplyName;

    @ApiModelProperty("接手人部门id")
    @NotNull(message = "接手人部门不能为空")
    Long newDepartmentId;

    @ApiModelProperty(value = "变更日期")
    @NotBlank(message = "变更日期不能为空")
    String transferDate;

    @ApiModelProperty("备注")
    String remark;
}