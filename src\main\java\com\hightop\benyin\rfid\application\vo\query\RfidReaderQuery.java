package com.hightop.benyin.rfid.application.vo.query;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.hightop.fario.common.mybatis.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldDefaults;

import java.util.List;

/**
 * RFID基站查询DTO
 * <AUTHOR>
 * @date 2024-10-12 16:36:01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@FieldDefaults(level = AccessLevel.PRIVATE)
@ApiModel("RFID基站查询DTO")
public class RfidReaderQuery extends PageQuery {

    @ApiModelProperty("ids")
    List<Long> ids;

    @ApiModelProperty("编码")
    String code;

    @ApiModelProperty("位置编码")
    String locationCode;

    @ApiModelProperty("设备id")
    String deviceId;


    @ApiModelProperty("所属单位")
    List<Long> departmentIds;

    @ApiModelProperty("位置")
    List<Long> locations;

    @ApiModelProperty("ip")
    String ipAddr;

    @ApiModelProperty("修改人")
    String updatedByName;

    @ApiModelProperty("状态")
    String status;

    @ApiModelProperty("扫描状态")
    Integer scanStatus;

    @ApiModelProperty("节能状态1：开启 2：关闭")
    Integer energyStatus;

    @ApiModelProperty("蜂鸣器开关1：开启 2：关闭")
    Integer bellStatus;

    @ApiModelProperty("启停状态1 启用 0 停用")
    Boolean isEnable;

    @ApiModelProperty("异动上报 1开启 0 关闭")
    Boolean  isReport;

    @ApiModelProperty("启用时间-起始")
    String startEnableDate;

    @ApiModelProperty("启用时间-终止")
    String endEnableDate;

    @ApiModelProperty("停用时间-起始")
    String startDisableDate;

    @ApiModelProperty("停用时间-终止")
    String endDisableDate;

}
