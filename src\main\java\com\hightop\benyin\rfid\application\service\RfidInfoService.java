package com.hightop.benyin.rfid.application.service;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import com.github.yulichang.toolkit.MPJWrappers;
import com.google.common.collect.Lists;
import com.hightop.benyin.rfid.application.vo.dto.AssetScanDto;
import com.hightop.benyin.rfid.application.vo.dto.AssetTrajectoryDto;
import com.hightop.benyin.rfid.application.vo.po.AssetTakeScanResultVo;
import com.hightop.benyin.rfid.application.vo.query.RfidInfoQuery;
import com.hightop.benyin.rfid.application.vo.query.RfidReaderQuery;
import com.hightop.benyin.rfid.domain.event.AssetTrajectoryEvent;
import com.hightop.benyin.rfid.domain.event.AssetVariationEvent;
import com.hightop.benyin.rfid.domain.service.*;
import com.hightop.benyin.rfid.infrastructure.entity.*;
import com.hightop.benyin.rfid.infrastructure.enums.*;
import com.hightop.benyin.share.domain.service.SequenceDomainService;
import com.hightop.benyin.share.infrastructure.util.DictUtil;
import com.hightop.benyin.share.infrastructure.util.DownloadResponseUtil;
import com.hightop.benyin.share.infrastructure.util.RedisLockUtils;
import com.hightop.benyin.share.socket.util.ReaderControlTool;
import com.hightop.benyin.system.domain.service.DepartmentInfoDomainService;
import com.hightop.benyin.system.infrastructure.entity.*;
import com.hightop.fario.base.util.CollectionUtils;
import com.hightop.fario.base.util.StringUtils;
import com.hightop.fario.common.core.util.ExecutorUtils;
import com.hightop.fario.common.mybatis.DataGrid;
import com.hightop.fario.common.mybatis.PageQuery;
import com.hightop.fario.common.mybatis.util.PageHelper;
import com.hightop.magina.casual.session.ApplicationSessions;
import com.hightop.magina.core.exception.MaginaException;
import com.hightop.magina.standard.code.dictionary.bind.DictItemEntry;
import com.hightop.magina.standard.code.property.Property;
import com.hightop.magina.standard.code.property.PropertyDomainService;
import com.hightop.magina.standard.ums.department.Department;
import com.hightop.magina.standard.ums.user.basic.UserBasic;
import com.hightop.magina.standard.ums.user.bind.UserEntry;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

/**
 * Rfid管理服务
 *
 * <AUTHOR>
 * @date 2024-10-12 16:36:01
 */
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Service
@Slf4j
public class RfidInfoService {
    RfidReaderService rfidReaderService;
    RfidInfoServiceDomain rfidInfoServiceDomain;
    RfidReaderServiceDomain rfidReaderServiceDomain;
    RfidAssetServiceDomain rfidAssetServiceDomain;
    ApplicationEventPublisher applicationEventPublisher;
    DepartmentInfoDomainService departmentInfoDomainService;
    RedisTemplate<String, String> redisTemplate;
    SequenceDomainService sequenceDomainService;
    RfidAssetTakeDetailServiceDomain rfidAssetTakeDetailServiceDomain;
    PropertyDomainService propertyDomainService;
    RfidAssetTakeService rfidAssetTakeService;
    RfidAssetTakeReaderServiceDomain rfidAssetTakeReaderServiceDomain;

    /**
     * Rfid分页查询
     *
     * @param pageQuery {@link PageQuery}
     * @return {@link DataGrid}
     */
    public DataGrid<RfidInfo> page(RfidInfoQuery pageQuery) {
        return PageHelper.startPage(pageQuery, p -> this.getRfidInfoPageList(pageQuery)
        ).peek(p -> {
            if (p.getAssetId() == null) {
                p.setAssetName("未绑定资产");
                p.setIsBind(false);
            }
        });
    }

    public List<RfidInfo> getRfidInfoPageList(RfidInfoQuery pageQuery) {
        return this.rfidInfoServiceDomain.selectJoinList(RfidInfo.class, MPJWrappers.lambdaJoin()
                .selectAll(RfidInfo.class)
                .selectAs(RfidAsset::getId, RfidInfo::getAssetId)
                .selectAs(RfidAsset::getCode, RfidInfo::getAssetCode)
                .selectAs(RfidAsset::getName, RfidInfo::getAssetName)
                .selectAs(RfidAsset::getModel, RfidInfo::getModel)
                .selectAs(RfidReader::getCode, RfidInfo::getReaderCode)
                .selectAs(RfidAsset::getAssetType, RfidInfo::getAssetType)
                .selectAs(AssetType::getName, RfidInfo::getAssetTypeName)
                .leftJoin(RfidAsset.class, RfidAsset::getId, RfidInfo::getAssetId)
                .leftJoin(RfidReader.class, RfidReader::getId, RfidInfo::getReaderId)
                .leftJoin(Location.class, Location::getId, RfidReader::getLocationId)
                .leftJoin(AssetType.class, AssetType::getCode, RfidAsset::getAssetType)
                .like(StringUtils.isNotBlank(pageQuery.getAssetName()), RfidAsset::getName, pageQuery.getAssetName())
                .like(StringUtils.isNotBlank(pageQuery.getAssetCode()), RfidAsset::getCode, pageQuery.getAssetCode())
                .like(StringUtils.isNotBlank(pageQuery.getModel()), RfidAsset::getModel, pageQuery.getModel())
                .like(StringUtils.isNotBlank(pageQuery.getBindCode()), RfidInfo::getBindCode, pageQuery.getBindCode())
                .like(StringUtils.isNotBlank(pageQuery.getRfidCode()), RfidInfo::getRfidCode, pageQuery.getRfidCode())
                .like(StringUtils.isNotBlank(pageQuery.getReaderCode()), RfidReader::getCode, pageQuery.getReaderCode())
                .like(StringUtils.isNotBlank(pageQuery.getDepartmentName()), Department::getName, pageQuery.getDepartmentName())
                .like(StringUtils.isNotBlank(pageQuery.getDeviceId()), RfidInfo::getDeviceId, pageQuery.getDeviceId())
                .like(StringUtils.isNotBlank(pageQuery.getType()), RfidInfo::getType, pageQuery.getType())
                .ne(StringUtils.isNotBlank(pageQuery.getNeType()), RfidInfo::getType, pageQuery.getNeType())
                .in(CollectionUtils.isNotEmpty(pageQuery.getStatus()), RfidInfo::getStatus, pageQuery.getStatus())
                .in(CollectionUtils.isNotEmpty(pageQuery.getTypes()), RfidInfo::getType, pageQuery.getTypes())
                .in(CollectionUtils.isNotEmpty(pageQuery.getLocations()), RfidInfo::getLocationId, pageQuery.getLocations())
                .eq(pageQuery.getReaderId() != null, RfidInfo::getReaderId, pageQuery.getReaderId())
                .eq(pageQuery.getLocationId() != null, Location::getId, pageQuery.getLocationId())
                .isNotNull(pageQuery.getBindTag() != null && pageQuery.getBindTag(), RfidAsset::getRfidCode)
                .isNull(pageQuery.getBindTag() != null && !pageQuery.getBindTag(), RfidAsset::getRfidCode)
                .ge(pageQuery.getStartCounts() != null, RfidInfo::getReportCount, pageQuery.getStartCounts())
                .le(pageQuery.getEndCounts() != null, RfidInfo::getReportCount, pageQuery.getEndCounts())
                .orderByDesc(RfidInfo::getLastReportTime));
    }

    public List<RfidInfo> getRfidInfoList(RfidInfoQuery pageQuery) {
        return rfidInfoServiceDomain.getScanList(pageQuery);
    }


    /**
     * 查询已绑定当前基站的资产列表
     *
     * @param deviceId
     * @return
     */
    public List<RfidAsset> getBindAssetList(String deviceId) {

        ReaderControlTool.searchTag(deviceId);
        //等待两秒接收数据
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }

        String resultKey = DictUtil.BIND_CACHE + DictUtil.LIST + deviceId;
        List<String> rfidCacheList = redisTemplate.opsForList().range(resultKey, 0, -1);

        List<RfidAsset> rfidAssets = rfidAssetServiceDomain.lambdaQuery()
                .in(RfidAsset::getRfidCode, rfidCacheList)
                .list();
        Map<String, RfidAsset> rfidAssetMap = rfidAssets.stream().collect(Collectors.toMap(RfidAsset::getRfidCode, item -> item));
        rfidCacheList.forEach(item -> {
            if (!rfidAssetMap.containsKey(item)) {
                RfidAsset rfidAsset = new RfidAsset();
                rfidAsset.setRfidCode(item);
                rfidAsset.setName("未绑定资产");
                rfidAsset.setCreatedBy(new UserEntry().setId(ApplicationSessions.id()));
                rfidAssets.add(rfidAsset);
            }
        });
        return rfidAssets;
    }

    public Boolean clearBindList(String deviceId) {
        String redisKey = DictUtil.BIND_CACHE + DictUtil.LIST  + deviceId;
        redisTemplate.delete(redisKey);
        return true;
    }


    /**
     * 发送扫描命令
     *
     * @param readerId
     * @return
     */
    public String getBindCode(Long readerId) {
        RfidReader rfidReader = rfidReaderServiceDomain.getById(readerId);
        if (StringUtils.isBlank(rfidReader.getDeviceId())) {
            throw new MaginaException("基站[" + rfidReader.getCode() + "]尚未绑定设备，请检查！");
        }
        String redisKey = DictUtil.BIND_CACHE + DictUtil.SCAN_CODE+ApplicationSessions.code()+":"+rfidReader.getDeviceId() ;
        if (redisTemplate.hasKey(redisKey)) {
            redisTemplate.delete(redisKey);
        }
        String code = sequenceDomainService.nextDateSequence(DictUtil.BIND_SCAN_KEY);
        //获取最新基站客户端标识
        redisTemplate.opsForValue().set(redisKey, code, 60, TimeUnit.MINUTES);
        return code;
    }

    /**
     * 发送绑定扫描命令
     *
     * @param readerId
     * @return
     */
    public Boolean sendScan(Long readerId) {
        RfidReader rfidReader = rfidReaderServiceDomain.getById(readerId);
        String redisKey = DictUtil.BIND_CACHE + DictUtil.SCAN_CODE+ApplicationSessions.code()+":"+rfidReader.getDeviceId() ;
        String code = redisTemplate.opsForValue().get(redisKey);
        if (StringUtils.isBlank(code)) {
            throw new MaginaException("执行失败，请先获取绑定码！");
        }
        if (StringUtils.isBlank(rfidReader.getDeviceId())) {
            throw new MaginaException("基站[" + rfidReader.getCode() + "]尚未绑定设备，请检查！");
        }
        ReaderControlTool.sendScan(redisTemplate,rfidReader.getDeviceId(), rfidReader, DictUtil.BIND_SCAN, code);
        return true;
    }

    /**
     * 是否扫描完成 单基站扫描
     *
     * @param businessType
     * @param bindCode
     * @param readerId
     * @return
     */
    public Boolean isCompleted(String businessType, String bindCode, Long readerId) {
        RfidReader rfidReader = rfidReaderServiceDomain.selectJoinOne(RfidReader.class, MPJWrappers.<RfidReader>lambdaJoin()
                .select(RfidReader::getDeviceId).select(RfidReader::getScanInterval).select(RfidReader::getScanDuration).select(RfidReader::getFocusInterval)
                .select(RfidReader::getFocusDuration)
                .eq(RfidReader::getId, readerId)
        );
        String key = DictUtil.SCAN_CACHE + DictUtil.STATUS + bindCode + ":" + rfidReader.getDeviceId();
        if (redisTemplate.hasKey(key)) {
            String scanStautus = redisTemplate.opsForValue().get(key);
            if (DictUtil.RECEIVED.equals(scanStautus)) {
                return true;
            }
        }
        return Boolean.FALSE;
    }


    /**
     * 是否扫描完成 多个基站同时扫描 盘点扫描
     *
     * @param businessType
     * @param bindCode
     * @return
     */
    public AssetTakeScanResultVo isCompleted(String businessType, String bindCode) {
        List<RfidAssetTakeReader> rfidAssetTakeReaders = rfidAssetTakeReaderServiceDomain.selectJoinList(RfidAssetTakeReader.class, MPJWrappers.<RfidAssetTakeReader>lambdaJoin()
                .select(RfidAssetTakeReader::getDeviceId)
                .select(RfidAssetTakeReader::getReaderId)
                .eq(RfidAssetTakeReader::getTakeCode, bindCode)
                .ne(RfidAssetTakeReader::getStatus, DictUtil.ENABLE)//过滤掉已完成的基站
        );
        if (CollectionUtils.isEmpty(rfidAssetTakeReaders)) {
            throw new MaginaException("盘点单未找到基站信息，请检查！");
        }
        AssetTakeScanResultVo assetTakeScanResultVo = new AssetTakeScanResultVo();
        rfidAssetTakeReaders.forEach(item -> {
            String key = DictUtil.SCAN_CACHE + DictUtil.STATUS + bindCode + ":" + item.getDeviceId();
            if (redisTemplate.hasKey(key)) {
                String scanStautus = redisTemplate.opsForValue().get(key);
                if (DictUtil.RECEIVED.equals(scanStautus)) {
                    item.setIsCompleted(true);
                }
            }
        });
        List<String> devices = rfidAssetTakeReaders.stream().filter(v -> v.getIsCompleted()).map(RfidAssetTakeReader::getDeviceId).distinct().collect(toList());
        assetTakeScanResultVo.setCompleteDevices(devices);
        if (devices.size() == rfidAssetTakeReaders.size()) {
            assetTakeScanResultVo.setAllCompleted(true);
        }
        return assetTakeScanResultVo;
    }

    /**
     * 接收扫描数据
     *
     * @param assetScanDto
     * @return
     */
    public Boolean receiveHandler(AssetScanDto assetScanDto) {
        String bindCode = assetScanDto.getBindCode();
        Long readerId = assetScanDto.getReaderId();
        String waitMsgType = assetScanDto.getBusinessType();

        RfidReader rfidReader = rfidReaderServiceDomain.selectJoinOne(RfidReader.class, MPJWrappers.<RfidReader>lambdaJoin()
                .select(RfidReader::getDeviceId).eq(RfidReader::getId, readerId)
        );
        try {
            // 延迟2秒，等待盘点数据扫描
            Thread.sleep(3000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }

        String deviceId = rfidReader.getDeviceId();

        String resultKey = DictUtil.SCAN_CACHE + DictUtil.RESULT + waitMsgType + ":" + bindCode + ":" + deviceId;
        List<String> rfidCacheList = redisTemplate.opsForList().range(resultKey, 0, -1);
        rfidCacheList = rfidCacheList.stream().distinct().collect(Collectors.toList());

        // 保存上报数据
        boolean result = this.reportHandler(rfidCacheList, deviceId, assetScanDto.getBusinessType(), DictUtil.LOCATION, bindCode);

        //如果有绑定码，则为绑定数据扫描
        if (StringUtils.isNotBlank(waitMsgType) && DictUtil.BIND_SCAN.equals(waitMsgType)) {
            log.info("期初绑定扫描接收成功，{}", deviceId);
        }

        //如果有盘点码，则为盘点数据扫描
        if (StringUtils.isNotBlank(waitMsgType) && DictUtil.TAKE_SCAN.equals(waitMsgType)) {

            log.info("盘点数据扫描接收成功，{}", deviceId);
            if (result) {
                //盘点结果处理
                List<String> finalRfidList = rfidCacheList;
                rfidAssetTakeService.takeReport(finalRfidList, deviceId, bindCode);
            }
        }

        // 为了防止数据过多，清理redis缓存 盘点时先不清理　
        ExecutorUtils.doAfterCommit(() -> {
           if(waitMsgType.equals(DictUtil.BIND_SCAN)){
               this.rfidAssetTakeService.deleteReaderCache(readerId, waitMsgType,bindCode);
           }
        });
        return Boolean.TRUE;
    }

    /**
     * Rfid上报
     *
     * @param rfidList
     * @param deviceId
     * @param type     1.迁入 2.迁出
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean reportHandler(List<String> rfidList, String deviceId, String type, Integer status, String bindCode) {
        RfidReaderQuery rfidReaderQuery = new RfidReaderQuery();
        rfidReaderQuery.setDeviceId(deviceId);
        List<RfidReader> rfidReaders = this.rfidReaderServiceDomain.getReaderList(rfidReaderQuery);
        if (CollectionUtils.isEmpty(rfidReaders)) {
            log.error("上报数据保存, 设备: {},  rfidReader is null", deviceId);
            return false;
        }
        RfidReader rfidReader = rfidReaders.get(0);
        if (type.equals(DictUtil.ADD_SCAN) || type.equals(DictUtil.SUB_SCAN)) {
            if (rfidReader.getIsReport() != null && !rfidReader.getIsReport()) {
                log.error("上报数据忽略, 设备: {}, 未开启异动上报", deviceId);
                return false;
            }
        }

        List<RfidInfo> rfidInfoList = new ArrayList<>();
        Property minuteProperty = propertyDomainService.getByCode(DictUtil.ABNORMAL_MINUTE);
        int minute = minuteProperty == null ? 1 : Integer.parseInt(minuteProperty.getValue());
        rfidList = rfidList.stream().distinct().collect(toList());
        List<AssetTrajectoryDto> assetTrajectoryDtos = Lists.newArrayList();

        for (String rfid : rfidList) {
            RfidAsset rfidAsset =
                    this.rfidAssetServiceDomain.selectJoinOne(RfidAsset.class, MPJWrappers.lambdaJoin()
                            .distinct().select(RfidAsset::getId).select(RfidAsset::getReaderId).select(RfidAsset::getLocationId).select(RfidAsset::getApplyId).select(RfidAsset::getApplyAt)
                            .select(RfidAsset::getApplyName).select(RfidAsset::getManagerName).select(RfidAsset::getManagerDeptId).select(RfidAsset::getRfidCode)
                            .select(RfidAsset::getManagerId).select(RfidAsset::getDepartmentId).select(RfidAsset::getId).select(RfidAsset::getIsReport).select(RfidAsset::getIsScan)
                            .selectAs(Department::getName, RfidAsset::getDepartmentName)
                            .selectAs(DepartmentExtends::getName, RfidAsset::getManagerDeptName)
                            .selectAs(Location::getFullName, RfidAsset::getLocation)
                            .selectAs(UserBasic::getName, RfidAsset::getCreatedByName)
                            .selectAs(RfidAsset::getId, RfidAsset::getAssetId)
                            .selectAs(Location::getFullName, RfidAsset::getLocation)
                            .selectAs(RfidReader::getDeviceId, RfidAsset::getReaderDeviceId)
                            .leftJoin(Location.class, Location::getId, RfidAsset::getLocationId)
                            .leftJoin(RfidReader.class, RfidReader::getId, RfidAsset::getReaderId)
                            .leftJoin(Department.class, Department::getId, RfidAsset::getDepartmentId)
                            .leftJoin(DepartmentExtends.class, DepartmentExtends::getId, RfidAsset::getManagerDeptId)
                            .eq(RfidAsset::getRfidCode, rfid)
                    );
            if (DictUtil.ADD_SCAN.equals(type)) {
                if (rfidAsset != null) {
                    if (rfidAsset.getLocationId() != null && rfidAsset.getLocationId().equals(rfidReader.getLocationId())) {
                        log.info("异动迁入上报数据忽略, 设备: {}, 标签: {}, 位置: {}, 同位置的不同基站", deviceId, rfid, rfidAsset.getLocation());
                        continue;
                    } else {
                        //更新最后位置
                        rfidAssetServiceDomain.lambdaUpdate()
                                .set(RfidAsset::getCurrLocationId, rfidReader.getLocationId())
                                .set(RfidAsset::getCurrReaderId, rfidReader.getId())
                                .eq(RfidAsset::getRfidCode, rfid)
                                .update();
                    }
                }
            }
            //迁出 日常不扫描就不记录
            if (DictUtil.SUB_SCAN.equals(type)) {
                if (rfidAsset != null && !rfidAsset.getIsScan()) {
                    log.info("异动迁出上报数据忽略, 设备: {}, 标签: {}, 位置: {}, 同位置的不同基站", deviceId, rfid, rfidAsset.getLocation());
                    continue;
                }
            }

            RfidInfo rfidInfo = rfidInfoServiceDomain.lambdaQuery()
                    .eq(RfidInfo::getRfidCode, rfid)
                    .eq(RfidInfo::getType, type)
                    .eq(StringUtils.isNotBlank(bindCode), RfidInfo::getStatus, status)
                    .eq(StringUtils.isNotBlank(bindCode), RfidInfo::getBindCode, bindCode)
                    .in(StringUtils.isEmpty(bindCode), RfidInfo::getStatus, Lists.newArrayList(DictUtil.REPORT, DictUtil.VARIATION))
                    .isNull(rfidReader == null, RfidInfo::getReaderId)
                    .eq(rfidReader != null && StringUtils.isNotBlank(rfidReader.getCode()), RfidInfo::getReaderId, rfidReader.getId())
                    .one();
            if (Objects.isNull(rfidInfo)) {
                rfidInfo = new RfidInfo();
                rfidInfo.setRfidCode(rfid);
                //暂定一分钟过期
                rfidInfo.setExpireTime(LocalDateTime.now().plusMinutes(minute));
                rfidInfo.setIpAddr(rfidReader.getIpAddr());
                if (Objects.nonNull(rfidReader)) {
                    rfidInfo.setLocation(rfidReader.getLocation());
                    rfidInfo.setLocationId(rfidReader.getLocationId());
                    rfidInfo.setReaderId(rfidReader.getId());
                    rfidInfo.setDeviceId(rfidReader.getDeviceId());
                    rfidInfo.setDepartmentIds(rfidReader.getDepartmentIds());
                    if (CollectionUtils.isNotEmpty(rfidReader.getDepartmentIds())) {
                        List<DepartmentInfo> departmentInfos = departmentInfoDomainService.selectJoinList(DepartmentInfo.class, MPJWrappers.lambdaJoin()
                                .select(DepartmentInfo::getId)
                                .select(DepartmentInfo::getName)
                                .select(DepartmentInfo::getManagerId)
                                .selectAs(UserInfo::getName, DepartmentInfo::getManagerName)
                                .leftJoin(UserInfo.class, UserInfo::getId, DepartmentInfo::getManagerId)
                                .in(DepartmentInfo::getId, rfidReader.getDepartmentIds())
                        );
                        if (CollectionUtils.isNotEmpty(departmentInfos)) {
                            String departmentName = departmentInfos.stream().map(v -> v.getName().getValue()).collect(Collectors.joining(","));
                            rfidInfo.setDepartmentName(departmentName);
                            rfidInfo.setManagerIds(departmentInfos.stream().map(DepartmentInfo::getManagerId).collect(toList()));
                            String managerNames = departmentInfos.stream().map(DepartmentInfo::getManagerName).collect(Collectors.joining(","));
                            rfidInfo.setManagerNames(managerNames);
                        }
                    }
                    rfidInfo.setLocationId(rfidReader.getLocationId());
                }

                rfidInfo.setType(new DictItemEntry().setValue(type));
                rfidInfo.setReportCount(1);
                rfidInfo.setStatus(status);
            } else {
                rfidInfo.setAssetId(rfidAsset != null ? rfidAsset.getId() : null);
                rfidInfo.setReportCount(rfidInfo.getReportCount() + 1);
            }
            if (rfidAsset != null) {
                rfidInfo.setAssetLocation(rfidAsset.getLocation());
                rfidInfo.setAssetLocationId(rfidAsset.getLocationId());
                rfidInfo.setAssetReader(rfidAsset.getReaderDeviceId());
                rfidInfo.setAssetReaderId(rfidAsset.getReaderId());
                rfidInfo.setAssetId(rfidAsset.getId());
                rfidInfo.setIsReport(rfidAsset.getIsReport());
                rfidInfo.setIsScan(rfidAsset.getIsScan());
                rfidInfo.setKeeperId(rfidAsset.getApplyId());
                rfidInfo.setKeeperName(rfidAsset.getApplyName());
                rfidInfo.setIpAddr(rfidReader.getIpAddr());
                rfidInfo.setKeeperDeptId(rfidAsset.getDepartmentId());
                rfidInfo.setKeeperDeptName(rfidAsset.getDepartmentName());
                rfidInfo.setManagerId(rfidAsset.getManagerId());
                rfidInfo.setManagerName(rfidAsset.getManagerName());
                rfidInfo.setManagerDeptId(rfidAsset.getManagerDeptId());
                rfidInfo.setManagerDeptName(rfidAsset.getManagerDeptName());

                //防止误报 迁出在异动中上报轨迹
                if(!rfidInfo.getType().equals(DictUtil.SUB_SCAN)){
                    //此处只记录迁入与扫描轨迹
                    TrajectoryType trajectoryType = TrajectoryType.IN;
                    TrajectorySource trajectorySource = null;

                    if (type.equals(DictUtil.ADD_SCAN) || type.equals(DictUtil.SUB_SCAN)) {
                        trajectorySource=TrajectorySource.VARIATION;
                    }else{
                        if(type.equals(DictUtil.LOOK_SCAN)){
                            trajectorySource=TrajectorySource.SEARCH;

                        }
                        if(type.equals(DictUtil.TAKE_SCAN)){
                            trajectorySource=TrajectorySource.TAKE;

                        }
                        if(type.equals(DictUtil.BIND_SCAN)){
                            trajectorySource=TrajectorySource.LOCATION;
                        }
                    }
                    AssetTrajectoryDto assetTrajectoryDto= AssetTrajectoryDto.builder()
                            .assetId(rfidAsset.getId())
                            .rfidCode(rfidInfo.getRfidCode())
                            .locationId(rfidInfo.getLocationId())
                            .location(rfidInfo.getLocation())
                            .readerId(rfidInfo.getReaderId())
                            .deviceId(rfidInfo.getDeviceId())
                            .trajectorySource(trajectorySource)
                            .trajectoryType(trajectoryType)
                            .build();
                    assetTrajectoryDtos.add(assetTrajectoryDto);
                }
            }
            //这种情况数是关联正确的则直接下发数据到基站
            if (rfidInfo.getType().getValue().equals(DictUtil.ADD_SCAN) && rfidAsset != null &&
                    Objects.nonNull(rfidAsset.getReaderId()) &&
                    rfidAsset.getReaderId().equals(rfidReader.getId())) {
                boolean sendAsset = rfidReaderService.sendAsset(rfidReader.getId());
                if (sendAsset) {
                    rfidInfo.setStatus(DictUtil.DEAL);
                }
            }
            rfidInfo.setBindCode(bindCode);
            rfidInfo.setLastReportTime(LocalDateTime.now());
            rfidInfoList.add(rfidInfo);
        }

        if(CollectionUtils.isNotEmpty(assetTrajectoryDtos)){
            AssetTrajectoryEvent assetTrajectoryEvent= new AssetTrajectoryEvent().setAssetTrajectoryDtos(assetTrajectoryDtos);
            applicationEventPublisher.publishEvent(assetTrajectoryEvent);
        }

        if (CollectionUtils.isNotEmpty(rfidInfoList)) {
            this.rfidInfoServiceDomain.saveOrUpdateBatch(rfidInfoList);
            //异动上报类型
            if (type.equals(DictUtil.ADD_SCAN) || type.equals(DictUtil.SUB_SCAN)) {
                //上报次数大于等于5的标签 上报异动
                Property property = propertyDomainService.getByCode(DictUtil.ABNORMAL_RULE);
                int abnormalCount = property == null ? 5 : Integer.parseInt(property.getValue());
                List<RfidInfo> rfidInfos = rfidInfoList.stream().filter(rfidInfo -> rfidInfo.getAssetId() != null && rfidInfo.getReportCount() >= abnormalCount && !rfidInfo.getStatus().equals(DictUtil.DEAL)).collect(toList());
                List<RfidInfo> variationRfidInfos = rfidInfos.stream().filter(rfidInfo -> {
                    boolean isVariation = isVariation(rfidInfo.getAssetId(), rfidInfo.getReaderId(), type);
                    //判断是否异动 并且要上报
                    return isVariation && rfidInfo.getIsReport() != null && rfidInfo.getIsReport();
                }).collect(toList());
                if (CollectionUtils.isNotEmpty(variationRfidInfos)) {
                    ExecutorUtils.doAfterCommit(() -> {
                        applicationEventPublisher.publishEvent(new AssetVariationEvent(variationRfidInfos, deviceId));
                    });
                }
            }
        }
        return true;
    }

    /**
     * 判断是否异动
     *
     * @param assetId
     * @param readerId
     * @param type
     * @return
     */
    private boolean isVariation(Long assetId, Long readerId, String type) {
        if (assetId == null) {
            return false;
        }
        RfidAsset rfidAsset = rfidAssetServiceDomain.getById(assetId);

        if (type.equals(DictUtil.ADD_SCAN)) {
            if (Objects.nonNull(rfidAsset.getReaderId())) {
                return true;
            } else {
                return rfidAsset.getReaderId() == null || !rfidAsset.getReaderId().equals(readerId);
            }
        } else {
            //迁出
            return Objects.nonNull(rfidAsset.getReaderId()) && rfidAsset.getReaderId().equals(readerId);
        }
    }

    /**
     * 获取目标标签列表- 全局搜索用
     *
     * @param rfidList
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public List<String> getSearchResult(List<String> rfidList, String deviceId, String takeCode) {
        String redisKey = DictUtil.LOOK_CACHE + DictUtil.LIST + takeCode;
        LinkedHashSet<String> targetRfidList = (LinkedHashSet<String>) redisTemplate.opsForSet().members(redisKey);
        if (CollectionUtils.isEmpty(targetRfidList)) {
            return null;
        }
        List<RfidAssetTakeDetail> rfidAssetTakeDetailList = rfidAssetTakeDetailServiceDomain.selectJoinList(RfidAssetTakeDetail.class, MPJWrappers.lambdaJoin()
                .selectAll(RfidAssetTakeDetail.class)
                .selectAs(RfidAsset::getPrice, RfidAssetTakeDetail::getPrice)
                .leftJoin(RfidAsset.class, RfidAsset::getId, RfidAssetTakeDetail::getAssetId)
                .eq(RfidAssetTakeDetail::getTakeCode, takeCode)
                .in(RfidAssetTakeDetail::getRfidCode, targetRfidList)
        );

        List<String> resultRfidList = new ArrayList<>();
        rfidList.forEach(rfid -> {
            //找到目标标签
            if (targetRfidList.contains(rfid)) {
                redisTemplate.opsForSet().remove(redisKey, rfid);
                resultRfidList.add(rfid);
            }
        });
        if (CollectionUtils.isEmpty(resultRfidList)) {
            return resultRfidList;
        }

        RfidReader rfidReader = rfidReaderServiceDomain.selectJoinOne(RfidReader.class, MPJWrappers.<RfidReader>lambdaJoin()
                .leftJoin(Location.class, Location::getId, RfidReader::getLocationId)
                .select(RfidReader::getDeviceId)
                .select(RfidReader::getId)
                .select(RfidReader::getLocationId)
                .selectAs(Location::getFullName, RfidReader::getLocation)
                .eq(RfidReader::getDeviceId, deviceId)
        );
        rfidAssetTakeDetailList.forEach(v -> {
            //找到目标标签
            if (rfidList.contains(v.getRfidCode())) {

                List<Long> locationIds = new ArrayList<>();
                List<Long> readerIds = new ArrayList<>();
                String location = v.getScanLocation();
                String readerDevice = v.getScanReaderDevice();
                if (CollectionUtils.isNotEmpty(v.getScanLocationIds())) {
                    locationIds.addAll(v.getScanLocationIds());
                }
                if (CollectionUtils.isNotEmpty(v.getScanReaderIds())) {
                    readerIds.addAll(v.getScanReaderIds());
                }
                if (!locationIds.contains(rfidReader.getLocationId())) {
                    locationIds.add(rfidReader.getLocationId());
                    if (StringUtils.isNotBlank(location)) {
                        location = location + "," + rfidReader.getLocation();
                    } else {
                        location = rfidReader.getLocation();
                    }
                }
                if (!readerIds.contains(rfidReader.getId())) {
                    readerIds.add(rfidReader.getId());
                    if (StringUtils.isNotBlank(readerDevice)) {
                        readerDevice = readerDevice + "," + rfidReader.getDeviceId();
                    } else {
                        readerDevice = rfidReader.getDeviceId();
                    }
                }

                v.setScanLocation(location);
                v.setScanReaderDevice(readerDevice);
                v.setScanLocationIds(locationIds);
                v.setScanReaderIds(readerIds);

                v.setScanType(DictUtil.ENABLE);
                v.setScanStatus(TakeScanStatus.FINISH);
                if (CollectionUtils.isNotEmpty(v.getScanLocationIds()) && v.getScanLocationIds().contains(v.getLocationId())) {
                    v.setTakeStatus(TakeStatus.YES);
                    v.setStatus(DiscrepancyStatus.NORMAL);
                } else {
                    v.setStatus(DiscrepancyStatus.CHANGE);
                }
            }
        });
        rfidAssetTakeDetailServiceDomain.updateBatchById(rfidAssetTakeDetailList);
        ExecutorUtils.doAfterCommit(() -> {
            rfidAssetTakeService.repeatComplete(takeCode);
        });
        return resultRfidList;
    }

    /**
     * 导出资产流程
     *
     * @param response
     * @return
     */
    public Boolean downloadData(HttpServletResponse response, RfidInfoQuery pageQuery) throws IOException {
        try {
            //查询数据
            List<RfidInfo> excelList = this.getRfidInfoPageList(pageQuery);
            //页面下载设置
            DownloadResponseUtil.addDownLoadHeader(response, "标签上报记录.xlsx");
            //将并添加信息，实体类结构，类的数据做成excel表格对象workbook
            Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams(), RfidInfo.class, excelList);
            workbook.write(response.getOutputStream());
        } catch (IOException e) {
            e.printStackTrace();
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    public Boolean clear() {
        rfidInfoServiceDomain.clear();
        return Boolean.TRUE;
    }
}
