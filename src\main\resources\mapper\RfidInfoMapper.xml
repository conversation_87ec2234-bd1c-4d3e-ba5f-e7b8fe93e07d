<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hightop.benyin.rfid.infrastructure.mapper.RfidInfoMapper">

    <select id="getScanList" resultType="com.hightop.benyin.rfid.infrastructure.entity.RfidInfo">
        select *
        from (
        SELECT distinct t.rfid_code,
        t.location,
        t.reader_id,
        t.location_id,
        t1.code                                                 assetCode,
        t1.name                                                 assetName,
        case when t1.id is not null then true else false end AS isBind
        FROM b_rfid_info t
        LEFT JOIN b_rfid_asset t1 ON t1.rfid_code = t.rfid_code
        <where>
            <if test="qo.rfidCode!= null and qo.rfidCode!= ''">
                and t.rfid_code like concat ('%',#{qo.rfidCode},'%')
            </if>
            <if test="qo.bindCode!= null and qo.bindCode!= ''">
                and t.bind_code like concat ('%',#{qo.bindCode},'%')
            </if>
            <if test="qo.status!= null">
                and t.status = #{qo.status}
            </if>

            <if test="qo.type!= null">
                and t.type = #{qo.type}
            </if>

            <if test="null!=qo.bindTag">
                <choose>
                    <when test="qo.bindTag==true">
                        and t1.id is not null
                    </when>
                    <otherwise>
                        and t1.id is null
                    </otherwise>
                </choose>
            </if>
        </where>) t  order by  t.isBind,t.rfid_code asc
    </select>

</mapper>