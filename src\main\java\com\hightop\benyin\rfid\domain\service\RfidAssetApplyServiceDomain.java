package com.hightop.benyin.rfid.domain.service;

import com.github.yulichang.base.MPJBaseServiceImpl;
import com.hightop.benyin.rfid.infrastructure.entity.RfidAssetApply;
import com.hightop.benyin.rfid.infrastructure.mapper.RfidAssetApplyMapper;
import org.springframework.stereotype.Service;

/**
 * rfid资产借用管理领域服务
 *
 * <AUTHOR>
 * @date 2024-10-12 16:36:01
 */
@Service
public class RfidAssetApplyServiceDomain extends MPJBaseServiceImpl<RfidAssetApplyMapper, RfidAssetApply> {
}
