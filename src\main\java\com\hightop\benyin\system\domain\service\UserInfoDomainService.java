package com.hightop.benyin.system.domain.service;

import com.github.yulichang.base.MPJBaseServiceImpl;
import com.github.yulichang.toolkit.MPJWrappers;
import com.hightop.benyin.share.infrastructure.util.DictUtil;
import com.hightop.benyin.system.infrastructure.entity.DepartmentInfo;
import com.hightop.benyin.system.infrastructure.entity.UserInfo;
import com.hightop.benyin.system.infrastructure.entity.UserPrivacyInfo;
import com.hightop.benyin.system.infrastructure.mapper.UserInfoMapper;
import com.hightop.magina.casual.session.ApplicationSessions;
import com.hightop.magina.core.exception.MaginaException;
import com.hightop.magina.standard.ums.role.Role;
import com.hightop.magina.standard.ums.role.api.RoleVo;
import com.hightop.magina.standard.ums.user.role.UserRole;
import com.hightop.magina.standard.ums.user.role.UserRoleDomainService;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Service
public class UserInfoDomainService extends MPJBaseServiceImpl<UserInfoMapper, UserInfo> {

    DepartmentInfoDomainService departmentInfoDomainService;
    UserRoleDomainService userRoleDomainService;

    public UserInfo getCurrUser(){
        return  this.getById(ApplicationSessions.id());
    }

    public UserInfo getUserFullInfo(Long id){
        return  this.selectJoinOne(UserInfo.class, MPJWrappers.<UserInfo>lambdaJoin()
                .selectAll(UserInfo.class)
                        .selectAs(UserPrivacyInfo::getEmail,UserInfo::getEmail )
                        .selectAs(UserPrivacyInfo::getMobileNumber,UserInfo::getEmail )
               .leftJoin(UserPrivacyInfo.class,UserPrivacyInfo::getId,UserInfo::getId)
                .eq(UserInfo::getId,id)
        );
    }

    /**
     * 获取当前用户所属公司部门信息
     *
     * @return
     */
    public List<Long> getCurrCompanyDeptList() {
        UserInfo userInfo = this.getCurrUser();
        if(userInfo.getIsBuildIn()){
            return null;
        }
        if (Objects.isNull(userInfo)) {
            throw new MaginaException("用户不存在！");
        }
        Long departmentId = userInfo.getDepartmentId();
        if (Objects.isNull(departmentId)) {
            throw new MaginaException("用户还未设置部门信息！");
        }
        return departmentInfoDomainService.getCompanyDeptList(departmentId);
    }


    /**
     * 获取 用户所属公司
     *
     * @param userId
     * @return
     */
    public DepartmentInfo getCompany(Long userId) {
        UserInfo userInfo = this.getById(userId);
        if (Objects.isNull(userInfo)) {
            throw new MaginaException("用户不存在！");
        }
        Long departmentId = userInfo.getDepartmentId();
        if (Objects.isNull(departmentId)) {
            throw new MaginaException("用户还未设置部门信息！");
        }
        return departmentInfoDomainService.getCompanyInfo(departmentId);
    }

    /**
     * 获取当前用户所属公司
     *
     * @return
     */
    public DepartmentInfo getCurrCompany() {
        UserInfo userInfo = this.getCurrUser();
        if (Objects.isNull(userInfo)) {
            throw new MaginaException("用户不存在！");
        }
        Long departmentId = userInfo.getDepartmentId();
        if (Objects.isNull(departmentId)) {
            throw new MaginaException("用户还未设置部门信息！");
        }
        return departmentInfoDomainService.getCompanyInfo(departmentId);
    }

    /**
     * 获取当前用户最高角色
     * @return
     */
    public String getCurrRoleCode() {

        List<RoleVo> roles = this.userRoleDomainService.getBaseMapper()
                .selectJoinList(RoleVo.class, MPJWrappers.<UserRole>lambdaJoin()
                        .selectAll(Role.class)
                        .innerJoin(Role.class,
                                on -> on.eq(Role::getId, UserRole::getRoleId).eq(Role::getIsEnable, true)
                        )
                        .eq(UserRole::getUserId, ApplicationSessions.id())
                );
        Boolean hasAdmin = roles.stream().filter(v -> DictUtil.ADMIN_ROLE.equals(v.getCode())).findFirst().isPresent();
        if (hasAdmin) {
            return DictUtil.ADMIN_ROLE;
        }
        Boolean isLeader = roles.stream().filter(v -> DictUtil.LEADER_ROLE.equals(v.getCode())).findFirst().isPresent();
        if (isLeader) {
            return DictUtil.LEADER_ROLE;
        }
        Boolean manager = roles.stream().filter(v -> DictUtil.MANAGER_ROLE.equals(v.getCode())).findFirst().isPresent();
        if (manager) {
            return DictUtil.MANAGER_ROLE;
        }
        return DictUtil.STAFF_ROLE;
    }

    public List<Long> getCurrDeptIds() {
        List<DepartmentInfo> departmentInfos = departmentInfoDomainService.lambdaQuery()
                .eq(DepartmentInfo::getManagerId, ApplicationSessions.id())
                .list();
        return departmentInfos.stream().map(DepartmentInfo::getId).collect(Collectors.toList());
    }

    public List<String> getCurrDeptCodes() {
        List<DepartmentInfo> departmentInfos = departmentInfoDomainService.lambdaQuery()
                .eq(DepartmentInfo::getManagerId, ApplicationSessions.id())
                .list();
        return departmentInfos.stream().map(v->v.getCode().getValue()).collect(Collectors.toList());
    }


    public void clearUser() {
        baseMapper.clearUser();
    }
}
