package com.hightop.benyin.share.infrastructure.restful;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 多个元素据
 * @Author: X.S
 * @date 2024/02/26 18:51
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface ClientMetas {
    ClientMeta[] value();
}
