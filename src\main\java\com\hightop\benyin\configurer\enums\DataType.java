package com.hightop.benyin.configurer.enums;


import com.hightop.magina.core.custom.entry.EnumEntry;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

/**
 * 消息类型状态
 *
 * <AUTHOR>
 * @date 2024/5/15 13:42
 */
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Getter
public enum DataType implements EnumEntry<String> {
    /**
     * 文本
     */
    TXT("文本"),
    /**
     * 位置
     */
    LOCATION("位置"),
    /**
     * 开关
     */
    ON_OFF("开关"),
    /**
     * 部门
     */
    DEPT("部门"),
    /**
     * 部门列表
     */
    DEPTS("部门列表"),
    /**
     *          基站
     */
    READER("基站"),
    /**
     * 性别
     */
    SEX("性别"),
    /**
     * 用户
     */
    USER("用户"),
    /**
     * 用户
     */
    ASSET_TYPE("资产类型"),
    /**
     * 是否
     */
    IF("是否"),
    /**
     * 字典
     */
    DICT("字典"),
    /**
     * 启停状态
     */
    ENABLE("启停状态");

    /**
     * 状态名称
     */
    String name;

    @Override
    public String getCode() {
        // 以枚举名称作为标识
        return super.name();
    }

    @Override
    public String getName() {
        return this.name;
    }
}