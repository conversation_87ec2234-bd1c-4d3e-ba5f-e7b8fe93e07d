package com.hightop.benyin.system.infrastructure.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelIgnore;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.hightop.benyin.configurer.annotation.RecordLog;
import com.hightop.benyin.configurer.annotation.RecordLogField;
import com.hightop.benyin.configurer.enums.DataType;
import com.hightop.benyin.system.infrastructure.enums.BusinessType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import net.sf.jsqlparser.expression.Alias;
import net.sf.jsqlparser.schema.Table;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;

@Data
@TableName(value = "st_location", autoResultMap = true)
@RecordLog(businessType = BusinessType.LOCATION)
@Accessors(chain = true)
public class Location implements Serializable {

    @TableId(value = Location.ID_FIELD, type = IdType.NONE)
    @ApiModelProperty("id")
    private Long id;

    @TableField("parent_id")
    @RecordLogField(value = "所属位置",dataType = DataType.LOCATION)
    @ApiModelProperty("父id")
    private  Long parentId;


    @TableField("code")
    @RecordLogField(value = "位置编码")
    @ApiModelProperty("位置编码")
    @Excel(name = "位置编码", width = 30, orderNum = "0")
    private String code;

    @TableField("name")
    @RecordLogField(value = "位置名称")
    @ApiModelProperty("位置名称")
    @Excel(name = "位置名称", width = 30, orderNum = "1")
    private String name;

    @TableField("another_name")
    @RecordLogField(value = "位置别称")
    @ApiModelProperty("位置别称")
    @Excel(name = "位置别称", width = 30, orderNum = "1")
    private String anotherName;


    @TableField(exist = false)
    @Excel(name = "上级单位编码", width = 30, orderNum = "3")
    @ApiModelProperty("上级单位编码")
    private  String parentCode;

    @TableField(exist = false)
    @ApiModelProperty("上级单位名称")
    @Excel(name = "上级单位名称", width = 30, orderNum = "2")
    private String parentName;


    @TableField(exist = false)
    @ApiModelProperty("部门列表")
    @Excel(name = "部门列表", width = 30, orderNum = "4")
    private String departmentNames;


    @TableField(exist = false)
    @ApiModelProperty("部门编码")
    @Excel(name = "部门编码", width = 30, orderNum = "5")
    private String departmentCodes;

    @TableField("sort")
    @ApiModelProperty("排序号")
    @ExcelIgnore
    private Integer sort;

    @TableField("is_enable")
    @ApiModelProperty("启停状态")
    @RecordLogField(value = "启停状态",dataType = DataType.ENABLE)
    @Excel(name = "启停状态", width = 30, orderNum = "7", replace = {"启用_true", "停用_false"})
    private Boolean isEnable;

    @TableField("type")
    @ApiModelProperty("类型0单位1楼栋2楼层3房间")
    private Integer type;

    @TableField("is_leaf")
    @ApiModelProperty("是否叶子节点")
    private Boolean isLeaf;

    /**
     * 是否可用 类似逻辑删除的状态标识字段
     */
    @TableField("is_available")
    @JsonIgnore
    private Boolean isAvailable;

    @TableField(value = "expire_at", updateStrategy = FieldStrategy.IGNORED)
    @ApiModelProperty("失效时间")
    @Excel(name = "停用时间", width = 30, orderNum = "9", format = "yyyy/MM/dd HH:mm:ss")
    private LocalDateTime expireAt;

    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @ApiModelProperty("创建时间")
    @Excel(name = "启用时间", width = 30, orderNum = "8", format = "yyyy/MM/dd HH:mm:ss")
    private LocalDateTime createdAt;

    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty("更新时间")
    private LocalDateTime updatedAt;

    @TableField(value = "updated_by")
    @ApiModelProperty("更新人")
    private Long updatedBy;

    @TableField(value = "department_ids",typeHandler = JacksonTypeHandler.class)
    @ApiModelProperty("关联的部门id列表")
    @RecordLogField(value = "关联部门",dataType = DataType.DEPTS)
    private List<Long> departmentIds;

    /**
     * id全路径
     */
    @TableField(Location.FULL_ID_PATH_FIELD)
    @JsonIgnore
    private String fullIdPath;
    /**
     * id全路径
     */
    @TableField("full_name")
    private String fullName;
    /**
     * 位置全路径
     */
    @TableField(exist = false)
    private String location;
    /**
     * 顶级节点父id
     */
    public static final Long TOP = 0L;
    /**
     * 默认比较器
     */
    public static final Comparator<Location> COMPARATOR =
            // 先按照排序号排序再按照编码排序
            Comparator.comparingInt(Location::getSort).thenComparing(it -> it.getCode());
    /**
     * id字段名称
     */
    public static final String ID_FIELD = "id";
    /**
     * id路径字段名称
     */
    public static final String FULL_ID_PATH_FIELD = "full_id_path";
    /**
     * 表名称
     */
    static final String TABLE_NAME = "st_location";

    /**
     * jsql位置表名
     *
     * @param alias  别名
     * @param withAs 是否要as关键字
     * @return {@link Table}
     */
    public static Table table(String alias, boolean withAs) {
        return new Table().withName("st_location").withAlias(new Alias(alias, withAs));
    }

    /**
     * 是否是不可用的位置
     *
     * @param location {@link Location}
     * @return true/false
     */
    public static boolean isDisable(Location location) {
        return location.isNotExists(location) || !location.getIsEnable();
    }

    /**
     * 是否是不存在的位置
     *
     * @param location {@link Location}
     * @return true/false
     */
    public static boolean isNotExists(Location location) {
        return Objects.isNull(location) || !location.getIsAvailable();
    }


}
