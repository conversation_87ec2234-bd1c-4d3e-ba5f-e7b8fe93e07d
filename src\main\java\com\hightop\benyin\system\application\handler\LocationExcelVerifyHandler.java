package com.hightop.benyin.system.application.handler;

import cn.afterturn.easypoi.excel.entity.result.ExcelVerifyHandlerResult;
import cn.afterturn.easypoi.handler.inter.IExcelVerifyHandler;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.google.common.collect.Lists;
import com.hightop.benyin.system.api.vo.excel.LocationExcel;
import com.hightop.benyin.system.domain.service.DepartmentInfoDomainService;
import com.hightop.benyin.system.domain.service.LocationDomainService;
import com.hightop.benyin.system.infrastructure.entity.DepartmentInfo;
import com.hightop.benyin.system.infrastructure.entity.Location;
import com.hightop.fario.base.util.StringUtils;
import com.hightop.magina.core.component.ApplicationContexts;

import java.util.List;
import java.util.Objects;
import java.util.StringJoiner;

public class LocationExcelVerifyHandler implements IExcelVerifyHandler<LocationExcel> {
    @Override
    public ExcelVerifyHandlerResult verifyHandler(LocationExcel data) {
        DepartmentInfoDomainService departmentDomainService = ApplicationContexts.getBean(DepartmentInfoDomainService.class);
        LocationDomainService locationDomainService = ApplicationContexts.getBean(LocationDomainService.class);

        StringJoiner joiner = new StringJoiner(",");

        if (StringUtils.isNotBlank(data.getDepartmentCode())) {
            List<Long> departmentIds = Lists.newArrayList();
            if (data.getDepartmentCode().indexOf(",") != -1) {
                String[] codes = data.getDepartmentCode().split(",");
                for (String code : codes) {
                    DepartmentInfo department = departmentDomainService.lambdaQuery()
                            .eq(DepartmentInfo::getCode, code).one();
                    if (Objects.isNull(department)) {
                        joiner.add("部门编码[" + code + "]有误");
                    } else {
                        departmentIds.add(department.getId());
                    }
                }
            } else {
                DepartmentInfo department = departmentDomainService.lambdaQuery()
                        .eq(DepartmentInfo::getCode, data.getDepartmentCode()).one();
                if (Objects.isNull(department)) {
                    joiner.add("部门编码有误");
                } else {
                    departmentIds.add(department.getId());
                }
            }
            data.setDepartmentIds(departmentIds);
        }
        if (StringUtils.isNotBlank(data.getParentCode())) {
            Location location = locationDomainService.lambdaQuery()
                    .eq(Location::getCode, data.getParentCode()).one();
            if (Objects.isNull(location)) {
//                joiner.add("上级位置编码有误");
            } else {
                data.setParentId(location.getId());
            }
        }
        // 校验编码是否存在 允许修改
        Location location = locationDomainService.lambdaQuery()
                .eq(Location::getCode, data.getCode()).one();
        if (Objects.nonNull(location)) {
            data.setId(location.getId());
        } else {
            data.setId(IdWorker.getId());
        }
        String errorMsg = joiner.toString();
        if (StringUtils.isNotBlank(errorMsg)) {
            return new ExcelVerifyHandlerResult(false, errorMsg);
        }
        return new ExcelVerifyHandlerResult(true);
    }
}
