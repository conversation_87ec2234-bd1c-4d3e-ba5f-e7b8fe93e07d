package com.hightop.benyin.rfid.application.vo.query;

import com.hightop.fario.common.mybatis.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldDefaults;

/**
 * RFID基站故障查询DTO
 * <AUTHOR>
 * @date 2024-10-12 16:36:01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@FieldDefaults(level = AccessLevel.PRIVATE)
@ApiModel("RFID基站故障查询DTO")
public class RfidReaderFaultQuery extends PageQuery {

    @ApiModelProperty("编码")
    String readerCode;
    @ApiModelProperty("设备id")
    String deviceId;

    @ApiModelProperty("位置")
    String location;

    @ApiModelProperty("状态")
    String status;


    @ApiModelProperty("处理类型")
    String handleType;

    @ApiModelProperty("处理人")
    String handleName;


    @ApiModelProperty("检测时间-起")
    String startDate;
    @ApiModelProperty("检测时间-止")
    String endDate;

    @ApiModelProperty("处理时间-起")
    String startHandleDate;
    @ApiModelProperty("处理时间-止")
    String endHandleDate;

}
