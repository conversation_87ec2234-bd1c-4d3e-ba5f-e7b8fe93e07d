package com.hightop.benyin.share.infrastructure.util;

import com.hightop.fario.base.util.codec.UrlCodec;

import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

/**
 * @ClassName com.xr.DownloadResponseUtil
 * @Description
 * <AUTHOR>
 * @Date 2024/9/07
 * @Version 1.0.0
 */
public class DownloadResponseUtil {

    /**
     * 页面下载设置
     * @param response
     * @param fileName
     * @throws UnsupportedEncodingException
     */
    public static void addDownLoadHeader(HttpServletResponse response, String fileName) {
        response.addHeader("Content-Disposition", "attachment;fileName=" + UrlCodec.encode(fileName, StandardCharsets.UTF_8));
        response.addHeader("Access-Control-Expose-Headers", "Content-Disposition");
        response.setHeader("Pragma", "No-cache");
        response.setCharacterEncoding("utf-8");
        response.setContentType("application/vnd.ms-excel;charset=utf-8");

    }


}
