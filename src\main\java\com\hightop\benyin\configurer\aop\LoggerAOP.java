package com.hightop.benyin.configurer.aop;


import com.hightop.benyin.configurer.aop.base.BaseLogger;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.*;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;


@Aspect
@Configuration
public class LoggerAOP extends BaseLogger {

    @Pointcut("execution(public * com.hightop.benyin.*.api.*.*.*(..))")
    public void webController1() {
    }

    @Pointcut("webController1()")
    public void webController() {
    }

    @Before("webController()")
    public void webBefore(JoinPoint joinPoint) throws Throwable {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            HttpServletRequest request = attributes.getRequest();
            controllerBefore(joinPoint, request);
        }catch (Exception e){
        }
    }

    @Around("webController()")
    public Object doAround(ProceedingJoinPoint pjp) throws Throwable {
        return pjp.proceed();
    }

    @AfterReturning(returning = "ret", pointcut = "webController()")
    public void webAfterReturning(Object ret) throws Throwable {
        controllerAfter(ret);
    }

}
