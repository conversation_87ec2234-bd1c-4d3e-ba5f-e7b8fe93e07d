package com.hightop.benyin.rfid.application.vo.po;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelIgnore;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.hightop.benyin.rfid.infrastructure.enums.ProcessType;
import com.hightop.benyin.share.infrastructure.util.DictUtil;
import com.hightop.fario.common.jackson.annotation.JsonAmount;
import com.hightop.magina.standard.code.dictionary.bind.DictItemBind;
import com.hightop.magina.standard.code.dictionary.bind.DictItemEntry;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotNull;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@ApiModel("资产盘点明细DTO")
public class RfidAssetTakeDetailInfoVo {

    @ApiModelProperty("id")
    @NotNull(message = "id不能为空")
    Long id;

    @ApiModelProperty("盘点单号")
    String takeCode;

    @ApiModelProperty(value = "RFID编号", required = true)
    @Excel(name = "RFID编号", width = 30, orderNum = "2")
    String rfidCode;

    @ApiModelProperty(value = "资产编码", required = true)
    @Excel(name = "资产编号", width = 30, orderNum = "3")
    String code;

    @ApiModelProperty(value = "资产名称", required = true)
    @Excel(name = "资产名称", width = 30, orderNum = "4")
    String name;

    @ApiModelProperty("资产型号")
    @Excel(name = "资产型号", width = 30, orderNum = "5")
    String model;

    @TableField(exist = false)
    @ApiModelProperty("资产小类")
    @ExcelIgnore
    String assetType;

    @TableField(exist = false)
    @ApiModelProperty("资产小类")
    @ExcelIgnore
    String assetTypeName;

    @ApiModelProperty("使用状态")
    @DictItemBind(DictUtil.DICT_USE_STATE)
    DictItemEntry useState;

    @ApiModelProperty("有无标签")
    Boolean hasTag;

    @JsonAmount
    @ApiModelProperty("资产价格")
    @Excel(name = "资产价格", width = 30, orderNum = "6")
    Long price;

    @ApiModelProperty("资产id")
    Long assetId;

    @ApiModelProperty("原资产情况")
    AssetBaseVo oriAssetVo;

    @ApiModelProperty("现资产情况")
    AssetBaseVo nowAssetVo;

    @ApiModelProperty("异常分析")
    String abnormalAnalyze;

    @ApiModelProperty("处理类型")
    @NotNull(message = "异常处理类型不能为空")
    ProcessType processType;


    @TableField("manager_id")
    @ApiModelProperty(value = "责任人", required = true)
    @ExcelIgnore
    Long managerId;

    @TableField("manager_name")
    @ApiModelProperty(value = "责任人", required = true)
    @Excel(name = "责任部门", width = 30, orderNum = "12")
    String managerName;

    @TableField("manager_dept_id")
    @ApiModelProperty(value = "责任部门", required = true)
    @ExcelIgnore
    Long managerDeptId;

    @Excel(name = "责任部门", width = 30, orderNum = "11")
    @ApiModelProperty("责任部门")
    String managerDeptName;

    @ApiModelProperty(value = "归属人id", required = true)
    @ExcelIgnore
    Long applyId;

    @ApiModelProperty("领用人姓名")
    @Excel(name = "领用人", width = 30, orderNum = "16")
    String applyName;

    @ApiModelProperty(value = "领用部门", required = true)
    @ExcelIgnore
    Long departmentId;

    @Excel(name = "领用部门", width = 30, orderNum = "15")
    @ApiModelProperty("领用部门")
    String departmentName;

    @ApiModelProperty("变更位置")
    Long changeLocation;

    @ApiModelProperty("变更基站")
    Long changeReader;

    @ApiModelProperty("变更负责人")
    Long changeManagerId;

    @ApiModelProperty("变更负责人部门")
    Long changeManagerDeptId;

    @ApiModelProperty("变更保管人")
    Long changeApplyId;

    @ApiModelProperty("变更保管人部门")
    Long changeApplyDeptId;

    @ApiModelProperty("位置id")
    Long locationId;

    @ApiModelProperty("位置")
    String location;

    @ApiModelProperty("基站id")
    Long readerId;

    @ApiModelProperty("基站设备id")
    String deviceId;

    @ApiModelProperty("变更类型")
    Integer type;

    @TableField("process_type")
    @ApiModelProperty("备注")
    String remark;
}
