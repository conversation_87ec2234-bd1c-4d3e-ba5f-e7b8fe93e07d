package com.hightop.benyin.share.infrastructure.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hightop.magina.casual.terminal.UserTerminal;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

/**
 * 小程序页面路径映射
 * @Author: X.S
 * @date 2023/11/23 18:21
 */
@FieldDefaults(level = AccessLevel.PRIVATE)
@Data
@TableName("b_page_mapping")
public class PageMapping {
    /**
     * 页面路径
     */
    @TableField
    String path;
    /**
     * 终端编码
     */
    @TableField
    UserTerminal terminal;
    /**
     * 页面名称
     */
    @TableField
    String name;
}
