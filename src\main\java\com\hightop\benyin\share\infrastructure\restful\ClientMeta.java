package com.hightop.benyin.share.infrastructure.restful;

import com.hightop.fario.base.constant.StringConstants;

import java.lang.annotation.*;

/**
 * 元素据
 * @Author: X.S
 * @date 2024/02/26 18:49
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Repeatable(ClientMetas.class)
public @interface ClientMeta {
    /**
     * 元素据键
     * @return 键
     */
    String key();
    /**
     * 元素据值
     * @return 值
     */
    String value() default StringConstants.EMPTY;
}
