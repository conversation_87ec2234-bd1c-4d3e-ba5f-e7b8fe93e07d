package com.hightop.benyin.rfid.application.vo.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotNull;

/**
 * @Description: 基站心跳上报设置
 * @Author: X.S
 * @Date: 2023/12/22 17:16
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@ApiModel("基站心跳上报设置DTO")
public class ReaderSetHeartBeatDto {

    @ApiModelProperty("id")
    @NotNull(message = "基站信息不能为空")
    Long id;

    @ApiModelProperty("心跳间隔默认10秒")    //单位：秒
    @NotNull(message = "心跳间隔不能为空")
    Integer interval;
}
