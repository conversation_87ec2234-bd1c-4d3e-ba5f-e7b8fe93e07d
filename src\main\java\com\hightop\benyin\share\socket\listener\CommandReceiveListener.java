package com.hightop.benyin.share.socket.listener;

import com.github.yulichang.toolkit.SpringContentUtils;
import com.hightop.benyin.share.socket.event.CommandReceiveEvent;
import com.hightop.benyin.share.socket.service.CommandHandler;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Service
@Slf4j
public class CommandReceiveListener {


    @EventListener
    public void doHandler(CommandReceiveEvent event) {
        log.info("开始处理请求：{}，数据长度：{}，数据：{}", event.getCommandType(), event.getLength(), event.getData());
        ApplicationContext applicationContext = SpringContentUtils.getApplicationContext();
        CommandHandler commandHandler = (CommandHandler) applicationContext.getBean(
                event.getCommandType().getBeanName());
        commandHandler.handle(event.getClientName(), event.getDeviceId(), event.getLength(), event.getData());
    }
}
