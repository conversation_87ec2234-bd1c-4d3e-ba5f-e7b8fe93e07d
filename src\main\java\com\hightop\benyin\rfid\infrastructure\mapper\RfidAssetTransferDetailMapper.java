package com.hightop.benyin.rfid.infrastructure.mapper;

import com.github.yulichang.base.MPJBaseMapper;
import com.hightop.benyin.rfid.application.vo.query.RfidAssetTransferDetailQuery;
import com.hightop.benyin.rfid.infrastructure.entity.RfidAssetTransferDetail;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 资产变更mapper
 *
 * <AUTHOR>
 * @date 2024-10-12 16:36:01
 */
public interface RfidAssetTransferDetailMapper extends MPJBaseMapper<RfidAssetTransferDetail> {

    /**
     * 分页查询资产变更列表
     * @param query
     * @return
     */
    List<RfidAssetTransferDetail> pageList(@Param("qo") RfidAssetTransferDetailQuery query);
}
