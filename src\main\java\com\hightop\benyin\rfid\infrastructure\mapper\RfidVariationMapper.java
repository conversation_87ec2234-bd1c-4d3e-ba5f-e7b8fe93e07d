package com.hightop.benyin.rfid.infrastructure.mapper;

import com.github.yulichang.base.MPJBaseMapper;
import com.hightop.benyin.rfid.application.vo.query.RfidVariationQuery;
import com.hightop.benyin.rfid.infrastructure.entity.RfidVariation;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * RFID异动信息mapper
 *
 * <AUTHOR>
 * @date 2024-10-12 16:36:01
 */
public interface RfidVariationMapper extends MPJBaseMapper<RfidVariation> {

    /**
     * 查询最后一次的RFID异动信息
     * @param query
     * @return
     */
    public List<RfidVariation> lastRfidRvariationList(@Param("qo") RfidVariationQuery query);

    @Delete("TRUNCATE TABLE b_rfid_variation")
    void clear();
}
