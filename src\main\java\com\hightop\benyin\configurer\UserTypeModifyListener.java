package com.hightop.benyin.configurer;

import com.hightop.fario.base.util.FieldUtils;
import com.hightop.magina.standard.ums.user.basic.UserType;
import org.springframework.context.ApplicationListener;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.ContextRefreshedEvent;

import java.lang.reflect.Field;

/**
 * 用户类型枚举名称修改
 * @Author: X.S
 * @date 2023/10/20 16:05
 */
@Configuration
public class UserTypeModifyListener implements ApplicationListener<ContextRefreshedEvent> {
    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        // 显式修改用户类型
        Field name = FieldUtils.getDeclareField(UserType.class, "name");
        name.setAccessible(true);
        FieldUtils.set(name, UserType.PERMANENT, "内部用户");
        FieldUtils.set(name, UserType.TEMPORARY, "外部用户");
        name.setAccessible(false);
    }
}
