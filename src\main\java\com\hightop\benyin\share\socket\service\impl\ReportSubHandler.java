package com.hightop.benyin.share.socket.service.impl;

import com.hightop.benyin.rfid.application.service.RfidInfoService;
import com.hightop.benyin.share.infrastructure.util.DictUtil;
import com.hightop.benyin.share.socket.service.CommandHandler;
import com.hightop.benyin.share.socket.util.MsgUtil;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 标签上报-迁出命令处理器
 */
@Service
@Slf4j
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class ReportSubHandler implements CommandHandler {

    RfidInfoService rfidInfoService;
    RedisTemplate<String, String> redisTemplate;


    @Override
    public void handle(String clientName, String deviceId, Integer length, String params) {
        log.info("迁出标签上报, 设备: {}, params: {}", deviceId, params);
        //RFID 12位一个
//        List<String> reportList = Lists.newArrayList();
        List<String> rfidList = MsgUtil.splitString(params, MsgUtil.RFID_STR_LENGTH);
//        DictItem dictItem = dictItemDomainService.lambdaQuery()
//                .eq(DictItem::getDictId, DictUtil.SUB_DICT_ID)
//                .eq(DictItem::getLabel, DictUtil.SUB_DICT_NAME)
//                .one();
//        Long times = dictItem!=null?Long.parseLong(dictItem.getValue()):null;
//        for (String rfid : rfidList) {
//            String key = DictUtil.SCAN_CODE + deviceId+":"+DictUtil.SUB_SCAN+":"+rfid;
//            long count = redisTemplate.opsForValue().increment(key);
//            if(count==1L){
//                redisTemplate.expire(key, 60, TimeUnit.SECONDS);
//            }
//            log.info("迁出上报次数: {}, {}次", clientName, count);
//            if(count==times){
//                reportList.add(rfid);
//                redisTemplate.delete(key);
//            }
//        }
//        //上报次数统计 减少误报
//        if(CollectionUtils.isEmpty(reportList)){
//            return;
//        }
        rfidInfoService.reportHandler(rfidList, deviceId, DictUtil.SUB_SCAN, DictUtil.REPORT, null);
    }
}
