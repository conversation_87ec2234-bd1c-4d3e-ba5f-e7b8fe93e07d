package com.hightop.benyin.share.infrastructure.util;

import com.hightop.magina.core.exception.MaginaException;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 品牌管理
 * <AUTHOR>
 */
public class CopyUtil {

    /**
     * 单体复制
     */
    public static <T> T copy(Object source, Class<T> clazz) {
        if (source == null) {
            return null;
        }
        
        try {
            T obj = clazz.newInstance();
            BeanUtils.copyProperties(source, obj);
            
            return obj;
        } catch (Exception e) {
            throw new MaginaException(e.getMessage(), e);
        }
    }

    /**
     * 列表复制
     */
    public static <T> List<T> copyList(List<?> source, Class<T> clazz) {
        List<T> target = new ArrayList<>();
        if (!CollectionUtils.isEmpty(source)) {
            for (Object c : source) {
                T obj = copy(c, clazz);
                target.add(obj);
            }
        }
        return target;
    }
}
