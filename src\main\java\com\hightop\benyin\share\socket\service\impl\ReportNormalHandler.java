package com.hightop.benyin.share.socket.service.impl;

import com.github.yulichang.toolkit.SpringContentUtils;
import com.hightop.benyin.rfid.application.service.RfidInfoService;
import com.hightop.benyin.rfid.domain.service.RfidAssetServiceDomain;
import com.hightop.benyin.rfid.domain.service.RfidReaderServiceDomain;
import com.hightop.benyin.rfid.infrastructure.entity.RfidAsset;
import com.hightop.benyin.rfid.infrastructure.entity.RfidReader;
import com.hightop.benyin.share.domain.event.ApiLogEvent;
import com.hightop.benyin.share.infrastructure.enums.MessageType;
import com.hightop.benyin.share.socket.enums.CommandType;
import com.hightop.benyin.share.socket.service.CommandHandler;
import com.hightop.benyin.share.socket.util.MsgUtil;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 日常扫描数据上报-正常标签数据-命令处理器
 */
@Service
@Slf4j
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class ReportNormalHandler implements CommandHandler {
    RfidReaderServiceDomain rfidReaderServiceDomain;
    RfidAssetServiceDomain rfidAssetServiceDomain;

    @Override
    public void handle(String clientName, String deviceId, Integer length, String params) {
        log.info("日常扫描数据上报-正常标签数据, clientName: {}, params: {}", clientName, params);
        ApplicationContext applicationContext = SpringContentUtils.getApplicationContext();
        RfidReader rfidReader = rfidReaderServiceDomain.lambdaQuery().eq(RfidReader::getIpAddr, clientName).one();
        if (rfidReader == null) {
            log.error("日常扫描数据上报-正常标签数据, clientName: {},  rfidReader is null", clientName);
            ApiLogEvent apiLogEvent = new ApiLogEvent(this, MessageType.SEND, CommandType.DOWNLOAD_TAG,
                    deviceId, null, "ip地址找不到对应的基站！", false);
            applicationContext.publishEvent(apiLogEvent);
            return;
        }
        // 解析参数
        String sizeStr = params.substring(0, 2);
        log.info("日常扫描数据上报-正常标签数据, clientName: {}, sizeStr: {}", clientName, sizeStr);
        String rfidStr = params.substring(2, params.length() - 1);
        List<String> rfidList = MsgUtil.splitString(rfidStr, MsgUtil.RFID_STR_LENGTH);
        rfidAssetServiceDomain.lambdaUpdate()
                .set(RfidAsset::getCurrLocationId, rfidReader.getLocationId())
                .set(RfidAsset::getCurrReaderId, rfidReader.getId())
                .ne(RfidAsset::getCurrReaderId, rfidReader.getId())
                .in(RfidAsset::getRfidCode, rfidList).update();
    }
}
