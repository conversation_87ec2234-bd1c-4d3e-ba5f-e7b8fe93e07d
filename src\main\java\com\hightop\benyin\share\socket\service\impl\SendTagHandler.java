package com.hightop.benyin.share.socket.service.impl;

import com.hightop.benyin.share.socket.service.CommandHandler;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

/**
 * 下发基站的保存标签回执
 */
@Service
@Slf4j
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class SendTagHandler implements CommandHandler {

    ApplicationEventPublisher applicationEventPublisher;

    @Override
    public void handle(String clientName, String deviceId, Integer length, String params) {
        log.info("下发基站的保存标签回执, client : {},deviceId : {},  下发数量: {}", clientName,deviceId, params);

    }
}
