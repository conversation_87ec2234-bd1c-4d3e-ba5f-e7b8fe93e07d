package com.hightop.benyin.system.application.service;

import com.alibaba.fastjson.JSON;
import com.hightop.benyin.system.domain.event.DataLogEvent;
import com.hightop.benyin.system.infrastructure.enums.BusinessType;
import com.hightop.benyin.system.infrastructure.enums.OperationType;
import com.hightop.fario.common.core.util.ExecutorUtils;
import com.hightop.magina.casual.session.ApplicationSessions;
import com.hightop.magina.standard.ums.department.Department;
import com.hightop.magina.standard.ums.department.DepartmentDomainService;
import com.hightop.magina.standard.ums.department.DepartmentManageService;
import com.hightop.magina.standard.ums.department.user.DepartmentUserDomainService;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@Service
@Primary
public class DepartmentManageExtendsService extends DepartmentManageService {
    ApplicationEventPublisher applicationEventPublisher;
    DepartmentDomainService departmentDomainService;

    public DepartmentManageExtendsService(DepartmentDomainService departmentDomainService, DepartmentUserDomainService departmentUserDomainService
            , ApplicationEventPublisher applicationEventPublisher) {
        super(departmentDomainService, departmentUserDomainService);
        this.applicationEventPublisher = applicationEventPublisher;
        this.departmentDomainService = departmentDomainService;
    }

    @Override
    public boolean updateById(Department department) {
        super.updateById(department);
        return Boolean.TRUE;
    }

    @Override
    public boolean removeById(Long id) {
        Department department = departmentDomainService.getById(id);
        super.removeById(id);
        return Boolean.TRUE;
    }

    @Override
    public boolean updateEnable(Long id, Boolean flag) {
        Department department = departmentDomainService.getById(id);
        OperationType operationType = flag?OperationType.ENABLE:OperationType.DISABLE;

        this.departmentDomainService.lambdaUpdate()
                .set(Department::getIsEnable, flag)
                .eq(Department::getId, id)
                .update();
        return Boolean.FALSE;
    }
}
