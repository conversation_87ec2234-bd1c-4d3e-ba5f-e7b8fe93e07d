package com.hightop.benyin.rfid.domain.service;

import com.github.yulichang.base.MPJBaseServiceImpl;
import com.hightop.benyin.rfid.application.vo.query.RfidAssetTransferQuery;
import com.hightop.benyin.rfid.infrastructure.entity.RfidAssetFlow;
import com.hightop.benyin.rfid.infrastructure.entity.RfidAssetTransfer;
import com.hightop.benyin.rfid.infrastructure.mapper.RfidAssetFlowMapper;
import com.hightop.benyin.rfid.infrastructure.mapper.RfidAssetTransferMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * rfid资产变更管理领域服务
 *
 * <AUTHOR>
 * @date 2024-10-12 16:36:01
 */
@Service
public class RfidAssetTransferServiceDomain extends MPJBaseServiceImpl<RfidAssetTransferMapper, RfidAssetTransfer> {

    public List<RfidAssetTransfer> pageList(RfidAssetTransferQuery query){
        return baseMapper.pageList(query);
    }
}
