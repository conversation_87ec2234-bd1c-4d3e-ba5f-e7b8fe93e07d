package com.hightop.benyin.system.api.controller;

import com.hightop.benyin.share.infrastructure.util.DownloadResponseUtil;
import com.hightop.benyin.system.api.vo.UserManageExtendVo;
import com.hightop.benyin.system.api.vo.dto.UserAddDto;
import com.hightop.benyin.system.api.vo.dto.UserResetPassDto;
import com.hightop.benyin.system.api.vo.dto.UserStatusDto;
import com.hightop.benyin.system.api.vo.dto.UserUpdateDto;
import com.hightop.benyin.system.api.vo.query.UserExtendPageQuery;
import com.hightop.benyin.system.api.vo.query.UserRolePageQuery;
import com.hightop.benyin.system.application.service.UserExtendService;
import com.hightop.fario.base.web.Operable;
import com.hightop.fario.base.web.Operation;
import com.hightop.fario.base.web.RestResponse;
import com.hightop.fario.common.mybatis.DataGrid;
import com.hightop.magina.standard.behavior.operation.IgnoreOperationLog;
import com.hightop.magina.standard.ums.permission.Permission;
import com.hightop.magina.standard.ums.permission.Permissions;
import com.hightop.magina.standard.ums.role.Role;
import com.hightop.magina.standard.ums.user.manage.UserManageVo;
import com.hightop.magina.standard.ums.user.manage.UserResetPasswordDto;
import com.hightop.magina.standard.ums.user.role.UserRole;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

@RequestMapping("/user-extends")
@RestController
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Api(tags = "用户管理-扩展功能")
public class UserExtendController {

    UserExtendService userExtendService;

    @PostMapping
    @ApiOperation("用户分页查询")
    @IgnoreOperationLog
    public DataGrid<UserManageExtendVo> page(@RequestBody UserExtendPageQuery pageQuery) {
        return this.userExtendService.page(pageQuery);
    }

    @PostMapping("/auth")
    @ApiOperation("用户分页查询")
    @IgnoreOperationLog
    public DataGrid<UserManageExtendVo> authPage(@RequestBody UserExtendPageQuery pageQuery) {
        return this.userExtendService.authPage(pageQuery);
    }

    @PostMapping("/list")
    @ApiOperation("有效用户查询")
    @IgnoreOperationLog
    public List<UserManageExtendVo> getUserList(@RequestBody UserExtendPageQuery pageQuery) {
        return this.userExtendService.getUserList(pageQuery);
    }

    @GetMapping("/member-page/{roleId}")
    @ApiOperation("角色成员分页查询")
    public DataGrid<UserManageVo> listPage(@ApiParam("角色id") @PathVariable Long roleId, UserRolePageQuery pageQuery) {
        return this.userExtendService.getUsersByRole(roleId, pageQuery);
    }

    @GetMapping("/add-member-page/{roleId}")
    @ApiOperation("角色成员添加分页查询")
    public DataGrid<UserManageVo> addMemberPage(@ApiParam("角色id") @PathVariable Long roleId, UserRolePageQuery pageQuery) {
        return this.userExtendService.toBeAddedUsers(roleId, pageQuery);
    }

    @GetMapping("getCurrRoles")
    @ApiOperation("获取当前用户角色")
    public List<Role> addMemberPage() {
        return this.userExtendService.getUserRoles();
    }

    @PostMapping("/add")
    @ApiOperation("用户添加")
    public RestResponse<Void> add(@Validated @RequestBody UserAddDto userAddDto) {
        return Operation.ADD.response(this.userExtendService.add(userAddDto));
    }


    @PutMapping
    @ApiOperation("启停用")
    public RestResponse<Void> enableDisable(@Validated @RequestBody UserStatusDto userStatusDto) {
        return Operation.UPDATE.response(this.userExtendService.enableDisable(userStatusDto));
    }

    @PutMapping("/update")
    @ApiOperation("修改用户信息")
    public RestResponse<Void> enableDisable(@Validated @RequestBody UserUpdateDto userStatusDto) {
        return Operation.UPDATE.response(this.userExtendService.update(userStatusDto));
    }


    @PostMapping("/update-password")
    @ApiOperation("密码修改")
    public RestResponse<Void> resetPassword(@RequestBody UserResetPassDto userResetPassDto) {
        return Operable.of("密码修改").response(this.userExtendService.updatePassword(userResetPassDto));
    }


    @PostMapping("/reset-password")
    @ApiOperation("密码重置")
    public RestResponse<Void> resetPassword(@RequestBody UserResetPasswordDto userResetPasswordDto) {
        return Operable.of("密码重置").response(this.userExtendService.resetPassword(userResetPasswordDto));
    }

    @DeleteMapping("clear")
    @ApiOperation("清理用户")
    public RestResponse<Void> delete() {
        return Operation.UPDATE.response(this.userExtendService.clearUser());
    }

    /**
     * 导入位置
     **/
    @PostMapping("/import")
    @ApiOperation(value = "导入用户数据")
    public RestResponse<Void> impor(@RequestParam("file") MultipartFile file) throws Exception {
        boolean b = this.userExtendService.importData(file);
        if (!b) {
            return new RestResponse(500, "解析失败", null, null);
        }
        return RestResponse.message("导入成功");
    }

    /**
     * 下载位置模板
     *
     * @param response
     * @return
     */
    @ApiOperation("下载导入用户模板")
    @GetMapping("/downTemplate")
    public RestResponse<Void> downTemplate(HttpServletResponse response) {
        Boolean b = userExtendService.downTemplate(response);
        if (!b) {
            return new RestResponse(500, "下载失败", null, null);
        }
        return RestResponse.message("下载成功");
    }

    /**
     * 导出当前资产数据
     *
     * @param response
     * @param pageQuery
     * @return
     */
    @ApiOperation("/导出用户数据")
    @PostMapping("/export")
    @IgnoreOperationLog
    public RestResponse<Void> downOrderData(HttpServletResponse response, @RequestBody UserExtendPageQuery pageQuery) {
        try {
            //页面下载设置
            Workbook workbook = userExtendService.downloadData(pageQuery);
            DownloadResponseUtil.addDownLoadHeader(response, "用户信息.xlsx");
            workbook.write(response.getOutputStream());
        } catch (IOException e) {
            e.printStackTrace();
            return new RestResponse(500, "导出失败", null, null);
        }
        return RestResponse.message("导出成功");
    }

}
