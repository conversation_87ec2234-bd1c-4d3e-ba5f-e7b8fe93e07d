package com.hightop.benyin.rfid.application.handler;

import cn.afterturn.easypoi.excel.entity.result.ExcelVerifyHandlerResult;
import cn.afterturn.easypoi.handler.inter.IExcelVerifyHandler;
import com.google.common.collect.Lists;
import com.hightop.benyin.rfid.application.vo.excel.AssetExcel;
import com.hightop.benyin.rfid.domain.service.RfidAssetServiceDomain;
import com.hightop.benyin.rfid.domain.service.RfidReaderServiceDomain;
import com.hightop.benyin.rfid.infrastructure.entity.RfidAsset;
import com.hightop.benyin.rfid.infrastructure.entity.RfidReader;
import com.hightop.benyin.share.infrastructure.util.DictUtil;
import com.hightop.benyin.system.application.service.SystemExtendService;
import com.hightop.benyin.system.domain.service.AssetTypeDomainService;
import com.hightop.benyin.system.domain.service.LocationDomainService;
import com.hightop.benyin.system.domain.service.UserInfoDomainService;
import com.hightop.benyin.system.infrastructure.entity.AssetType;
import com.hightop.benyin.system.infrastructure.entity.Location;
import com.hightop.benyin.system.infrastructure.entity.UserInfo;
import com.hightop.fario.base.util.StringUtils;
import com.hightop.magina.core.component.ApplicationContexts;
import com.hightop.magina.standard.code.dictionary.item.DictItem;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.StringJoiner;

public class AssetExcelVerifyHandler implements IExcelVerifyHandler<AssetExcel> {
    @Override
    public ExcelVerifyHandlerResult verifyHandler(AssetExcel data) {
        RfidReaderServiceDomain rfidReaderServiceDomain = ApplicationContexts.getBean(RfidReaderServiceDomain.class);
        LocationDomainService locationDomainService = ApplicationContexts.getBean(LocationDomainService.class);
        UserInfoDomainService userInfoDomainService = ApplicationContexts.getBean(UserInfoDomainService.class);
        RfidAssetServiceDomain rfidassetServiceDomain = ApplicationContexts.getBean(RfidAssetServiceDomain.class);
        SystemExtendService systemExtendService = ApplicationContexts.getBean(SystemExtendService.class);
        AssetTypeDomainService assetTypeDomainService = ApplicationContexts.getBean(AssetTypeDomainService.class);


        StringJoiner joiner = new StringJoiner(",");
        if (StringUtils.isNotBlank(data.getReaderCode())) {
            RfidReader rfidReader = rfidReaderServiceDomain.lambdaQuery()
                    .eq(RfidReader::getCode, data.getReaderCode()).one();
            if (Objects.isNull(rfidReader)) {
                joiner.add("基站编码有误");
            } else {
                data.setLocationId(rfidReader.getLocationId());
                data.setReaderId(rfidReader.getId());
            }
        }
        RfidAsset rfidAsset = rfidassetServiceDomain.lambdaQuery().eq(RfidAsset::getCode, data.getCode()).one();
        if (rfidAsset != null) {
            data.setOriRfidAsset(rfidAsset);
        }

        if (StringUtils.isNotBlank(data.getParentCode())) {
//            Long rfidCount = rfidassetServiceDomain.lambdaQuery().eq(RfidAsset::getCode, data.getParentCode()).count();
//            if (rfidCount == 0L) {
//                joiner.add("上级资产编码数据不存在");
//            }
        }

//        if (StringUtils.isNotBlank(data.getRfidCode())) {
//            Long rfidCount = rfidassetServiceDomain.lambdaQuery().eq(RfidAsset::getRfidCode, data.getRfidCode()).count();
//            if (rfidCount > 0L) {
//                joiner.add("RFID编码已存在");
//            }
//            data.setHasTag(true);
//        }
//        Long count = rfidassetServiceDomain.lambdaQuery().eq(RfidAsset::getOriRfidCode, data.getOriRfidCode()).count();
//        if (count > 0L) {
//            joiner.add("原资产标签号已存在");
//        }
        if (StringUtils.isNotBlank(data.getAssetParentType())) {
            AssetType assetType = assetTypeDomainService.lambdaQuery()
                    .eq(AssetType::getName, data.getAssetParentType())
                    .ne(AssetType::getParentId, AssetType.TOP)
                    .one();
            if (Objects.isNull(assetType)) {
                assetType = assetTypeDomainService.lambdaQuery()
                        .eq(AssetType::getName, data.getAssetParentType())
                        .eq(AssetType::getParentId, AssetType.TOP)
                        .one();
                if(assetType == null){
                    joiner.add("资产大类有误！");
                }else{
                    data.setAssetParentType(assetType.getCode());
                    data.setAssetParentId(assetType.getId());
                }
            } else {
                data.setAssetParentType(assetType.getCode());
                data.setAssetParentId(assetType.getId());
            }
        }
        if (StringUtils.isNotBlank(data.getAssetMiddleType())
                &&data.getAssetParentId()!=null) {
            AssetType assetType = assetTypeDomainService.getByParentName(data.getAssetParentId(),data.getAssetMiddleType());
            if (Objects.isNull(assetType)) {
                joiner.add("资产小类有误！");
            } else {
                data.setAssetMiddleType(assetType.getCode());
                data.setAssetMiddleId(assetType.getId());
            }
        }


        if (StringUtils.isNotBlank(data.getAssetType())
        &&data.getAssetMiddleId()!=null) {
            AssetType assetType = assetTypeDomainService.getByParentName(data.getAssetMiddleId(),data.getAssetType());
            if (Objects.isNull(assetType)) {
                joiner.add("分类名称有误！");
            } else {
                data.setAssetType(assetType.getCode());
            }
        }

//        if(StringUtils.isNotBlank(data.getParentCode())){
//            Long rfidCount = rfidassetServiceDomain.lambdaQuery().eq(RfidAsset::getCode, data.getParentCode()).count();
//            if (rfidCount == 0L) {
//                data.setParentCode(DictUtil.ROOT_CODE);
//            }
//        }

        if (StringUtils.isNotBlank(data.getUseState())) {
            DictItem dictItem = systemExtendService.getByDictCodeAndLabel(DictUtil.DICT_USE_STATE, data.getUseState());
            if (Objects.isNull(dictItem)) {
                joiner.add("使用状态有误,参考数据字典" + DictUtil.DICT_USE_STATE);
            } else {
                data.setUseState(dictItem.getValue());
            }
        }

        if (StringUtils.isNotBlank(data.getAssetPurpose())) {
            DictItem dictItem = systemExtendService.getByDictCodeAndLabel(DictUtil.DICT_ASSET_PURPOSE, data.getAssetPurpose());
            if (Objects.isNull(dictItem)) {
                joiner.add("资产用途有误,参考数据字典" + DictUtil.DICT_ASSET_PURPOSE);
            } else {
                data.setAssetPurpose(dictItem.getValue());
            }
        }

        if (StringUtils.isNotBlank(data.getCardType())) {
            DictItem dictItem = systemExtendService.getByDictCodeAndLabel(DictUtil.DICT_CARD_TYPE, data.getCardType());
            if (Objects.isNull(dictItem)) {
                joiner.add("卡片类型有误,参考数据字典" + DictUtil.DICT_CARD_TYPE);
            } else {
                data.setCardType(dictItem.getValue());
            }
        }

        if (StringUtils.isNotBlank(data.getAcquireMode())) {
            DictItem dictItem = systemExtendService.getByDictCodeAndLabel(DictUtil.DICT_ACQUIRE_MODE, data.getAcquireMode());
            if (Objects.isNull(dictItem)) {
                joiner.add("取得方式有误,参考数据字典" + DictUtil.DICT_ACQUIRE_MODE);
            } else {
                data.setAcquireMode(dictItem.getValue());
            }
        }
        if (StringUtils.isNotBlank(data.getFinancialClassify())) {
            String[] financialClassify = data.getFinancialClassify().split(",");
            List<Integer> list = Lists.newArrayList();
            for (String item : financialClassify) {
                DictItem dictItem = systemExtendService.getByDictCodeAndLabel(DictUtil.DICT_FINANCIAL_CLASSIFY, item);
                if (Objects.isNull(dictItem)) {
                    joiner.add("财务归口有误,参考数据字典" + DictUtil.DICT_FINANCIAL_CLASSIFY);
                } else {
                    list.add(Integer.parseInt(dictItem.getValue()));
                }
            }
            data.setFinancialClassifyList(list);
        }

        if (StringUtils.isNotBlank(data.getApplyCode())) {
            UserInfo userBasic = userInfoDomainService.lambdaQuery().eq(UserInfo::getCode, data.getApplyCode()).one();
            if (userBasic == null) {
                joiner.add("领用人账号有误");
            } else {
                data.setApplyId(userBasic.getId());
                data.setApplyName(userBasic.getName());
                data.setDepartmentId(userBasic.getDepartmentId());
            }
        }
        if (StringUtils.isNotBlank(data.getLocationCode())) {
            Location location = locationDomainService.lambdaQuery().eq(Location::getCode, data.getLocationCode()).one();
            if (location == null) {
                joiner.add("位置编码有误");
            } else {
                data.setLocationId(location.getId());
            }
        }
        if (StringUtils.isNotBlank(data.getReaderCode())) {
            RfidReader rfidReader = rfidReaderServiceDomain.lambdaQuery().eq(RfidReader::getCode, data.getReaderCode()).one();
            if (rfidReader == null) {
                joiner.add("基站编码有误");
            } else {
                data.setReaderId(rfidReader.getId());
            }
        }
        if (StringUtils.isNotBlank(data.getManagerCode())) {
            UserInfo userBasic1 = userInfoDomainService.lambdaQuery().eq(UserInfo::getCode, data.getManagerCode()).one();
            if (userBasic1 == null) {
                joiner.add("责任人账号有误");
            } else {
                data.setManagerId(userBasic1.getId());
                data.setManagerName(userBasic1.getName());
                data.setManagerDeptId(userBasic1.getDepartmentId());
            }
        }

        if (StringUtils.isNotBlank(data.getHasTagStr())) {
            if (data.getHasTagStr().equals("是")) {
                data.setHasTag(true);
            } else {
                data.setHasTag(false);
            }
        }
        if (StringUtils.isNotBlank(data.getIsReportStr())) {
            if (data.getIsReportStr().equals("是")) {
                data.setIsReport(true);
            } else {
                data.setIsReport(false);
            }
        }
        if (StringUtils.isNotBlank(data.getIsStatisStr())) {
            if (data.getIsStatisStr().equals("是")) {
                data.setIsStatis(true);
            } else {
                data.setIsStatis(false);
            }
        }
        if (StringUtils.isNotBlank(data.getIsTakeStatisStr())) {
            if (data.getIsTakeStatisStr().equals("是")) {
                data.setIsTakeStatis(true);
            } else {
                data.setIsTakeStatis(false);
            }
        }
        if (StringUtils.isNotBlank(data.getIsTakeStr())) {
            if (data.getIsTakeStr().equals("是")) {
                data.setIsTake(true);
            } else {
                data.setIsTake(false);
            }
        }

        if (StringUtils.isNotBlank(data.getIsScanStr())) {
            if (data.getIsScanStr().equals("是")) {
                data.setIsScan(true);
            } else {
                data.setIsScan(false);
            }
        }
        if (StringUtils.isNotBlank(data.getPriceStr())) {
            data.setPrice(new BigDecimal(data.getPriceStr()).multiply(new BigDecimal(100)).longValue());
        }

        if (StringUtils.isNotBlank(data.getInitSalvageStr())) {
            data.setInitSalvage(new BigDecimal(data.getInitSalvageStr()).multiply(new BigDecimal(100)).longValue());
        }

        if (StringUtils.isNotBlank(data.getNowSalvageStr())) {
            data.setNowSalvage(new BigDecimal(data.getNowSalvageStr()).multiply(new BigDecimal(100)).longValue());
        }

        if (StringUtils.isNotBlank(data.getDataSourceStr())) {
            data.setDataSource(data.getDataSourceStr().equals("期初导入") ? 1 : 0);
        }

        String errorMsg = joiner.toString();
        if (StringUtils.isNotBlank(errorMsg)) {
            return new ExcelVerifyHandlerResult(false, errorMsg);
        }
        return new ExcelVerifyHandlerResult(true);
    }
}
