package com.hightop.benyin.system.infrastructure.mapper;

import com.github.yulichang.base.MPJBaseMapper;
import com.hightop.benyin.system.api.vo.DepartmentInfoTreeVo;
import com.hightop.benyin.system.api.vo.query.DepartmentQuery;
import com.hightop.benyin.system.infrastructure.entity.DepartmentInfo;
import com.hightop.benyin.system.infrastructure.entity.UserInfo;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface DepartmentInfoMapper extends MPJBaseMapper<DepartmentInfo> {

    @Delete("TRUNCATE TABLE st_department")
    void clearDepartment();

    List<DepartmentInfoTreeVo> getDepartmentList(@Param("qo") DepartmentQuery query);

}
