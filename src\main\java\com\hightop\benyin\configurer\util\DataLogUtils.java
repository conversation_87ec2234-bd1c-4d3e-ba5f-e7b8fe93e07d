package com.hightop.benyin.configurer.util;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.core.enums.SqlMethod;
import com.baomidou.mybatisplus.core.metadata.TableInfo;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.google.common.collect.Lists;
import com.hightop.benyin.configurer.annotation.RecordLog;
import com.hightop.benyin.configurer.annotation.RecordLogField;
import com.hightop.benyin.rfid.infrastructure.enums.AssetChangeSource;
import com.hightop.benyin.share.infrastructure.util.DictUtil;
import com.hightop.benyin.share.infrastructure.util.ThreadLocalUtil;
import com.hightop.benyin.system.infrastructure.entity.DataLog;
import com.hightop.magina.casual.session.ApplicationSessions;
import com.hightop.magina.standard.code.dictionary.bind.DictItemEntry;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.ParameterMapping;
import org.apache.ibatis.mapping.SqlCommandType;
import org.apache.ibatis.reflection.MetaObject;
import org.apache.ibatis.session.Configuration;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.ibatis.type.TypeHandlerRegistry;
import org.mybatis.spring.SqlSessionUtils;

import java.lang.reflect.Field;
import java.text.DateFormat;
import java.time.LocalDateTime;
import java.util.*;

@Slf4j
public class DataLogUtils {

    /**
     * 获取SQL语句
     */
    public static String getSql(Configuration configuration, BoundSql boundSql) {
        Object parameterObject = boundSql.getParameterObject();
        List<ParameterMapping> parameterMappings = boundSql.getParameterMappings();
        String sql = boundSql.getSql().replaceAll("[\\s]+", " ");
        if (parameterMappings.size() == 0 && parameterObject == null) {
            return sql;
        }
        TypeHandlerRegistry typeHandlerRegistry = configuration.getTypeHandlerRegistry();
        if (typeHandlerRegistry.hasTypeHandler(parameterObject.getClass())) {
            sql = sql.replaceFirst("\\?", getParameterValue(parameterObject));
        } else {
            MetaObject metaObject = configuration.newMetaObject(parameterObject);
            for (ParameterMapping parameterMapping : parameterMappings) {
                String propertyName = parameterMapping.getProperty();
                if (metaObject.hasGetter(propertyName)) {
                    Object obj = metaObject.getValue(propertyName);
                    sql = sql.replaceFirst("\\?", getParameterValue(obj));
                } else if (boundSql.hasAdditionalParameter(propertyName)) {
                    Object obj = boundSql.getAdditionalParameter(propertyName);
                    sql = sql.replaceFirst("\\?", getParameterValue(obj));
                }
            }
        }
        return sql;
    }

    /**
     * 获取参数值
     */
    public static String getParameterValue(Object o) {
        if (o == null) {
            return "";
        }
        if (o instanceof String) {
            return "'" + o.toString() + "'";
        }
        if (o instanceof Date) {
            DateFormat formatter = DateFormat.getDateTimeInstance(DateFormat.DEFAULT, DateFormat.DEFAULT, Locale.CHINA);
            return "'" + formatter.format(o) + "'";
        }
        return o.toString();
    }

    /**
     * 获取两个相同实体对象不同属性值
     */
    public static List<DataLog> getDiffValue(Object object1, Object object2, RecordLog recordLog) {
        //对象都为null，返回空数据
        if (object1 == null && object2 == null) {
            return null;
        }
        Long id = null;
        List<DataLog> dataLogs = Lists.newArrayList();
        Object source = ThreadLocalUtil.get(DictUtil.SOURCE);
        Object businessCode = ThreadLocalUtil.get(DictUtil.BUSINESS_CODE);
        if (object1 == null) {//旧对象为null
            Field[] object2Fields = object2.getClass().getDeclaredFields();
            for (int i = 0; i < object2Fields.length; i++) {
                object2Fields[i].setAccessible(true);
                Field field = object2Fields[i];
                try {
                    Object value2 = object2Fields[i].get(object2);
                    //忽略表不存在的字段
                    if (field.isAnnotationPresent(TableField.class) && !field.getAnnotation(TableField.class).exist()) {
                        continue;
                    }
                    //获取id主键值
                    if ("id".equals(field.getName())) {
                        id = Long.parseLong(value2.toString());
                    }
                    RecordLogField recordLogField = field.getAnnotation(RecordLogField.class);
                    DataLog dataLog = new DataLog();
                    if (source != null) {
                        dataLog.setSource((AssetChangeSource) source);
                    }
                    if (businessCode != null) {
                        dataLog.setBusinessCode(businessCode.toString());
                    }
                    dataLog.setBusinessId(id);
                    dataLog.setDictCode(recordLogField.dictCode());
                    dataLog.setCreatedBy(ApplicationSessions.id());
                    dataLog.setCreatedAt(LocalDateTime.now());
                    dataLog.setBusinessType(recordLog.businessType());
                    dataLog.setDataType(recordLogField.dataType());
                    dataLog.setOperationType(recordLogField.value());
                    dataLog.setOriData("");
                    if(StringUtils.isNotBlank(recordLogField.dictCode())){
                        DictItemEntry dictItemEntry = (DictItemEntry)value2;
                        dataLog.setNewData(dictItemEntry.getValue());
                    }else{
                        dataLog.setNewData(value2.toString());
                    }
                    dataLogs.add(dataLog);
                } catch (IllegalAccessException e) {
                    log.error("非法操作", e);
                }
            }
        } else {//旧对象和新对象都不为null
            Field[] object1Fields = object1.getClass().getDeclaredFields();
            Field[] object2Fields = object2.getClass().getDeclaredFields();
            for (int i = 0; i < object1Fields.length; i++) {
                object1Fields[i].setAccessible(true);
                object2Fields[i].setAccessible(true);
                Field field = object1Fields[i];

                try {
                    Object value1 = object1Fields[i].get(object1);
                    Object value2 = object2Fields[i].get(object2);
                    //忽略表不存在的字段
                    if (field.isAnnotationPresent(TableField.class) && !field.getAnnotation(TableField.class).exist()) {
                        continue;
                    }
                    //获取id主键值
                    if ("id".equals(field.getName())) {
                        id = Long.parseLong(value1.toString());
                    }

                    //忽略表不记录日志的字段
                    if (!field.isAnnotationPresent(RecordLogField.class)) {
                        continue;
                    }
                    //新值为null处理
                    if (value2 == null) {
                        if (!field.isAnnotationPresent(TableField.class) || !field.getAnnotation(TableField.class).updateStrategy().equals(FieldStrategy.IGNORED)) {
                            continue;
                        }
                    }

                    RecordLogField recordLogField = field.getAnnotation(RecordLogField.class);
                    if (!Objects.equals(value1, value2)) {
                        DataLog dataLog = new DataLog();
                        dataLog.setBusinessId(id);
                        dataLog.setDictCode(recordLogField.dictCode());
                        dataLog.setCreatedBy(ApplicationSessions.id());
                        dataLog.setCreatedAt(LocalDateTime.now());
                        dataLog.setBusinessType(recordLog.businessType());
                        dataLog.setDataType(recordLogField.dataType());
                        dataLog.setOperationType(recordLogField.value());
                        if (field.getType().equals(List.class)) {
                            dataLog.setOriData(Objects.nonNull(value1) ? JSON.toJSONString(value1) : "");
                            dataLog.setNewData(Objects.nonNull(value2) ? JSON.toJSONString(value2) : "");
                        } else {
                            if(StringUtils.isNotBlank(recordLogField.dictCode())){
                                DictItemEntry dictItemEntry1 = (DictItemEntry)value1;
                                DictItemEntry dictItemEntry2 = (DictItemEntry)value2;
                                dataLog.setOriData(dictItemEntry1.getValue());
                                dataLog.setNewData(dictItemEntry2.getValue());
                            }else{
                                dataLog.setOriData(Objects.nonNull(value1) ? value1.toString() : "");
                                dataLog.setNewData(Objects.nonNull(value2) ? value2.toString() : "");
                            }
                        }
                        if(!dataLog.getNewData().equals(dataLog.getOriData())){
                            dataLogs.add(dataLog);
                        }
                    }
                } catch (IllegalAccessException e) {
                    log.error("非法操作", e);
                }
            }
        }
        return dataLogs;

    }

    /**
     * 查询旧数据(只获取第一条数据)
     */
    public static Object selectOldData(String sql, TableInfo tableInfo) {
        String selectSql = "AND " + sql.substring(sql.toUpperCase().lastIndexOf("WHERE") + 5);
        Map<String, Object> map = new HashMap<>(1);
        map.put(Constants.WRAPPER, Wrappers.query().eq("1", 1).last(selectSql));
        SqlSessionFactory sqlSessionFactory = SqlHelper.sqlSessionFactory(tableInfo.getEntityType());
        SqlSession sqlSession = sqlSessionFactory.openSession();
        List<?> oldData;
        try {
            oldData = sqlSession.selectList(tableInfo.getSqlStatement(SqlMethod.SELECT_LIST.getMethod()), map);
        } finally {
            SqlSessionUtils.closeSqlSession(sqlSession, sqlSessionFactory);
        }
        return oldData != null && oldData.size() > 0 ? oldData.get(0) : null;
    }

    /**
     * 比较修改前后数据
     */
    public static List<DataLog> getDataCompare(SqlCommandType sqlCommandType, String sql, TableInfo tableInfo, Object entity) {
        RecordLog recordLog = tableInfo.getEntityType().getAnnotation(RecordLog.class);
        if (SqlCommandType.INSERT.equals(sqlCommandType)) {//新增
            return DataLogUtils.getDiffValue(null, entity, recordLog);
        }
        if (SqlCommandType.UPDATE.equals(sqlCommandType)) {//修改
            return DataLogUtils.getDiffValue(selectOldData(sql, tableInfo), entity, recordLog);
        }
//        else if (SqlCommandType.DELETE.equals(sqlCommandType)) {//删除
//            return DataLogUtils.getDiffValue(selectOldData(sql, tableInfo), null, recordLog);
//        }
        return null;
    }
}
