package com.hightop.benyin.share.domain.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.zxing.WriterException;
import com.hightop.benyin.share.application.dto.QrCodeDto;
import com.hightop.benyin.share.infrastructure.util.QrCodeUtils;
import com.hightop.fario.base.util.IoUtils;
import com.hightop.fario.common.jackson.JacksonUtils;
import com.hightop.magina.core.exception.MaginaException;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.io.InputStream;
import java.util.Objects;

/**
 * 二维码服务
 * <AUTHOR>
 * @date 2024/09/02 10:06
 */
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Service
@Transactional(propagation = Propagation.NOT_SUPPORTED)
public class QrCodeDomainService {
    ObjectMapper objectMapper;

    /**
     * 生成带本印logo的二维码
     * @param data 二维码数据
     * @return base64图片
     */
    public String createDefaultQr(QrCodeDto<?> data) {
        try (InputStream inputStream = new ClassPathResource("logo.png").getInputStream()) {
            return this.createQr(data, inputStream);
        } catch (IOException e) {
            throw new MaginaException("二维码生成失败", e);
        }
    }

    /**
     * 生成二维码返回Base64(不带图片)
     * @param data 二维码数据
     * @return base64图片
     */
    public String createQr(QrCodeDto<?> data) {
        return this.createQr(data, (String) null);
    }

    /**
     * 生成二维码返回Base64(带图片)
     * @param data        二维码数据
     * @param inputStream logo图片
     * @return base64图片
     */
    public String createQr(QrCodeDto<?> data, InputStream inputStream) {
        return this.createQr(data, inputStream, null);
    }

    /**
     * 生成二维码图片（底部带文字）
     * @param data 二维码数据
     * @param text 底部文字
     * @return base64图片
     */
    public String createQr(QrCodeDto<?> data, String text) {
        return this.createQr(data, null, text);
    }

    /**
     * 生成二维码图片
     * @param data        二维码数据
     * @param inputStream logo图片
     * @param text        底部文字
     * @return base64图片
     */
    public String createQr(QrCodeDto<?> data, InputStream inputStream, String text) {
        try {
            return QrCodeUtils.createQrCode(JacksonUtils.serialize(objectMapper, data), inputStream, text);
        } catch (IOException | WriterException e) {
            throw new MaginaException("二维码生成失败", e);
        } finally {
            if (Objects.nonNull(inputStream)) {
                IoUtils.closeQuietly(inputStream);
            }
        }
    }
}
