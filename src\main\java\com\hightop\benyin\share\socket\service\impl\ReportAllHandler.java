package com.hightop.benyin.share.socket.service.impl;

import com.github.yulichang.toolkit.SpringContentUtils;
import com.hightop.benyin.rfid.application.service.RfidInfoService;
import com.hightop.benyin.rfid.domain.service.RfidAssetServiceDomain;
import com.hightop.benyin.rfid.domain.service.RfidReaderServiceDomain;
import com.hightop.benyin.rfid.infrastructure.entity.RfidAsset;
import com.hightop.benyin.rfid.infrastructure.entity.RfidReader;
import com.hightop.benyin.share.domain.event.ApiLogEvent;
import com.hightop.benyin.share.infrastructure.enums.MessageType;
import com.hightop.benyin.share.socket.enums.CommandType;
import com.hightop.benyin.share.socket.service.CommandHandler;
import com.hightop.benyin.share.socket.util.MsgUtil;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 集中扫描数据上报-命令处理器
 */
@Service
@Slf4j
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class ReportAllHandler implements CommandHandler {
    RfidReaderServiceDomain rfidReaderServiceDomain;
    RfidAssetServiceDomain rfidAssetServiceDomain;
    RfidInfoService rfidInfoService;

    @Override
    public void handle(String clientName, String deviceId, Integer length, String params) {
        log.info("日常扫描数据上报-命令处理器, clientName: {}, params: {}", clientName, params);
        ApplicationContext applicationContext = SpringContentUtils.getApplicationContext();
        RfidReader rfidReader = rfidReaderServiceDomain.lambdaQuery().eq(RfidReader::getIpAddr, clientName).one();
        if (rfidReader == null) {
            log.error("标签全量下载-处理器, clientName: {},  rfidReader is null", clientName);
            ApiLogEvent apiLogEvent = new ApiLogEvent(this, MessageType.SEND, CommandType.DOWNLOAD_TAG,
                    deviceId, null, "ip地址找不到对应的基站！", false);
            applicationContext.publishEvent(apiLogEvent);
            return;
        }
        Integer firstParamIndex = length*MsgUtil.RFID_STR_LENGTH;
        String type = params.substring(0,2);
        if(length>0){
            String rfidStr = params.substring(2,firstParamIndex+2);
            List<String> rfidList = MsgUtil.splitString(rfidStr, MsgUtil.RFID_STR_LENGTH);
            log.info("type:{},数量{}", type,rfidList.size());
        }

        //处理2层数据
        String lengthStr =  params.substring(firstParamIndex+2,firstParamIndex+6);
        byte[] lenthBytes = MsgUtil.convertMessage(lengthStr);
        int  length2 = lenthBytes[0]  * 256+ lenthBytes[1];
        if(length2>0){
            String type2 =  params.substring(firstParamIndex+6,firstParamIndex+8);
            int endIndex = firstParamIndex+8+(length2*MsgUtil.RFID_STR_LENGTH);
            String secendParam =  params.substring(firstParamIndex+8,endIndex);
            List<String> rfidLists = MsgUtil.splitString(secendParam, MsgUtil.RFID_STR_LENGTH);
            log.info("type:{},数量{}", type2,rfidLists.size());

        }
        //处理3层数据
        int firstParamIndex3 = firstParamIndex+8+(length2*MsgUtil.RFID_STR_LENGTH);
        String length3Str =  params.substring(firstParamIndex3,firstParamIndex3+4);
        int  length3 = lenthBytes[0]  * 256+ lenthBytes[1];
        if(length3>0){
            String type3 =  params.substring(firstParamIndex3+4,firstParamIndex3+6);
            String secendParam =  params.substring(firstParamIndex3+6,params.length());
            List<String> rfidLists = MsgUtil.splitString(secendParam, MsgUtil.RFID_STR_LENGTH);
            log.info("type:{},数量{}", type3,rfidLists.size());

        }
    }
}
