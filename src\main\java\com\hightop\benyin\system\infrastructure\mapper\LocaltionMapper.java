package com.hightop.benyin.system.infrastructure.mapper;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.yulichang.base.MPJBaseMapper;
import com.hightop.benyin.system.api.vo.LocationVo;
import com.hightop.benyin.system.api.vo.query.LocationQuery;
import com.hightop.benyin.system.infrastructure.entity.Location;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public interface LocaltionMapper extends MPJBaseMapper<Location> {
    /**
     * 获得所有未删除{@link Location#getIsAvailable()}的位置列表
     *
     * @return {@link List}
     */
    default List<Location> getAvailableLocations() {
        return this.selectList(Wrappers.<Location>lambdaQuery().eq(Location::getIsAvailable, true));
    }

    /**
     * 获得所有未删除{@link Location#getIsAvailable()}位置的id和位置映射
     *
     * @return {@link Map}
     */
    default Map<Long, Location> getAvailableLocationMapping() {
        return this.getAvailableLocations().stream().collect(Collectors.toMap(Location::getId, it -> it));
    }

    /**
     * 获得启用状态{@link Location#getIsEnable()}的位置列表
     *
     * @return {@link List}
     */
    default List<Location> getEnabledLocations() {
        return this.getAvailableLocations().stream().filter(Location::getIsEnable).collect(Collectors.toList());
    }

    /**
     * 获得所有启用状态{@link Location#getIsEnable()}位置的id和位置映射
     *
     * @return {@link Map}
     */
    default Map<Long, Location> getEnabledLocationMapping() {
        return this.getEnabledLocations().stream().collect(Collectors.toMap(Location::getId, it -> it));
    }

    /**
     * 列表树查询
     * @param query
     * @return
     */
    public  List<LocationVo> getLocationTreeList(@Param("qo") LocationQuery query);


    @Delete("TRUNCATE TABLE st_location")
    void clearLocation();

}
