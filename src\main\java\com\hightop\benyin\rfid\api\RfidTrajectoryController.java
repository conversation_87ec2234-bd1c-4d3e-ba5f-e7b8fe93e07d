package com.hightop.benyin.rfid.api;

import com.hightop.benyin.rfid.application.service.RfidTrajectoryService;
import com.hightop.benyin.rfid.application.vo.query.RfidTrajectoryQuery;
import com.hightop.benyin.rfid.infrastructure.entity.RfidTrajectory;
import com.hightop.fario.base.web.RestResponse;
import com.hightop.fario.common.mybatis.DataGrid;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * RFID资产轨迹rest接口
 *
 * <AUTHOR>
 * @date 2024-10-12 16:36:01
 */
@RequestMapping("/trajectory")
@RestController
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Api(tags = "RFID资产轨迹")
public class RfidTrajectoryController {
    RfidTrajectoryService rfidTrajectoryService;


    @GetMapping("/page")
    @ApiOperation("分页查询")
    public RestResponse<DataGrid<RfidTrajectory>> page(RfidTrajectoryQuery pageQuery) {
        return RestResponse.ok(this.rfidTrajectoryService.pageList(pageQuery));
    }


}
