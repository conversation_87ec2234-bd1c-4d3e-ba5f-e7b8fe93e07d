package com.hightop.benyin.system.api.vo.excel;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelIgnore;
import com.hightop.benyin.share.application.vo.ExcelBaseInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.List;

@Data
@ApiModel("位置信息导入模板")
public class LocationExcel extends ExcelBaseInfo {


    @Excel(name = "位置编码", width = 30, orderNum = "0")
    @NotBlank
    @Size(min = 1, max = 64, message = "位置编码长度在{min}至{max}之间")
    String code;

    @Excel(name = "位置名称", width = 30, orderNum = "1")
    @NotBlank
    @Size(min = 1, max = 64, message = "位置名称长度在{min}至{max}之间")
    String name;

    @Excel(name = "位置别称", width = 30, orderNum = "2")
    String anotherName;

    @Excel(name = "上级单位名称", width = 30, orderNum = "3")
    @ApiModelProperty("上级单位名称")
    String parentName;

    @Excel(name = "上级单位编码", width = 30, orderNum = "4")
    @ApiModelProperty("上级单位编码")
    @Size(min = 1, max = 64, message = "上级位置编码长度在{min}至{max}之间")
    String parentCode;

    @Excel(name = "部门列表", width = 30, orderNum = "5")
    @ApiModelProperty("部门列表")
    String departmentName;

    @Excel(name = "部门编码", width = 30, orderNum = "6")
    @ApiModelProperty("部门编码")
    String departmentCode;

    @ApiModelProperty("排序号")
    @Excel(name = "排序号", width = 30, orderNum = "7")
    Integer sort;

    @ExcelIgnore
    Long parentId;
    @ExcelIgnore
    Long id;

    @ExcelIgnore
    List<Long> departmentIds;


}
