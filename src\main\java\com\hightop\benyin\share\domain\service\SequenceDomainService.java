package com.hightop.benyin.share.domain.service;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hightop.benyin.share.infrastructure.entity.Sequence;
import com.hightop.benyin.share.infrastructure.mapper.SequenceMapper;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Objects;
import java.util.Optional;

/**
 * 序列号领域服务
 * @Author: X.S
 * @date 2023/10/23 14:44
 */
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Service
public class SequenceDomainService {
    RedisTemplate<String, String> redisTemplate;
    SequenceMapper sequenceMapper;
    /**
     * 年份后两位+月份+日期
     */
    private static final String YYMMDD = "yyMMdd";
    /**
     * yyMMdd格式化
     */
    private static final DateTimeFormatter DTF_YYMMDD = DateTimeFormatter.ofPattern(YYMMDD);
    /**
     * yyMMddHHmmss格式化
     */
    private static final DateTimeFormatter DTF_YYMMDDHHMMSS = DateTimeFormatter.ofPattern(YYMMDD + "HHmmss");
    /**
     * 十进制基数、序列号最大长度
     */
    private static final int BASE = 10, MAX_LENGTH = 19;

    /**
     * 序列号(prefix + sequenceLength自增序列号)
     * @param prefix         前缀
     * @param sequenceLength 序列号长度
     * @return 编号
     */
    public String nextSequence(String prefix, int sequenceLength) {
        // 需要保证序列长度long范围内
        assert MAX_LENGTH > sequenceLength : String.format("序列号长度不能超过%d", MAX_LENGTH);

        Sequence sequence =
            this.sequenceMapper.selectOne(
                Wrappers.<Sequence>lambdaQuery()
                    .eq(Sequence::getPrefix, prefix)
                    .eq(Sequence::getLength, sequenceLength)
                    // 添加排他锁
                    .last("FOR UPDATE")
            );

        long value =
            Optional.ofNullable(sequence)
                // 当db存在指定前缀的序列号 此时为行锁
                .map(it -> {
                    this.sequenceMapper.update(
                        it.setLength(null).setPrefix(null).increase(),
                        Wrappers.<Sequence>lambdaQuery().eq(Sequence::getPrefix, prefix)
                            .eq(Sequence::getLength, sequenceLength)
                    );

                    return it.getValue();
                })
                // 当没查询到给定前缀时 此时升级为表锁 再写入一条数据
                .orElseGet(() -> {
                    Sequence newSequence = new Sequence().setPrefix(prefix).setLength(sequenceLength).setValue(1L);
                    this.sequenceMapper.insert(newSequence);

                    return newSequence.getValue();
                });

        // 保证序列号不越界
        assert value < 0 || value > (Math.pow(BASE, sequenceLength) - 1)
            : String.format("序列号前缀%s，序列长度%d已经越界", prefix, sequenceLength);

        return String.format("%s%0" + sequenceLength + "d", prefix, value);
    }

    /**
     * 默认8位序列号的日期格式编号(prefix + 8位自增序列号)
     * @param prefix 前缀
     * @return 编号
     */
    public String nextSequence(String prefix) {
        return this.nextSequence(prefix, 8);
    }

    /**
     * 日期序列号(prefix + yyMMdd + sequenceLength自增序列号)
     * @param prefix         前缀
     * @param sequenceLength 序列号长度
     * @return 编号
     */
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public String nextDateSequence(String prefix, int sequenceLength) {
        LocalDateTime now = LocalDate.now().atTime(0, 0, 0), expireAt = now.plusDays(1L).plusSeconds(1);

        return this.increaseSequence(prefix + now.format(DTF_YYMMDD), sequenceLength, expireAt);
    }

    /**
     * 默认6位序列号的日期格式编号(prefix + yyMMdd + 6位自增序列号)
     * @param prefix 前缀
     * @return 编号
     */
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public String nextDateSequence(String prefix) {
        return this.nextDateSequence(prefix, 6);
    }

    /**
     * 日期时间序列号(prefix + yyMMddHHmmss + sequenceLength自增序列号)
     * @param prefix         前缀
     * @param sequenceLength 序列号长度
     * @return 编号
     */
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public String nextDatetimeSequence(String prefix, int sequenceLength) {
        LocalDateTime now = LocalDateTime.now(), expireAt = now.plusSeconds(2L);

        return this.increaseSequence(prefix + now.format(DTF_YYMMDDHHMMSS), sequenceLength, expireAt);
    }

    /**
     * 默认3位序列号的日期时间格式编号(prefix + yyMMdd + 6位自增序列号)
     * @param prefix 前缀
     * @return 编号
     */
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public String nextDatetimeSequence(String prefix) {
        return this.nextDatetimeSequence(prefix, 3);
    }

    /**
     * 递增redis编号生成
     * @param key            前缀
     * @param sequenceLength 序列号长度
     * @param expireAt       过期时间
     * @return 编号
     */
    @SuppressWarnings("unchecked")
    protected String increaseSequence(String key, int sequenceLength, LocalDateTime expireAt) {
        byte[] keyBytes = ((RedisSerializer<String>) this.redisTemplate.getKeySerializer()).serialize(key);
        Objects.requireNonNull(keyBytes);
        // 有效期内递增序列号
        Long sequence =
            this.redisTemplate.execute(con -> {
                Long incr = con.incr(keyBytes);
                con.expireAt(keyBytes, expireAt.atZone(ZoneId.systemDefault()).toInstant().getEpochSecond());

                return incr;
            }, true);

        return String.format("%s%0" + sequenceLength + "d", key, sequence);
    }
}
