package com.hightop.benyin.share.schedule;

import com.github.yulichang.toolkit.MPJWrappers;
import com.google.common.collect.Lists;
import com.hightop.benyin.rfid.application.service.RfidReaderFaultService;
import com.hightop.benyin.rfid.application.service.RfidVariationService;
import com.hightop.benyin.rfid.application.vo.dto.AssetTrajectoryDto;
import com.hightop.benyin.rfid.domain.event.AssetTrajectoryEvent;
import com.hightop.benyin.rfid.domain.service.RfidInfoServiceDomain;
import com.hightop.benyin.rfid.domain.service.RfidReaderServiceDomain;
import com.hightop.benyin.rfid.domain.service.RfidVariationServiceDomain;
import com.hightop.benyin.rfid.infrastructure.entity.RfidInfo;
import com.hightop.benyin.rfid.infrastructure.entity.RfidReader;
import com.hightop.benyin.rfid.infrastructure.entity.RfidReaderFault;
import com.hightop.benyin.rfid.infrastructure.entity.RfidVariation;
import com.hightop.benyin.rfid.infrastructure.enums.*;
import com.hightop.benyin.share.infrastructure.util.DictUtil;
import com.hightop.benyin.share.socket.util.SocketUtil;
import com.hightop.fario.base.util.CollectionUtils;
import com.hightop.magina.standard.code.property.Property;
import com.hightop.magina.standard.code.property.PropertyDomainService;
import com.hightop.magina.standard.task.job.ScheduleJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

@Component
@Slf4j
public class AssetSchedule {

    @Autowired
    private RfidInfoServiceDomain rfidInfoServiceDomain;

    @Autowired
    private RfidVariationServiceDomain rfidVariationServiceDomain;

    @Autowired
    private RfidReaderServiceDomain rfidReaderServiceDomain;

    @Autowired
    private PropertyDomainService propertyDomainService;

    @Autowired
    private RfidVariationService rfidVariationService;

    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;

    @Autowired
    private RfidReaderFaultService rfidReaderFaultService ;

    /**
     * 异动上报超时处理（一分钟内未上报5次，则认为误报）10秒一次
     *
     * @throws Exception
     */
    @ScheduleJob(id = 20001L, cron = "0/10 * * * * ? ", description = "异动上报超时处理")
    public void abnormalExpire() throws Exception {
        log.info("-------------------异动上报超时处理及基站断开连接处理-------------------");

        Property property = propertyDomainService.getByCode(DictUtil.ABNORMAL_RULE);
        int abnormalCount = property == null ? 5 : Integer.parseInt(property.getValue());
        //异动上报判误处理
        List<RfidInfo> rfidInfoList = rfidInfoServiceDomain.lambdaQuery()
                .eq(RfidInfo::getStatus, 0)
                .lt(RfidInfo::getReportCount, abnormalCount)
                .in(RfidInfo::getType, Lists.newArrayList(DictUtil.ADD_SCAN, DictUtil.SUB_SCAN))
                .list();
        rfidInfoList.forEach(rfidInfo -> {
            if (rfidInfo.getExpireTime().isBefore(LocalDateTime.now())) {
                rfidInfo.setStatus(4);
                rfidInfoServiceDomain.updateById(rfidInfo);
            }
        });

        // 异动超时处理
        List<RfidVariation> rfidVariations = rfidVariationServiceDomain.lambdaQuery()
                .eq(RfidVariation::getStatus, VariationStatus.NOTICE)
                .list();
        rfidVariations.forEach(variation -> {
            if (variation.getExpireTime().isBefore(LocalDateTime.now())) {
                variation.setStatus(VariationStatus.OVERTIME);
                rfidVariationServiceDomain.updateById(variation);
            }
        });

    }

    /**
     * 异动停止上报后，已上报的异动自动恢复 2分钟一次
     *
     * @throws Exception
     */
    @ScheduleJob(id = 20002L, cron = "0 0/2 * * * ? *", description = "异动上报自动处理恢复")
    public void abnormaRecover() throws Exception {
        log.info("-------------------异动上报自动处理恢复");
        Property property = propertyDomainService.getByCode(DictUtil.ABNORMAL_IGNORE);
        int days = property == null ? 1 : Integer.parseInt(property.getValue());
        days = days * -1;
        //超过5分钟未上报的异动自动处理恢复
        LocalDateTime expireTime = LocalDateTime.now().plusMinutes(days);
        List<RfidVariation> outVariations = this.rfidVariationServiceDomain.selectJoinList(RfidVariation.class, MPJWrappers.lambdaJoin()
                .selectAll(RfidVariation.class)
                .leftJoin(RfidInfo.class, RfidInfo::getId, RfidVariation::getInfoId)
                .eq(RfidVariation::getStatus, VariationStatus.WAIT)
        );
        outVariations.forEach(variation -> {
            List<RfidInfo> rfidInfos = rfidInfoServiceDomain.lambdaQuery()
                    .eq(RfidInfo::getRfidCode, variation.getRfidCode())
                    .in(RfidInfo::getType, Lists.newArrayList(DictUtil.ADD_SCAN, DictUtil.SUB_SCAN))
                    .in(RfidInfo::getStatus, Lists.newArrayList(DictUtil.REPORT, DictUtil.VARIATION))
                    .le(RfidInfo::getLastReportTime, expireTime)
                    .orderByDesc(RfidInfo::getLastReportTime).list();
            if(CollectionUtils.isNotEmpty(rfidInfos)){
                List<Long> infoIds =rfidInfos.stream().map(RfidInfo::getId).collect(Collectors.toList());
                if(variation.getLastInfoId() == null){
//                    停止上报 自动忽略
                    if(infoIds.contains(variation.getInfoId())){
                        ignortAbnormal(variation,Lists.newArrayList(variation.getInfoId()));
                    }
                }else{
                    //都停止上报 自动忽略
                    if(infoIds.contains(variation.getInfoId())&&infoIds.contains(variation.getLastInfoId())) {
                        ignortAbnormal(variation,Lists.newArrayList(variation.getInfoId(),variation.getLastInfoId()));
                    }
                    //首次上报停止
                    if(infoIds.contains(variation.getInfoId())&&!infoIds.contains(variation.getLastInfoId())) {
                        variation.setAbnormalType(AbnormalType.VARIATION);
                        RfidInfo rfidInfo = rfidInfoServiceDomain.getById(variation.getInfoId());
                        variation.setAbnormalAnalyze(rfidVariationService.getAbnormalAnalyze(variation,rfidInfo));

                        rfidVariationServiceDomain.updateById(variation);

                        rfidInfoServiceDomain.lambdaUpdate()
                                .set(RfidInfo::getStatus, 4)
                                .in(RfidInfo::getId, variation.getInfoId()).update();
                    }
                    //后续上报停止
                    if(!infoIds.contains(variation.getInfoId())&&infoIds.contains(variation.getLastInfoId())) {
                        variation.setAbnormalType(AbnormalType.OUT_UNKNOWN);
                        RfidInfo rfidInfo = rfidInfoServiceDomain.getById(variation.getInfoId());
                        variation.setAbnormalAnalyze(rfidVariationService.getAbnormalAnalyze(variation,rfidInfo));
                        variation.setScanLocationId(null);
                        variation.setScanLocation(null);
                        variation.setScanReaderId(null);
                        variation.setScanReader(null);
                        rfidVariationServiceDomain.updateById(variation);
                        rfidInfoServiceDomain.lambdaUpdate()
                                .set(RfidInfo::getStatus, 4)
                                .in(RfidInfo::getId, variation.getLastInfoId()).update();
                    }
                }
            }
        });

    }

    private void ignortAbnormal(RfidVariation variation,List<Long> infoIds){

        List<RfidInfo> rfidInfos = rfidInfoServiceDomain.lambdaQuery()
                .in(RfidInfo::getId, infoIds)
                .list();
        for (RfidInfo rfidInfo : rfidInfos) {
            //判断基站是否连接正常
           if( SocketUtil.isConnect(rfidInfo.getDeviceId())){
               variation.setStatus(VariationStatus.SUCCESS);
               variation.setProcessType(ProcessType.RECOVER);
               variation.setRemark("异动已停止上报，自动处理");
               rfidVariationServiceDomain.updateById(variation);
               rfidInfoServiceDomain.lambdaUpdate()
                       .set(RfidInfo::getStatus, 4)
                       .in(RfidInfo::getId, infoIds).update();

               //上报基站轨迹 迁回
               AssetTrajectoryDto assetTrajectoryDto= AssetTrajectoryDto.builder()
                       .assetId(rfidInfo.getAssetId())
                       .rfidCode(rfidInfo.getRfidCode())
                       .locationId(rfidInfo.getLocationId())
                       .location(rfidInfo.getLocation())
                       .readerId(rfidInfo.getReaderId())
                       .deviceId(rfidInfo.getDeviceId())
                       .trajectorySource(TrajectorySource.RELIEVE)
                       .trajectoryType(TrajectoryType.BACK)
                       .build();
               List<AssetTrajectoryDto> assetTrajectoryDtos = Lists.newArrayList(assetTrajectoryDto);
               AssetTrajectoryEvent assetTrajectoryEvent= new AssetTrajectoryEvent().setAssetTrajectoryDtos(assetTrajectoryDtos);
               applicationEventPublisher.publishEvent(assetTrajectoryEvent);
           }
        }

    }

    /**
     * 基站掉线检测 30秒检测一次
     *
     * @throws Exception
     */
    @ScheduleJob(id = 20003L, cron = "0/30 * * * * ? ", description = "基站掉线检测")
    public void stationBreakdown() throws Exception {
        LocalDateTime now = LocalDateTime.now();
        log.info("-------------------基站掉线检测开始，当前时间: {}-------------------", now);

        List<RfidReader> rfidReaders = rfidReaderServiceDomain.selectJoinList(RfidReader.class, MPJWrappers.lambdaJoin()
                .select(RfidReader::getId, RfidReader::getUpdatedAt, RfidReader::getStatus, RfidReader::getDeviceId, RfidReader::getHeatInterval)
                .eq(RfidReader::getStatus, ReaderStatus.NORMAL));
        
        int offlineCount = 0;
        for (RfidReader rfidReader : rfidReaders) {
            // 基于心跳间隔的2倍时间来判断是否掉线
            int heatInterval = rfidReader.getHeatInterval();
            int timeoutSeconds = heatInterval * 2;
            LocalDateTime deadlineTime = now.minusSeconds(timeoutSeconds);
            if (rfidReader.getUpdatedAt().isBefore(deadlineTime)) {
                offlineCount++;
                log.warn("检测到基站掉线: 设备ID={}, 最后活跃时间={}, 超时{}秒", 
                        rfidReader.getDeviceId(), rfidReader.getUpdatedAt(), timeoutSeconds);
                rfidReader.setStatus(ReaderStatus.BREAKDOWN);
                rfidReaderServiceDomain.updateById(rfidReader);

                //上报基站故障
                RfidReaderFault rfidReaderFault = new RfidReaderFault();
                rfidReaderFault.setReaderId(rfidReader.getId());
                rfidReaderFault.setDeviceId(rfidReader.getDeviceId());
                rfidReaderFault.setStatus(FaultStatus.WAIT_DEAL);
                rfidReaderFault.setDescription("设备断线，心跳上报异常！");
                rfidReaderFaultService.save(rfidReaderFault);
            }
        }
    }

}
