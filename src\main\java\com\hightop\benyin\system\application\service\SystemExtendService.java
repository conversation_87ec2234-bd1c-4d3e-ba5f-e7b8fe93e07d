package com.hightop.benyin.system.application.service;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.github.yulichang.toolkit.MPJWrappers;
import com.hightop.benyin.share.domain.service.SequenceDomainService;
import com.hightop.benyin.system.api.vo.query.DictItemTree;
import com.hightop.benyin.system.domain.service.UserInfoDomainService;
import com.hightop.fario.base.constant.StringConstants;
import com.hightop.fario.base.util.CollectionUtils;
import com.hightop.fario.base.util.StringUtils;
import com.hightop.fario.common.mybatis.DataGrid;
import com.hightop.fario.common.mybatis.util.PageHelper;
import com.hightop.magina.core.custom.entry.TreeEntry;
import com.hightop.magina.core.exception.MaginaException;
import com.hightop.magina.standard.cipher.CipherText;
import com.hightop.magina.standard.code.dictionary.dict.Dict;
import com.hightop.magina.standard.code.dictionary.dict.DictDomainService;
import com.hightop.magina.standard.code.dictionary.item.DictItem;
import com.hightop.magina.standard.code.dictionary.item.DictItemDomainService;
import com.hightop.magina.standard.ums.department.Department;
import com.hightop.magina.standard.ums.department.DepartmentDomainService;
import com.hightop.magina.standard.ums.department.user.DepartmentUser;
import com.hightop.magina.standard.ums.department.user.DepartmentUserDomainService;
import com.hightop.magina.standard.ums.department.user.DepartmentUserPageQuery;
import com.hightop.magina.standard.ums.role.RoleDomainService;
import com.hightop.magina.standard.ums.user.basic.UserBasic;
import com.hightop.magina.standard.ums.user.basic.UserBasicDomainService;
import com.hightop.magina.standard.ums.user.manage.UserManageVo;
import com.hightop.magina.standard.ums.user.privacy.UserPrivacy;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Service
public class SystemExtendService {
    RoleDomainService roleDomainService;
    DepartmentUserDomainService departmentUserDomainService;
    UserBasicDomainService userBasicDomainService;
    UserInfoDomainService userInfoDomainService;
    DepartmentDomainService departmentDomainService;
    SequenceDomainService sequenceDomainService;
    DictDomainService dictDomainService;
    DictItemDomainService dictItemDomainService;

    /**
     * 获取待添加部门的用户列表
     *
     * @param pageQuery
     * @return
     */
    public DataGrid<UserManageVo> toBeAddedUsers(DepartmentUserPageQuery pageQuery) {
        return PageHelper.startPage(pageQuery, p -> this.userBasicDomainService.getBaseMapper().selectJoinList(
                        UserManageVo.class,
                        MPJWrappers.<UserBasic>lambdaJoin()
                                .selectAll(UserBasic.class)
                                .selectAll(UserPrivacy.class)
                                // 严禁列名重复
                                .selectIgnore(UserPrivacy::getId)
                                .leftJoin(DepartmentUser.class, on -> on.eq(DepartmentUser::getUserId, UserBasic::getId))
                                .leftJoin(UserPrivacy.class, UserPrivacy::getId, UserBasic::getId)
                                .isNull(DepartmentUser::getUserId)
                                .eq(UserBasic::getIsAvailable, true)
                                .like(StringUtils.isNotEmpty(p.getName()), UserBasic::getName, p.getName())
                                .orderByDesc(UserBasic::getId)
                )
        );
    }

    public boolean saveDepartment(Department department) {
        if (Objects.isNull(department.getParentId())) {
            department.setParentId(Department.TOP);
        }
        if (department.getParentId() != null) {
            Long count = departmentDomainService.lambdaQuery()
                    .eq(Department::getParentId, department.getParentId())
                    .eq(Department::getName, department.getName())
                    .eq(Department::getIsAvailable, true).count();
            if (count > 0) {
                throw new MaginaException("同层级已存在相同单位名称");
            }
        }
        this.checkDepartment(department);
        department.setId(IdWorker.getId());
        this.setFullIdPath(department);

        return this.departmentDomainService.save(department);
    }

    protected void checkDepartment(Department department) {
        Long codeCount =
                this.departmentDomainService.lambdaQuery()
                        .eq(Department::getCode, department.getCode())
                        .eq(Department::getIsAvailable, true)
                        .ne(Objects.nonNull(department.getId()), Department::getId, department.getId())
                        .count();

        if (codeCount > 0) {
            throw new MaginaException("单位编码已存在");
        }

        Long levelCount =
                this.departmentDomainService.lambdaQuery()
                        .eq(Department::getParentId, department.getParentId())
                        .eq(Department::getName, department.getName())
                        .eq(Department::getIsAvailable, true)
                        .ne(Objects.nonNull(department.getId()), Department::getId, department.getId())
                        .count();

        if (levelCount > 0) {
            throw new MaginaException("同层级已存在相同部门名称");
        }
    }

    protected void setFullIdPath(Department department) {
        // 设置全路径id与编码
        if (department.getParentId() != null) {
            Department parentDepartment = this.departmentDomainService.getById(department.getParentId());
            // 设置全路径id
            department.setFullIdPath(parentDepartment.getFullIdPath() + section(department.getId()));
            String code = sequenceDomainService.nextSequence(parentDepartment.getCode().getValue(), 2);
            department.setCode(new CipherText(code));
        }
    }

    protected String section(Long id) {
        return StringConstants.SLASH + id;
    }


    private List<DictItemTree> listDictItem(String dictCode) {
        Dict dict = this.dictDomainService.getByCode(dictCode);
        if (Objects.isNull(dict)) {
            return new ArrayList<>();
        }
        return this.dictItemDomainService.selectJoinList(
                DictItemTree.class,
                MPJWrappers.<DictItem>lambdaJoin()
                        .selectAll(DictItem.class)
                        .eq(DictItem::getDictId, dict.getId())
                        .eq(DictItem::getIsEnable, true)
                        .orderByAsc(DictItem::getSort)
                        .orderByAsc(DictItem::getValue)
        );
    }


    public List<DictItemTree> getItemSubTree(String dictCode, String dictItemCode) {
        // 以树形结构查询字典项
        List<DictItemTree> items = this.listDictItem(dictCode);
        // 默认构造全树
        Long top = DictItem.ROOT;
        // 若存在子树根节点
        if (Objects.nonNull(dictItemCode)) {
            Optional<DictItemTree> first =
                    items.stream()
                            .filter(it -> Objects.equals(it.getValue(), dictItemCode))
                            .findFirst();
            // 未找到子树根节点
            if (!first.isPresent()) {
                return new ArrayList<>();
            }

            // 设置构造树根节点为子树根节点
            top = first.get().getId();
        }

        return TreeEntry.generate(items, DictItemTree.COMPARATOR, top);
    }

    public DictItem getByDictCodeAndLabel(String code, String label) {
        return
                dictItemDomainService
                        .selectJoinOne(
                                DictItem.class,
                                MPJWrappers.<DictItem>lambdaJoin()
                                        .selectAll(DictItem.class)
                                        .innerJoin(Dict.class, on -> on.eq(Dict::getId, DictItem::getDictId).eq(Dict::getCode, code))
                                        .eq(DictItem::getLabel, label)
                        );
    }

    public DictItem getByDictCodeAndLabelParent(String code, String label, Long parentId) {
        List<DictItem> dictItems = dictItemDomainService.selectJoinList(
                DictItem.class,
                MPJWrappers.<DictItem>lambdaJoin()
                        .selectAll(DictItem.class)
                        .innerJoin(Dict.class, on -> on.eq(Dict::getId, DictItem::getDictId).eq(Dict::getCode, code))
                        .eq(DictItem::getLabel, label)
        );
        if (CollectionUtils.isNotEmpty(dictItems)) {
            if (dictItems.size() == 1) {
                return dictItems.get(0);
            }else{
               return dictItems.stream().filter(it -> Objects.equals(it.getParentId(), parentId)).findFirst().orElse(null);
            }
        }
        return null;
    }

}
