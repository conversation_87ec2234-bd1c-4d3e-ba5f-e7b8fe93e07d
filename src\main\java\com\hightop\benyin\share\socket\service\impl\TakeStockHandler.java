package com.hightop.benyin.share.socket.service.impl;

import com.github.yulichang.toolkit.MPJWrappers;
import com.github.yulichang.toolkit.SpringContentUtils;
import com.google.common.base.Joiner;
import com.hightop.benyin.rfid.application.service.RfidAssetTakeService;
import com.hightop.benyin.rfid.application.service.RfidInfoService;
import com.hightop.benyin.rfid.application.service.RfidReaderService;
import com.hightop.benyin.rfid.domain.service.RfidAssetServiceDomain;
import com.hightop.benyin.rfid.domain.service.RfidReaderServiceDomain;
import com.hightop.benyin.rfid.infrastructure.entity.RfidAsset;
import com.hightop.benyin.rfid.infrastructure.entity.RfidReader;
import com.hightop.benyin.share.domain.event.ApiLogEvent;
import com.hightop.benyin.share.infrastructure.enums.MessageType;
import com.hightop.benyin.share.infrastructure.util.DictUtil;
import com.hightop.benyin.share.infrastructure.util.RedisLockUtils;
import com.hightop.benyin.share.socket.enums.CommandType;
import com.hightop.benyin.share.socket.service.CommandHandler;
import com.hightop.benyin.share.socket.util.MsgUtil;
import com.hightop.benyin.share.socket.util.ReaderControlTool;
import com.hightop.benyin.system.infrastructure.entity.Location;
import com.hightop.fario.base.util.CollectionUtils;
import com.hightop.fario.base.util.StringUtils;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.LinkedHashSet;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 一键盘点-命令处理器
 */
@Service
@Slf4j
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class TakeStockHandler implements CommandHandler {

    RfidReaderServiceDomain rfidReaderServiceDomain;
    RfidReaderService rfidReaderService;
    RfidAssetServiceDomain rfidAssetServiceDomain;
    RedisTemplate<String, String> redisTemplate;
    RfidInfoService rfidInfoService;
    RfidAssetTakeService rfidAssetTakeService;

    @Override
    public void handle(String clientName, String deviceId, Integer length, String params) {
        log.info("扫描数据上报, 设备: {}, params: {}", deviceId, params);
        List<String> rfidList = MsgUtil.splitString(params, MsgUtil.RFID_STR_LENGTH);
        if (StringUtils.isEmpty(deviceId)) {
            return;
        }
        if (params.equals(MsgUtil.RESULT_SUCCESS)) {
            log.info("扫描数据上报, 设备: {}, params: {} 长度小于24被忽略", deviceId, params);
            return;
        }
        if (params.equals(MsgUtil.scan_end)) {
            log.info("扫描数据上报, 设备: {}, params: {} 扫描结束", deviceId, params);
            return;
        }
        String scanCodeKey = DictUtil.SCAN_CACHE + DictUtil.SCAN_CODE + deviceId;
        String scanTypeKey = DictUtil.SCAN_CACHE + DictUtil.SCAN_TYPE + deviceId;

        String waitMsgType = redisTemplate.opsForValue().get(scanTypeKey);
        String businessCode = redisTemplate.opsForValue().get(scanCodeKey);
        log.info("扫描数据上报, 单号: {}, 类型: {}", businessCode, waitMsgType);
        RfidReader rfidReader = rfidReaderServiceDomain.selectJoinOne(RfidReader.class, MPJWrappers.<RfidReader>lambdaJoin()
                .selectAll(RfidReader.class)
                .selectAs(Location::getFullName, RfidReader::getLocation)
                .leftJoin(Location.class, Location::getId, RfidReader::getLocationId)
                .eq(RfidReader::getDeviceId, deviceId)
        );
        if (Objects.isNull(rfidReader)) {
            ApplicationContext applicationContext = SpringContentUtils.getApplicationContext();
            ApiLogEvent apiLogEvent = new ApiLogEvent(this, MessageType.RECEIVE, CommandType.TAKE_STOCK,
                    deviceId, Joiner.on(",").join(rfidList), "设备" + deviceId + "未找到基站！", false);
            applicationContext.publishEvent(apiLogEvent);
            return;
        }
        //基站资产绑定检查
        if (StringUtils.isEmpty(waitMsgType)) {
            if (CollectionUtils.isEmpty(rfidList)) {
                return;
            }
            if (Objects.isNull(rfidReader.getLocationId())) {
                ApplicationContext applicationContext = SpringContentUtils.getApplicationContext();
                ApiLogEvent apiLogEvent = new ApiLogEvent(this, MessageType.RECEIVE, CommandType.TAKE_STOCK,
                        deviceId, Joiner.on(",").join(rfidList), "基站未配置对应位置信息！", false);
                applicationContext.publishEvent(apiLogEvent);
                return;
            }
            String resultKey = DictUtil.BIND_CACHE + DictUtil.LIST + deviceId;
            if (redisTemplate.hasKey(resultKey)) {
                redisTemplate.delete(resultKey);
            }
            redisTemplate.opsForList().leftPushAll(resultKey, rfidList);
            redisTemplate.expire(resultKey, 10, TimeUnit.MINUTES);
            log.info("{}绑定检查，已绑定数量{}，", clientName, rfidList.size());
            long count = rfidAssetServiceDomain.lambdaQuery()
                    .eq(RfidAsset::getReaderId, rfidReader.getId())
                    .isNotNull(RfidAsset::getRfidCode)
                    .count();
            if (count != rfidList.size()) {
                log.info("{}绑定结果不匹配，已绑定{}个,应绑{}个", clientName, rfidList.size(), count);
                rfidReaderService.sendAsset(rfidReader.getId());
            }
        } else {
            if (CollectionUtils.isEmpty(rfidList)) {
                endScan(rfidReader, waitMsgType, businessCode, deviceId, false);
                return;
            }
            //表示搜索扫描
            if (DictUtil.LOOK_SCAN.equals(waitMsgType)) {
                log.info("搜索数据扫描接收成功，{}", clientName);
                String redisKey = DictUtil.LOOK_CACHE + DictUtil.LIST + businessCode;
                LinkedHashSet<String> waitSearchList = (LinkedHashSet<String>) redisTemplate.opsForSet().members(redisKey);
                if (CollectionUtils.isEmpty(waitSearchList)) {
                    log.info("---本次搜索标签已全部找到，忽略上报！");
                    endScan(rfidReader, waitMsgType, businessCode, deviceId, true);
                    // 释放锁
                    String lookReaderRedisKey = DictUtil.LOOK_CACHE + DictUtil.READER + businessCode;
                    if (redisTemplate.hasKey(lookReaderRedisKey)) {
                        LinkedHashSet<String> searchReaderList = (LinkedHashSet<String>) redisTemplate.opsForSet().members(lookReaderRedisKey);
                        if (CollectionUtils.isNotEmpty(searchReaderList)) {
                            //全部结束
                            for (String item : searchReaderList) {
                                String key = DictUtil.SCAN_CACHE + DictUtil.STATUS + businessCode + ":" + item;
                                redisTemplate.opsForValue().set(key, DictUtil.RECEIVED, 30, TimeUnit.MINUTES);
                                String lockKey = DictUtil.LOCK + item;
                                RedisLockUtils.unlock(lockKey, businessCode);
                            }
                        }
                    }
                    return;
                }
                // 保存上报数据
                List<String> targetRfidList = rfidInfoService.getSearchResult(rfidList, deviceId, businessCode);
                if (CollectionUtils.isEmpty(targetRfidList)) {
                    log.info("未找到搜索结果，{}", clientName);
                    endScan(rfidReader, waitMsgType, businessCode, deviceId, false);
                    return;
                }
                //保存搜索结果
                rfidInfoService.reportHandler(targetRfidList, clientName, DictUtil.LOOK_SCAN, DictUtil.LOCATION, businessCode);
                log.info("搜索数据接收成功，{}", clientName);
            } else {
                //盘点或绑定扫描 去重保存
                String resultKey = DictUtil.SCAN_CACHE + DictUtil.RESULT + waitMsgType + ":" + businessCode + ":" + deviceId;
                List<String> rfidCacheList = redisTemplate.opsForList().range(resultKey, 0, -1);
                if (CollectionUtils.isEmpty(rfidCacheList)) {
                    rfidCacheList = rfidList;
                } else {
                    rfidCacheList.addAll(rfidList);
                }
                rfidCacheList = rfidCacheList.stream().distinct().collect(Collectors.toList());
                redisTemplate.opsForList().leftPushAll(resultKey, rfidCacheList);
                redisTemplate.expire(resultKey, 30, TimeUnit.MINUTES);
                log.info("{},{}扫描数据上报完成，共{}条数据", businessCode, clientName, rfidCacheList.size());
            }
            endScan(rfidReader, waitMsgType, businessCode, deviceId, false);
        }
    }

    /**
     * 结束扫描
     *
     * @param rfidReader
     * @param waitMsgType
     * @param businessCode
     * @param deviceId
     * @param isCompleted  强制结束
     */
    private void endScan(RfidReader rfidReader, String waitMsgType, String businessCode, String deviceId, Boolean isCompleted) {
        String key = DictUtil.SCAN_CACHE + DictUtil.STATUS + businessCode + ":" + deviceId;
        redisTemplate.opsForValue().set(key, DictUtil.RECEIVED, 30, TimeUnit.MINUTES);
        //释放锁
        String lockKey = DictUtil.LOCK + deviceId;
        RedisLockUtils.unlock(lockKey, businessCode);

    }
}
