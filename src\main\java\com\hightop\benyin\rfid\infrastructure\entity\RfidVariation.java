package com.hightop.benyin.rfid.infrastructure.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.*;
import com.hightop.benyin.rfid.infrastructure.enums.AbnormalType;
import com.hightop.benyin.rfid.infrastructure.enums.ProcessType;
import com.hightop.benyin.rfid.infrastructure.enums.VariationStatus;
import com.hightop.benyin.share.infrastructure.util.DictUtil;
import com.hightop.magina.standard.code.dictionary.bind.DictItemBind;
import com.hightop.magina.standard.code.dictionary.bind.DictItemEntry;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.time.LocalDateTime;

/**
 * rfid异动信息
 *
 * <AUTHOR>
 * @date 2024-10-12 16:36:01
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@TableName(value = "b_rfid_variation", autoResultMap = true)
@ApiModel
public class RfidVariation {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @ApiModelProperty("id")
    Long id;

    @TableField("code")
    @ApiModelProperty("异动编码")
    String code;

    @TableField("info_id")
    @ApiModelProperty("rfid编码")
    Long infoId;

    @TableField("abnormal_analyze")
    @ApiModelProperty("异常分析")
    String abnormalAnalyze;

    @TableField("abnormal_type")
    @Excel(name = "异动类型", width = 30, orderNum = "5", enumExportField = "name")
    AbnormalType abnormalType;

    @TableField("process_type")
    @ApiModelProperty("处理类型")
    @Excel(name = "处理类型", width = 30, orderNum = "6", enumExportField = "name")
    ProcessType processType;

    @TableField("scan_location_id")
    @ApiModelProperty("出现位置")
    Long scanLocationId;

    @TableField("scan_location")
    @ApiModelProperty("出现位置")
    String scanLocation;

    @TableField("scan_reader_id")
    @ApiModelProperty("出现基站")
    Long scanReaderId;

    @TableField("scan_reader")
    @ApiModelProperty("出现基站")
    String scanReader;

    @TableField("status")
    @ApiModelProperty("状态")
    VariationStatus status;

    @TableField("expire_time")
    @ApiModelProperty("过期时间")
    LocalDateTime expireTime;

    @TableField("last_info_id")
    @ApiModelProperty("最后上报id")
    Long lastInfoId;

    @TableField("change_manager_id")
    @ApiModelProperty("变更负责人")
    Long changeManagerId;

    @TableField("change_manager_dept_id")
    @ApiModelProperty("变更负责部门")
    Long changeManagerDeptId;

    @TableField("change_apply_id")
    @ApiModelProperty("变更保管人")
    Long changeApplyId;

    @TableField("change_apply_dept_id")
    @ApiModelProperty("变更保管部门")
    Long changeApplyDeptId;

    @TableField("change_location_id")
    @ApiModelProperty("变更位置")
    Long changeLocationId;

    @TableField("change_location")
    @ApiModelProperty("变更位置")
    String changeLocation;

    @TableField("change_reader_id")
    @ApiModelProperty("变更基站")
    Long changeReaderId;

    @TableField("change_reader")
    @ApiModelProperty("变更基站")
    String changeReader;

    @TableField("remark")
    @ApiModelProperty("备注")
    String remark;

    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @ApiModelProperty("创建时间")
    @Excel(name = "异动时间", width = 30, orderNum = "12", format = "yyyy-MM-dd HH:mm:ss")

    LocalDateTime createdAt;

    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty("更新时间")
    LocalDateTime updatedAt;

    @TableField(value = "updated_by")
    @ApiModelProperty("更新人")
    Long updatedBy;

    @TableField("deleted")
    @ApiModelProperty("是否删除  0未删除  1删除")
    @TableLogic
    Integer deleted;

    @TableField(exist = false)
    @ApiModelProperty("上报类型")
    @DictItemBind(DictUtil.DICT_SCAN_TYPE)
    DictItemEntry type;

    @TableField(exist = false)
    @ApiModelProperty("完成状态")
    @Excel(name = "完成状态", width = 30, orderNum = "6")
    String statusName;


    @TableField(exist = false)
    @ApiModelProperty("基站编码")
    Long readerId;

    @TableField(exist = false)
    @ApiModelProperty("基站编码")
    @Excel(name = "上报基站", width = 30, orderNum = "7")
    String deviceId;

    @TableField(exist = false)
    @ApiModelProperty("位置编码")
    Long locationId;

    @TableField(exist = false)
    @ApiModelProperty("位置")
    @Excel(name = "检测位置", width = 30, orderNum = "8")
    String location;

    @TableField(exist = false)
    @ApiModelProperty("绑定位置")
    String assetLocation;

    @TableField(exist = false)
    @ApiModelProperty("所属单位")
    Long departmentId;

    @TableField(exist = false)
    @ApiModelProperty("所属单位")
    @Excel(name = "所在部门", width = 30, orderNum = "9")
    String departmentName;

    @TableField(exist = false)
    @ApiModelProperty("资产id")
    Long assetId;

    @TableField(exist = false)
    @ApiModelProperty("负责人")
    Long managerId;

    @TableField(exist = false)
    @ApiModelProperty("负责人")
    @Excel(name = "责任人", width = 30, orderNum = "10")

    String managerName;

    @TableField(exist = false)
    @ApiModelProperty("资产编码")
    @Excel(name = "资产编码", width = 30, orderNum = "1")
    String assetCode;


    @TableField(exist = false)
    @ApiModelProperty("资产小类")
    String assetType;

    @TableField(exist = false)
    @ApiModelProperty("资产类型")
    String assetTypeName;

    @TableField(value = "rfid_code")
    @ApiModelProperty("rfid编码")
    @Excel(name = "RFID编号", width = 30, orderNum = "0")
    String rfidCode;

    @TableField(exist = false)
    @ApiModelProperty("原rfid编码")
    @Excel(name = "原rfid编码", width = 30, orderNum = "4")
    String oriRfidCode;

    @TableField(exist = false)
    @ApiModelProperty("资产名称")
    @Excel(name = "资产名称", width = 30, orderNum = "2")
    String assetName;

    @TableField(exist = false)
    @ApiModelProperty("领用人")
    @Excel(name = "保管人", width = 30, orderNum = "11")

    String applyName;

    @TableField(exist = false)
    @ApiModelProperty("领用人")
    Long applyId;

    @TableField(exist = false)
    @ApiModelProperty("型号")
    @Excel(name = "规格型号", width = 30, orderNum = "3")

    String model;

    @TableField(exist = false)
    @ApiModelProperty("价格")
    Long price;

    public String getStatusName() {
        return status != null ? status.getName() : null;
    }

}
