package com.hightop.benyin.rfid.infrastructure.mapper;

import com.github.yulichang.base.MPJBaseMapper;
import com.hightop.benyin.rfid.application.vo.po.AssetTotalVo;
import com.hightop.benyin.rfid.application.vo.query.RfidAssetQuery;
import com.hightop.benyin.rfid.infrastructure.entity.RfidAsset;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 资产管理mapper
 *
 * <AUTHOR>
 * @date 2024-10-12 16:36:01
 */
public interface RfidAssetMapper extends MPJBaseMapper<RfidAsset> {

    /**
     * 根据条件查询资产列表
     *
     * @param rfidAssetQuery
     * @return
     */
    List<RfidAsset> getAssetList(@Param("qo") RfidAssetQuery rfidAssetQuery);

    /**
     * 根据条件查询资产总数
     *
     * @param rfidAssetQuery
     * @return
     */
    AssetTotalVo getAssetTotal(@Param("qo") RfidAssetQuery rfidAssetQuery);

    /**
     * 根据条件查询未绑定标签资产总数
     *
     * @param rfidAssetQuery
     * @return
     */
    Long getNotBindTotal(@Param("qo") RfidAssetQuery rfidAssetQuery);

    /**
     * 根据条件查询未绑定基站资产总数
     *
     * @param rfidAssetQuery
     * @return
     */
    Long getNotReaderTotal(@Param("qo") RfidAssetQuery rfidAssetQuery);

    @Delete("TRUNCATE TABLE b_rfid_asset")
    void clearAsset();
}
