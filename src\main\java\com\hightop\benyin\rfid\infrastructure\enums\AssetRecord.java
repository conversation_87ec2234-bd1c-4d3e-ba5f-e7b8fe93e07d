package com.hightop.benyin.rfid.infrastructure.enums;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.hightop.magina.core.custom.entry.EnumEntry;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

/**
 * 审核记录枚举
 *
 * <AUTHOR>
 * @date 2023/12/20 17:22
 */
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Getter
public enum AssetRecord implements EnumEntry<Integer> {

    /**
     * 驳回申请
     */
    REJECT(0, InoutTypeEnums.IN, "驳回申请"),
    /**
     * 审核通过 32
     */
    PASS(1, InoutTypeEnums.IN, "审核通过"),
    /**
     * 提交资产登记 16
     */
    ADD(2, InoutTypeEnums.IN, "提交资产登记");

    /**
     * 命令
     */
    Integer code;

    /**
     * 操作类型 0-上报 1-下发
     */
    @JsonIgnore
    InoutTypeEnums type;

    /**
     * 操作说明
     */
    String name;


}
