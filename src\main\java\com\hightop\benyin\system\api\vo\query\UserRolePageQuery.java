package com.hightop.benyin.system.api.vo.query;

import com.hightop.fario.common.mybatis.PageQuery;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldDefaults;

@Data
@EqualsAndHashCode(callSuper = true)
@FieldDefaults(level = AccessLevel.PRIVATE)
public class UserRolePageQuery extends PageQuery {
    @ApiModelProperty("姓名")
    String name;

    @ApiModelProperty("账号")
    String code;
}
