<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hightop.benyin.rfid.infrastructure.mapper.RfidAssetMapper">

    <resultMap id="assetResultMap" type="com.hightop.benyin.rfid.infrastructure.entity.RfidAsset" autoMapping="true">
        <result column="financial_classify" property="financialClassify"  typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler" />
    </resultMap>

    <select id="getAssetList" resultMap="assetResultMap">
        SELECT t.id,
        t.rfid_code,
        t.code,
        t.name,
        t.model,
        t.sign_code,
        t.cheer_code,
        t.ori_rfid_code,
        t.asset_type,
        t.asset_middle_type,
        t.asset_purpose,
        t.asset_parent_type,
        t.card_type,
        t.acquire_mode,
        t.acquire_date,
        t.enter_date,
        t.price,
        t.parent_code,
        t.manager_dept_id,
        t.manager_id,
        t.manager_name,
        t.type,
        t.reader_id,
        t.department_id,
        t.apply_name,
        t.apply_at,
        t.location_id,
        t.storage_location,
        t.apply_id,
        t.use_state,
        t.asset_status,
        t.status,
        t.init_salvage,
        t.now_salvage,
        t.financial_classify,
        t.has_tag,
        t.is_scan,
        t.is_report,
        t.is_take,
        t.is_flow,
        t.is_take_statis,
        t.is_statis,
        t.sync_state,
        t.in_status,
        t.data_source,
        t.created_at,
        t.created_by,
        t.create_dept,
        t.breakage_at,
        t.updated_at,
        t.updated_by,
        t.deleted,
        t3.name AS departmentName,
        t3.code AS departmentCode,
        t6.code AS managerDeptCode,
        t6.name AS managerDeptName,
        t2.full_name AS location,
        t4.name AS createdByName,
        t4.code AS createdByCode,
        t.id AS assetId,
        t5.code AS createDeptCode,
        t7.code AS managerCode,
        t8.code AS applyCode,
        t9.name AS assetTypeName,
        t10.name AS assetMiddleTypeName,
        t11.name AS assetParentTypeName,
        t12.name AS categoryType,
        t5.name AS createDeptName,t1.code AS readerCode,t1.device_id AS readerDeviceId,t2.code AS locationCode
        FROM b_rfid_asset t
        LEFT JOIN b_rfid_reader t1 ON (t1.id = t.reader_id)
        LEFT JOIN st_location t2 ON (t2.id = t.location_id)
        LEFT JOIN st_department t3 ON (t3.id = t.department_id)
        LEFT JOIN st_user_basic t4 ON (t4.id = t.created_by)
        LEFT JOIN st_department t5 ON (t5.id = t.create_dept)
        LEFT JOIN st_department t6 ON (t6.id = t.manager_dept_id)
        LEFT JOIN st_user_basic t7 ON (t7.id = t.manager_id)
        LEFT JOIN st_user_basic t8 ON (t8.id = t.apply_id)
        LEFT JOIN b_asset_type t9 ON (t9.code = t.asset_type)
        LEFT JOIN b_asset_type t10 ON (t10.id = t9.parent_id)
        LEFT JOIN b_asset_type t11 ON (t11.id = t10.parent_id)
        LEFT JOIN b_asset_type t12 ON (t12.id = t11.parent_id)

        WHERE t.deleted = false
        <include refid="SqlWhere"></include>

        ORDER BY t.code DESC
    </select>

    <select id="getAssetTotal" resultType="com.hightop.benyin.rfid.application.vo.po.AssetTotalVo">
        SELECT count(1) as number,sum(t.price) amount
        FROM b_rfid_asset t
        LEFT JOIN b_rfid_reader t1 ON (t1.id = t.reader_id)
        LEFT JOIN st_location t2 ON (t2.id = t.location_id)
        LEFT JOIN st_department t3 ON (t3.id = t.department_id)
        LEFT JOIN st_user_basic t4 ON (t4.id = t.created_by)
        LEFT JOIN st_department t5 ON (t5.id = t.create_dept)
        WHERE t.deleted = false
        <include refid="SqlWhere"></include>
        ORDER BY t.created_at DESC
    </select>

    <select id="getNotBindTotal" resultType="java.lang.Long">
        SELECT count(1) as number,sum(t.price) amount
        FROM b_rfid_asset t
        LEFT JOIN b_rfid_reader t1 ON (t1.id = t.reader_id)
        LEFT JOIN st_location t2 ON (t2.id = t.location_id)
        LEFT JOIN st_department t3 ON (t3.id = t.department_id)
        LEFT JOIN st_user_basic t4 ON (t4.id = t.created_by)
        LEFT JOIN st_department t5 ON (t5.id = t.create_dept)
        WHERE t.deleted = false and t.has_tag = 1 and (t.rfid_code is null or t.rfid_code = '' )
        <include refid="SqlWhere"></include>
    </select>

    <select id="getNotReaderTotal" resultType="java.lang.Long">
        SELECT count(1)
        FROM b_rfid_asset t
        LEFT JOIN b_rfid_reader t1 ON (t1.id = t.reader_id)
        LEFT JOIN st_location t2 ON (t2.id = t.location_id)
        LEFT JOIN st_department t3 ON (t3.id = t.department_id)
        LEFT JOIN st_user_basic t4 ON (t4.id = t.created_by)
        LEFT JOIN st_department t5 ON (t5.id = t.create_dept)
        WHERE t.deleted = false and t.has_tag = 1 and t.reader_id is null
        <include refid="SqlWhere"></include>
    </select>



<sql id="SqlWhere">

    <if test="null!=qo.bindTag">
        <choose>
            <when test="qo.bindTag==true">
                and (t.rfid_code is not null and  t.rfid_code != '')
            </when>
            <otherwise>
                and( t.rfid_code is null or t.rfid_code = '' )
            </otherwise>
        </choose>
    </if>


    <if test="null!=qo.bindReader">
        <choose>
            <when test="qo.bindReader==true">
                and t.reader_id is not null
            </when>
            <otherwise>
                and t.reader_id is null
            </otherwise>
        </choose>
    </if>

    <if test="qo.parentCode!= null and qo.parentCode!= ''">
        and t.parent_code = #{qo.parentCode}
    </if>
    <if test="qo.userId!= null">
        and (t.manager_id = #{qo.userId} or t.apply_id = #{qo.userId} )
    </if>
    <if test="qo.inStatus!= null">
        and t.in_status =#{qo.inStatus}
    </if>
    <if test="null!=qo.dataSource and !qo.dataSource.isEmpty()">
        and t.data_source in
        <foreach collection="qo.dataSource" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </if>

    <if test="qo.departmentId!= null">
        and (t.manager_dept_id = #{qo.departmentId} or t.department_id = #{qo.departmentId} )
    </if>
    <if test="qo.createdByName!= null and qo.createdByName!= ''">
        and t4.name like concat ('%',#{qo.createdByName},'%')
    </if>
    <if test="qo.readerCode!= null and qo.readerCode!= ''">
        and t1.code like concat ('%',#{qo.readerCode},'%')
    </if>

    <if test="qo.applyName!= null and qo.applyName!= ''">
        and t.apply_name like concat ('%',#{qo.applyName},'%')
    </if>
    <if test="qo.model!= null and qo.model!= ''">
        and t.model like concat ('%',#{qo.model},'%')
    </if>

    <if test="qo.name!= null and qo.name!= ''">
        and t.name like concat ('%',#{qo.name},'%')
    </if>

    <if test="qo.departmentName!= null and qo.departmentName!= ''">
        and t3.name like concat ('%',#{qo.departmentName},'%')
    </if>

    <if test="qo.code!= null and qo.code!= ''">
        and (t.code like concat ('%',#{qo.code},'%') or t.parent_code like concat ('%',#{qo.code},'%'))
    </if>

    <if test="qo.signCode!= null and qo.signCode!= ''">
        and t.sign_code like concat ('%',#{qo.signCode},'%')
    </if>

    <if test="qo.locationId!= null">
        and t.location_id =#{qo.locationId}
    </if>

    <if test="qo.readerId!= null">
        and t.reader_id =#{qo.readerId}
    </if>
    <if test="qo.status!= null">
        <choose>
            <when test="qo.status.code=='STASH' ">
                and t.status =#{qo.status} and t.created_by =#{qo.createdUserId}
            </when>
            <otherwise>
                and t.status =#{qo.status} and t.in_status = 1
            </otherwise>
        </choose>

    </if>

    <if test="null!=qo.isTake and !qo.isTake.isEmpty()">
        and t.is_take in
        <foreach collection="qo.isTake" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </if>

    <if test="null!=qo.isScan and !qo.isScan.isEmpty()">
        and t.is_scan in
        <foreach collection="qo.isScan" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </if>

    <if test="null!=qo.hasTag and !qo.hasTag.isEmpty()">
        and t.has_tag in
        <foreach collection="qo.hasTag" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </if>

    <if test="null!=qo.isStatis and !qo.isStatis.isEmpty()">
        and t.is_statis in
        <foreach collection="qo.isStatis" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </if>
    <if test="null!=qo.isTakeStatis and !qo.isTakeStatis.isEmpty()">
        and t.is_take_statis in
        <foreach collection="qo.isTakeStatis" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </if>
    <if test="null!=qo.isReport and !qo.isReport.isEmpty()">
        and t.is_report in
        <foreach collection="qo.isReport" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </if>

    <if test="qo.isFlow!= null">
        and t.is_flow =#{qo.isFlow}
    </if>

    <if test="qo.type!= null">
        and t.type =#{qo.type}
    </if>

    <if test="qo.id!= null">
        and t.id =#{qo.id}
    </if>

    <if test="qo.inOutType!= null and qo.inOutType == 2">
        and t.apply_id is not null
    </if>

    <if test="qo.rfidCode!= null and qo.rfidCode!= ''">
        and t.rfid_code like concat ('%',#{qo.rfidCode},'%')
    </if>

    <if test="qo.oriRfidCode!= null and qo.oriRfidCode!= ''">
        and t.ori_rfid_code like concat ('%',#{qo.oriRfidCode},'%')
    </if>

    <if test="qo.neStatus!= null and qo.neStatus!= ''">
        and t.status != #{qo.neStatus}
    </if>

    <if test="null!=qo.rfidCodes and !qo.rfidCodes.isEmpty()">
        and t.rfid_code in
        <foreach collection="qo.rfidCodes" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </if>
    <if test="null!=qo.useState and !qo.useState.isEmpty()">
        and t.use_state in
        <foreach collection="qo.useState" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </if>
    <if test="null!=qo.assetType and !qo.assetType.isEmpty()">
        and t.asset_type in
        <foreach collection="qo.assetType" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </if>

    <if test="null!=qo.departmentIds and !qo.departmentIds.isEmpty()">
        and t.department_id in
        <foreach collection="qo.departmentIds" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </if>

    <if test="null!=qo.managerDeptIds and !qo.managerDeptIds.isEmpty()">
        and t.manager_dept_id in
        <foreach collection="qo.managerDeptIds" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </if>

    <if test="null!=qo.createDepIds and !qo.createDepIds.isEmpty()">
        and t.create_dept in
        <foreach collection="qo.createDepIds" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </if>

    <if test="null!=qo.managerIds and !qo.managerIds.isEmpty()">
        and t.manager_id in
        <foreach collection="qo.managerIds" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </if>

    <if test="null!=qo.applyIds and !qo.applyIds.isEmpty()">
        and t.apply_id in
        <foreach collection="qo.applyIds" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </if>

    <if test="null!=qo.createdByIds and !qo.createdByIds.isEmpty()">
        and t.created_by in
        <foreach collection="qo.createdByIds" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </if>
    <if test="null!=qo.acquireMode and !qo.acquireMode.isEmpty()">
        and t.acquire_mode in
        <foreach collection="qo.acquireMode" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </if>

    <if test="null!=qo.locations and !qo.locations.isEmpty()">
        and t.location_id in
        <foreach collection="qo.locations" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </if>

    <if test="null!=qo.assetPurpose and !qo.assetPurpose.isEmpty()">
        and t.asset_purpose in
        <foreach collection="qo.assetPurpose" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </if>

    <if test="null!=qo.financialClassify and !qo.financialClassify.isEmpty()">
        AND
        <foreach collection="qo.financialClassify" item="id" separator=" OR " open="(" close=")">
            JSON_CONTAINS(t.financial_classify,cast(#{id} as char ))
        </foreach>
    </if>

    <if test="null != qo.startDate and '' != qo.startDate ">
        and t.created_at &gt;= concat(#{qo.startDate},' 00:00:00')
    </if>
    <if test="null != qo.endDate and '' != qo.endDate ">
        and t.created_at &lt;= concat(#{qo.endDate},' 23:59:59')
    </if>

    <if test="null != qo.startBreakageDate and '' != qo.startBreakageDate ">
        and t.breakage_at &gt;= #{qo.startBreakageDate}
    </if>
    <if test="null != qo.endBreakageDate and '' != qo.endBreakageDate ">
        and t.breakage_at &lt;= #{qo.endBreakageDate}
    </if>
    <if test="null != qo.startEnterDate and '' != qo.startEnterDate ">
        and t.enter_date &gt;= #{qo.startEnterDate}
    </if>
    <if test="null != qo.endEnterDate and '' != qo.endEnterDate ">
        and t.enter_date &lt;= #{qo.endEnterDate}
    </if>

    <if test="null != qo.startAcquireDate and '' != qo.startAcquireDate ">
        and t.acquire_date &gt;= #{qo.startAcquireDate}
    </if>
    <if test="null != qo.endAcquireDate and '' != qo.endAcquireDate ">
        and t.acquire_date &lt;= #{qo.endAcquireDate}
    </if>

    <if test="null != qo.startPrice and '' != qo.startPrice ">
        and t.price &gt;= #{qo.startPrice}
    </if>
    <if test="null != qo.endPrice and '' != qo.endPrice ">
        and t.price &lt;= #{qo.endPrice}
    </if>

    <if test="null != qo.startPrice and '' != qo.startPrice ">
        and t.price &gt;= #{qo.startPrice}
    </if>
    <if test="null != qo.endPrice and '' != qo.endPrice ">
        and t.price &lt;= #{qo.endPrice}
    </if>

    <if test="null != qo.startSalvagePrice and '' != qo.startSalvagePrice ">
        and t.now_salvage &gt;= #{qo.startSalvagePrice}
    </if>
    <if test="null != qo.endSalvagePrice and '' != qo.endSalvagePrice ">
        and t.now_salvage &lt;= #{qo.endSalvagePrice}
    </if>
</sql>
</mapper>
