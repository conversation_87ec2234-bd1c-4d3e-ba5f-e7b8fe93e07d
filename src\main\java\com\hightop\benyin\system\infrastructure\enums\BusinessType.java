package com.hightop.benyin.system.infrastructure.enums;

import com.hightop.magina.core.custom.entry.EnumEntry;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

/**
 * 业务类型
 * <AUTHOR>
 * @date 2024/5/15 13:42
 */
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Getter
public enum BusinessType implements EnumEntry<String> {
    /**
     * 人员信息
     */
    USER("人员信息"),
    /**
     * 部门
     */
    DEPT("单位信息"),
    /**
     * 单位位置
     */
    LOCATION("位置数据"),
    /**
     * 基站数据
     */
    READER("基站数据"),
    /**
     * 资产数据
     */
    ASSET("资产数据");

    /**
     * 状态名称
     */
    String name;

    @Override
    public String getCode() {
        // 以枚举名称作为标识
        return super.name();
    }

    @Override
    public String getName() {
        return this.name;
    }

}
