package com.hightop.benyin.rfid.infrastructure.mapper;

import com.github.yulichang.base.MPJBaseMapper;
import com.hightop.benyin.rfid.infrastructure.entity.RfidAssetTakeDetail;
import org.apache.ibatis.annotations.Delete;

/**
 * 资产盘点明细管理mapper
 *
 * <AUTHOR>
 * @date 2024-09-15 14:53:13
 */
public interface RfidAssetTakeDetailMapper extends MPJBaseMapper<RfidAssetTakeDetail> {

    @Delete("TRUNCATE TABLE b_rfid_asset_take_detail")
    void clear();
}
