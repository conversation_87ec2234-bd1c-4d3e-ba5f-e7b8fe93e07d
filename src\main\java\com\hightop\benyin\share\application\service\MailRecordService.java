package com.hightop.benyin.share.application.service;

import com.github.yulichang.toolkit.MPJWrappers;
import com.google.common.collect.Lists;
import com.hightop.benyin.rfid.domain.event.AssetChangeEvent;
import com.hightop.benyin.share.application.dto.MailSendDto;
import com.hightop.benyin.share.application.query.MailRecordQuery;
import com.hightop.benyin.share.domain.service.MailRecordServiceDomain;
import com.hightop.benyin.share.infrastructure.entity.MailRecord;
import com.hightop.benyin.share.infrastructure.util.DictUtil;
import com.hightop.benyin.system.domain.service.UserInfoDomainService;
import com.hightop.benyin.system.infrastructure.entity.UserInfo;
import com.hightop.benyin.system.infrastructure.entity.UserPrivacyInfo;
import com.hightop.fario.base.util.CollectionUtils;
import com.hightop.fario.base.util.StringUtils;
import com.hightop.fario.common.core.util.ExecutorUtils;
import com.hightop.fario.common.mybatis.DataGrid;
import com.hightop.fario.common.mybatis.PageQuery;
import com.hightop.fario.common.mybatis.util.PageHelper;
import com.hightop.magina.casual.session.ApplicationSessions;
import com.hightop.magina.core.exception.MaginaException;
import com.hightop.magina.standard.ums.user.bind.UserEntry;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.stereotype.Service;

import java.util.List;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Service
public class MailRecordService {

    JavaMailSender javaMailSender;
    MailRecordServiceDomain mailRecordServiceDomain;
    UserInfoDomainService userDomainService;

    /**
     * Rfid分页查询
     *
     * @param pageQuery {@link PageQuery}
     * @return {@link DataGrid}
     */
    public DataGrid<MailRecord> page(MailRecordQuery pageQuery) {
        return PageHelper.startPage(pageQuery, p -> this.mailRecordServiceDomain.selectJoinList(MailRecord.class, MPJWrappers.lambdaJoin()
                .selectAll(MailRecord.class)
                .selectAs(UserInfo::getName, MailRecord::getSendToName)
                .leftJoin(UserPrivacyInfo.class, UserPrivacyInfo::getEmail, MailRecord::getSendTo)
                .leftJoin(UserInfo.class, UserInfo::getId, UserPrivacyInfo::getId)
                .like(StringUtils.isNotBlank(pageQuery.getSendTo()), MailRecord::getSendTo, pageQuery.getSendTo())
                .like(StringUtils.isNotBlank(pageQuery.getSubject()), MailRecord::getSubject, pageQuery.getSubject())
                .in(CollectionUtils.isNotEmpty(pageQuery.getSendTos()), UserInfo::getId, pageQuery.getSendTos())
                .eq(pageQuery.getBusinessId() != null, MailRecord::getBusinessId, pageQuery.getBusinessId())
                .ge(StringUtils.isNotBlank(pageQuery.getStartDate()), MailRecord::getCreatedAt, pageQuery.getStartDate() + " 00:00:00")
                .le(StringUtils.isNotBlank(pageQuery.getEndDate()), MailRecord::getCreatedAt, pageQuery.getEndDate() + " 23:59:59")
                .orderByDesc(MailRecord::getCreatedAt)
        ));
    }

    public boolean send(MailSendDto mailSendDto) {
        MailRecord mailRecord = new MailRecord();
        mailRecord.setChannel(mailSendDto.getChannel());
        mailRecord.setBusinessId(mailSendDto.getBusinessId());
        mailRecord.setSendTo(mailSendDto.getSendTo());
        mailRecord.setSubject(mailSendDto.getSubject());
        mailRecord.setContent(mailSendDto.getContent());
        mailRecord.setCreatedBy(new UserEntry().setId(ApplicationSessions.id()));

        List<String> emails = Lists.newArrayList();
        List<String> userNames = Lists.newArrayList();
        for (Long sendTo : mailSendDto.getSendTo()) {
            UserInfo userInfo = userDomainService.getUserFullInfo(sendTo);
            if (userInfo.getEmail() == null || StringUtils.isBlank(userInfo.getEmail().getValue())) {
                throw new MaginaException("用户" + userInfo.getName() + "未设置邮箱！");
            }
            userNames.add(userInfo.getName() + "(" + userInfo.getEmail().getValue()+")");
            emails.add(userInfo.getEmail().getValue());
        }
        mailRecord.setSendToName(String.join(",", userNames));
        mailRecord.setStatus(DictUtil.ENABLE);
        ExecutorUtils.doAfterCommit(
                () -> this.send(emails, mailRecord.getSubject(), mailRecord.getContent())
        );
        return mailRecordServiceDomain.save(mailRecord);
    }

    public void send(List<String> emails, String subject, String content) {
        emails.forEach(email -> this.send(email, subject, content));
    }

    /**
     * 发送邮件
     *
     * @param to
     * @param subject
     * @param content
     */
    public void send(String to, String subject, String content) {
        SimpleMailMessage mailMessage = new SimpleMailMessage();
        mailMessage.setFrom("<EMAIL>");
        mailMessage.setTo(to);
        mailMessage.setText(content);
        mailMessage.setSubject(subject);
        javaMailSender.send(mailMessage);
    }
}
