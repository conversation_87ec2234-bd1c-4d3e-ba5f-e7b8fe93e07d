package com.hightop.benyin.configurer;

import com.hightop.fario.base.constant.ProfileConstants;
import com.hightop.magina.standard.ums.system.SystemExcludePattern;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

import java.util.Collections;

/**
 * 开发环境：全局匿名配置
 * @Author: X.S
 * @Date: 2022/11/29 10:50
 */
@Configuration(proxyBeanMethods = false)
public class AnonymousConfig {
    /**
     * 开发环境全局匿名
     * @return {@link SystemExcludePattern}
     * @Author: X.S
     * @Date: 2023/7/27 11:36
     */
//    @Bean
//    @Profile({ProfileConstants.DEV})
//    public SystemExcludePattern devSystemExcludePattern() {
//        return () -> Collections.singletonList("/**");
//    }

}
