package com.hightop.benyin.share.infrastructure.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.experimental.FieldDefaults;

import java.time.LocalDateTime;

/**
 * 用户与微信绑定关系(用户、小程序、公众号)
 * @Author: X.S
 * @date 2023/11/06 16:21
 */
@FieldDefaults(level = AccessLevel.PRIVATE)
@Data
@Accessors(chain = true)
@TableName("b_user_wechat_bind")
public class UserWeChatBind {
    /**
     * 小程序openId
     */
    @TableId(value = "open_id", type = IdType.INPUT)
    String openId;
    /**
     * 用户id或者客户手机号
     */
    @TableField("user_id")
    Long userId;
    /**
     * 微信公众号openId 用于模版消息发送
     */
    @TableField("official_open_id")
    String officialOpenId;
    /**
     * 绑定终端
     */
    @TableField
    String terminal;
    /**
     * 绑定时间
     */
    @TableField("bound_at")
    LocalDateTime boundAt;
}
