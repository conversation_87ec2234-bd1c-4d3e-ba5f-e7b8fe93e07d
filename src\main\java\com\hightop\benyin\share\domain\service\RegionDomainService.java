package com.hightop.benyin.share.domain.service;

import com.github.yulichang.base.MPJBaseServiceImpl;
import com.hightop.benyin.share.infrastructure.entity.Region;
import com.hightop.benyin.share.infrastructure.mapper.RegionMapper;
import com.hightop.fario.base.util.StringUtils;
import org.springframework.stereotype.Service;

import java.util.LinkedList;
import java.util.List;
import java.util.Objects;

/**
 * 地区领域服务
 * @Author: X.S
 * @date 2023/10/20 17:44
 */
@Service
public class RegionDomainService extends MPJBaseServiceImpl<RegionMapper, Region> {
    /**
     * 省份编码后缀
     */
    public static final String PROVINCE_SUFFIX = "0000";
    /**
     * 城市编码后缀
     */
    public static final String CITY_SUFFIX = "00";
    /**
     * 获得地区的全名
     * @param code 地区编码
     * @return 地区全名
     */
    public String getRegionFullName(Integer code) {
        Integer t = code;
        StringBuilder sb = new StringBuilder();
        while (!Objects.equals(t, Region.TOP)) {
            Region region = super.getById(t);
            if (Objects.isNull(region)) {
                break;
            }
            sb.insert(0, region.getRegionFullName());
            t = region.getParentCode();
        }

        return sb.toString();
    }

    public String cutRegion(String regionCode) {
        if (StringUtils.isNotBlank(regionCode)) {
            if (regionCode.endsWith(PROVINCE_SUFFIX)) {
                return regionCode.substring(0, 2);
            } else if (regionCode.endsWith(CITY_SUFFIX)) {
                return regionCode.substring(0, 4);
            }
        }
        return regionCode;
    }

    /**
     * 获得地区从顶到下的编码数组
     * @param code 地区编码
     * @return {@link List}
     */
    public List<Integer> getRegionFullCode(Integer code) {
        Integer t = code;
        List<Integer> list = new LinkedList<>();
        while (!Objects.equals(t, Region.TOP)) {
            Region region = super.getById(t);
            if (Objects.isNull(region)) {
                break;
            }
            list.add(0, region.getCode());
            t = region.getParentCode();
        }

        return list;
    }

    public Region getRegionAndParentName(Integer code){
       Region region =lambdaQuery().eq(Region::getCode, code).one();
       if(!region.isTop()){
           Region parentRegion =lambdaQuery().eq(Region::getCode, region.getParentCode()).one();
           region.setParentName(parentRegion.getFullName());
       }
        return region;
    }
}
