package com.hightop.benyin.share.socket.enums;

import com.hightop.magina.core.custom.entry.EnumEntry;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

/**
 * 消息错误类型
 * <AUTHOR>
 * @date 2024/5/15 13:42
 */
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Getter
public enum ErrorType implements EnumEntry<String> {
    /**
     * 无错误
     */
    NO(""),
    /**
     * 格式错误
     */
    FORMAL_ERROR("格式错误"),
    /**
     * header 错误
     */
    HEAD_ERROR("协议 header 错误"),
    /**
     * 连接异常
     */
    CONNECT_ERROR("连接异常"),
    /**
     * 未知命令
     */
    UNKNOWN_CMD("未知命令"),
    /**
     * 未知命令
     */
    UNKNOWN("未知错误");

    /**
     * 状态名称
     */
    String name;

    @Override
    public String getCode() {
        // 以枚举名称作为标识
        return super.name();
    }

    @Override
    public String getName() {
        return this.name;
    }

}
