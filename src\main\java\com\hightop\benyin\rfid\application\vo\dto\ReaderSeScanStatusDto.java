package com.hightop.benyin.rfid.application.vo.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotNull;

/**
 * @Description: 基站心跳上报设置
 * @Author: X.S
 * @Date: 2023/12/22 17:16
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@ApiModel("基站心跳上报设置DTO")
public class ReaderSeScanStatusDto {

    @ApiModelProperty("id")
    @NotNull(message = "基站信息不能为空")
    Long id;

    @ApiModelProperty("扫描状态")
    @NotNull(message = "扫描状态不能为空")
    Integer status;

    @ApiModelProperty("扫描间隔默认20秒")    //单位：秒
    Integer interval;
    @ApiModelProperty("扫描持续时间")    //单位：秒
    Integer duration;
}
