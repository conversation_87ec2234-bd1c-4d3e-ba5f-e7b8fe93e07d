package com.hightop.benyin.configurer;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.TableInfo;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.baomidou.mybatisplus.core.toolkit.TableNameParser;
import com.github.yulichang.toolkit.SpringContentUtils;
import com.google.common.collect.Lists;
import com.hightop.benyin.configurer.annotation.RecordLog;
import com.hightop.benyin.configurer.util.DataLogUtils;
import com.hightop.benyin.system.domain.event.DataLogEvent;
import com.hightop.benyin.system.infrastructure.entity.DataLog;
import com.hightop.fario.base.util.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlCommandType;
import org.apache.ibatis.plugin.*;
import org.apache.ibatis.session.Configuration;
import org.springframework.context.ApplicationContext;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Properties;

@Slf4j
@Intercepts({@Signature(type = Executor.class, method = "update", args = {MappedStatement.class, Object.class})})
public class MybatisPlusLogInterceptor implements Interceptor {

    private static final List<String> ignoreTables = Lists.newArrayList("st_operation_log", "st_job_log", "st_login_log", "st_data_log", "b_api_log", "b_rfid_asset_change_log", "b_sequence");

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        try {
            MappedStatement mappedStatement = (MappedStatement) invocation.getArgs()[0];
            BoundSql boundSql = mappedStatement.getBoundSql(invocation.getArgs()[1]);
            Configuration configuration = mappedStatement.getConfiguration();
            String preparedSql = boundSql.getSql();

            //如果是新增操作且未启用新增日志，则跳过
            if (SqlCommandType.INSERT.equals(mappedStatement.getSqlCommandType())) {
                return invocation.proceed();
            }
            if (preparedSql.contains("st_operation_log")) {
                return invocation.proceed();
            }
            //SQL语句
            String sql = DataLogUtils.getSql(configuration, boundSql);
            //表名
            Collection<String> tables = new TableNameParser(sql).tables();
            //如果找不到表则跳过
            if (CollectionUtils.isEmpty(tables)) {
                return invocation.proceed();
            }
            //忽略数据日志表操作
            if (ignoreTables.contains(tables.iterator().next())) {
                return invocation.proceed();
            }

            String tableName = CollectionUtils.isNotEmpty(tables) ? tables.iterator().next() : "";//多个表取第一个表名
            //实体
            TableInfo tableInfo = TableInfoHelper.getTableInfos().stream().filter(t -> t.getTableName().equals(tableName)).findFirst().orElse(null);//取第一个实体
            if (tableInfo == null) {//如果找不到实体类则跳过
                return invocation.proceed();
            }
            Class<?> entityType = tableInfo.getEntityType();
            if (entityType == null) {//如果找不到实体类则跳过
                return invocation.proceed();
            }
            boolean isRecordLog = entityType.isAnnotationPresent(RecordLog.class);
            if (!isRecordLog) {//如果实体没有数据日志注解则跳过
                return invocation.proceed();
            }
            //保存日志(只处理使用Mybatis更新函数的数据)
            Object et = invocation.getArgs()[1];
            if (et instanceof Map) {
                String key = "et";
                String listKey = "collection";
                Map<?, ?> map = (Map<?, ?>) et;
                if (map.containsKey(key)) {
                    if (map.get(key) != null) {
                        this.saveLog(mappedStatement.getSqlCommandType(), sql, tableInfo, map.get(key));
                    } else {
                        log.error("SQL更新批量操作，无法保存数据日志");
                    }
                } else if (map.containsKey(listKey) && map.get(listKey) instanceof Collection) {
                    List<Object> list = (List<Object>) map.get(listKey);
                    for (Object o : list) {
                        this.saveLog(mappedStatement.getSqlCommandType(), sql, tableInfo, o);
                    }
                }
            } else {
                this.saveLog(mappedStatement.getSqlCommandType(), sql, tableInfo, et);
            }


        } catch (Exception e) {
            log.error("数据日志保存失败", e);
        }

        return invocation.proceed();

    }

    @Override
    public Object plugin(Object target) {
        return Plugin.wrap(target, this);
    }

    @Override
    public void setProperties(Properties properties) {
    }


    /**
     * 保存日志
     */
    private void saveLog(SqlCommandType sqlCommandType, String sql, TableInfo tableInfo, Object entity) throws Exception {
        //比较修改前后数据
        List<DataLog> dataLogs = DataLogUtils.getDataCompare(sqlCommandType, sql, tableInfo, entity);

        //保存日志
        ApplicationContext applicationContext = SpringContentUtils.getApplicationContext();
        applicationContext.publishEvent(new DataLogEvent(dataLogs));
    }

}
