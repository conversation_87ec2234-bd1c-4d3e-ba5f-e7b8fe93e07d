package com.hightop.benyin.rfid.application.vo.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldDefaults;

/**
 * 资产变更查询DTO
 *
 * <AUTHOR>
 * @date 2024-10-12 16:36:01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@FieldDefaults(level = AccessLevel.PRIVATE)
@ApiModel("rfid变动信息查询DTO")
public class RfidAssetTransferDetailQuery extends RfidAssetTransferQuery {

    @ApiModelProperty("新RFID编码")
    String rfidCode;

    @ApiModelProperty("资产编码")
    String assetCode;

    @ApiModelProperty("资产名称")
    String assetName;

}
