package com.hightop.benyin.rfid.application.service;

import com.github.yulichang.toolkit.MPJWrappers;
import com.hightop.benyin.rfid.application.vo.dto.ReaderFaultDealDto;
import com.hightop.benyin.rfid.application.vo.query.RfidReaderFaultQuery;
import com.hightop.benyin.rfid.domain.service.RfidReaderFaultServiceDomain;
import com.hightop.benyin.rfid.domain.service.RfidReaderServiceDomain;
import com.hightop.benyin.rfid.infrastructure.entity.RfidReader;
import com.hightop.benyin.rfid.infrastructure.entity.RfidReaderFault;
import com.hightop.benyin.rfid.infrastructure.entity.RfidTrajectory;
import com.hightop.benyin.rfid.infrastructure.enums.FaultStatus;
import com.hightop.benyin.rfid.infrastructure.enums.HandleType;
import com.hightop.benyin.rfid.infrastructure.enums.ReaderStatus;
import com.hightop.benyin.system.infrastructure.entity.Location;
import com.hightop.fario.base.util.StringUtils;
import com.hightop.fario.common.mybatis.DataGrid;
import com.hightop.fario.common.mybatis.PageQuery;
import com.hightop.fario.common.mybatis.util.PageHelper;
import com.hightop.magina.casual.session.ApplicationSessions;
import com.hightop.magina.core.exception.MaginaException;
import com.hightop.magina.standard.ums.department.Department;
import com.hightop.magina.standard.ums.user.basic.UserBasic;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * Rfid基站故障管理服务
 *
 * <AUTHOR>
 * @date 2024-10-12 16:36:01
 */
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Service
public class RfidReaderFaultService {

    RfidReaderFaultServiceDomain rfidReaderFaultServiceDomain;
    RfidReaderServiceDomain rfidReaderServiceDomain;

    /**
     * Rfid基站故障管理分页查询
     *
     * @param pageQuery {@link PageQuery}
     * @return {@link DataGrid}
     */
    public DataGrid<RfidReaderFault> page(RfidReaderFaultQuery pageQuery) {
        return PageHelper.startPage(pageQuery, p ->
                this.rfidReaderFaultServiceDomain.selectJoinList(RfidReaderFault.class, MPJWrappers.lambdaJoin()
                        .selectAll(RfidReaderFault.class)
                        .selectAs(Location::getFullName, RfidReaderFault::getLocation)
                        .selectAs(RfidReader::getCode, RfidReaderFault::getReaderCode)
                        .selectAs(UserBasic::getName, RfidReaderFault::getHandleName)
                        .leftJoin(RfidReader.class, RfidReader::getId, RfidReaderFault::getReaderId)
                        .leftJoin(Location.class, Location::getId, RfidReader::getLocationId)
                        .leftJoin(UserBasic.class, UserBasic::getId, RfidReaderFault::getHandleId)
                        .like(StringUtils.isNotBlank(pageQuery.getReaderCode()), RfidReader::getCode, pageQuery.getReaderCode())
                        .like(StringUtils.isNotBlank(pageQuery.getLocation()), Location::getFullName, pageQuery.getLocation())
                        .like(StringUtils.isNotBlank(pageQuery.getDeviceId()), RfidReaderFault::getDeviceId, pageQuery.getDeviceId())
                        .like(StringUtils.isNotBlank(pageQuery.getHandleName()), UserBasic::getName, pageQuery.getHandleName())
                        .eq(StringUtils.isNotBlank(pageQuery.getStatus()), RfidReaderFault::getStatus, pageQuery.getStatus())
                        .eq(StringUtils.isNotBlank(pageQuery.getHandleType()), RfidReaderFault::getHandleType, pageQuery.getHandleType())
                        .ge(StringUtils.isNotBlank(pageQuery.getStartDate()), RfidReaderFault::getCreatedAt, pageQuery.getStartDate() + " 00:00:00")
                        .le(StringUtils.isNotBlank(pageQuery.getEndDate()), RfidReaderFault::getCreatedAt, pageQuery.getEndDate() + " 23:59:59")

                        .ge(StringUtils.isNotBlank(pageQuery.getStartHandleDate()), RfidReaderFault::getHandleAt, pageQuery.getStartHandleDate() + " 00:00:00")
                        .le(StringUtils.isNotBlank(pageQuery.getEndHandleDate()), RfidReaderFault::getHandleAt, pageQuery.getEndHandleDate() + " 23:59:59")

                        .orderByDesc(RfidReaderFault::getCreatedAt)
                )
        );
    }

    /**
     * Rfid基站故障管理添加
     *
     * @param rfidReaderFault {@link RfidReaderFault}
     * @return true/false
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean save(RfidReaderFault rfidReaderFault) {
        long count = rfidReaderFaultServiceDomain.lambdaQuery()
                .eq(RfidReaderFault::getReaderId, rfidReaderFault.getReaderId())
                .eq(RfidReaderFault::getStatus, FaultStatus.WAIT_DEAL).count();
        if (count > 0L) {
            return false;
        }
        return this.rfidReaderFaultServiceDomain.save(rfidReaderFault);
    }

    /**
     * Rfid基站故障恢复
     *
     * @param readerId
     * @return true/false
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean recover(Long readerId) {
        return this.rfidReaderFaultServiceDomain.lambdaUpdate()
                .set(RfidReaderFault::getStatus, FaultStatus.RECOVER)
                .set(RfidReaderFault::getHandleAt, LocalDateTime.now())
                .eq(RfidReaderFault::getReaderId, readerId)
                .eq(RfidReaderFault::getStatus, FaultStatus.WAIT_DEAL)
                .update();
    }


    /**
     * Rfid基站故障恢复
     *
     * @param readerFaultDealDto
     * @return true/false
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deal(ReaderFaultDealDto readerFaultDealDto) {
        if (readerFaultDealDto.getHandleType().equals(HandleType.CHANGE)) {
            RfidReader rfidReader = this.rfidReaderServiceDomain.getById(readerFaultDealDto.getReaderId());
            if (rfidReader == null) {
                throw new MaginaException("基站不存在");
            }
            if (rfidReader.getStatus().equals(ReaderStatus.NORMAL)) {
                throw new MaginaException("请关闭基站确认状态已断线再进行更换操作");
            }
            rfidReader.setDeviceId(readerFaultDealDto.getChangeDeviceId());
            rfidReader.setUpdatedBy(ApplicationSessions.id());
            this.rfidReaderServiceDomain.updateById(rfidReader);
        }

        return this.rfidReaderFaultServiceDomain.lambdaUpdate()
                .set(RfidReaderFault::getStatus, FaultStatus.COMPLETE)
                .set(RfidReaderFault::getRemark, readerFaultDealDto.getRemark())
                .set(RfidReaderFault::getHandleAt, LocalDateTime.now())
                .set(RfidReaderFault::getHandleId, ApplicationSessions.id())
                .set(RfidReaderFault::getHandleType, readerFaultDealDto.getHandleType())
                .set(RfidReaderFault::getChangeDeviceId, readerFaultDealDto.getChangeDeviceId())
                .eq(RfidReaderFault::getId, readerFaultDealDto.getId())
                .update();
    }

}
