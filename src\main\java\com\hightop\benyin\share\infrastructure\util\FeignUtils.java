package com.hightop.benyin.share.infrastructure.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.hightop.fario.common.jackson.FarioObjectMapper;
import com.hightop.fario.common.jackson.module.FarioModule;
import feign.Feign;
import feign.Logger;
import feign.Retryer;
import feign.form.FormEncoder;
import feign.jackson.JacksonDecoder;
import feign.jackson.JacksonEncoder;
import feign.okhttp.OkHttpClient;
import feign.slf4j.Slf4jLogger;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * feign客户端创建工具
 * @Author: X.S
 * @date 2023/11/03 17:15
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class FeignUtils {
    private static final ObjectMapper OBJECT_MAPPER = new FarioObjectMapper() {
        // 构造块初始化
        {
            super.registerModule(new FarioModule());
        }
    };
    private static final JacksonEncoder JACKSON_ENCODER = new JacksonEncoder(OBJECT_MAPPER);
    private static final JacksonDecoder JACKSON_DECODER = new JacksonDecoder(OBJECT_MAPPER);
    private static final FormEncoder FORM_ENCODER = new FormEncoder();

    /**
     * feign构造器
     * 默认encoder jackson
     * @return {@link Feign.Builder}
     */
    public static Feign.Builder builder() {
        return
            Feign.builder()
                .client(new OkHttpClient())
                .logger(new Slf4jLogger())
                .decoder(JACKSON_DECODER)
                .logLevel(Logger.Level.FULL)
                // 不做重试
                .retryer(Retryer.NEVER_RETRY);
    }

    public static Feign.Builder form() {
        return builder().encoder(FORM_ENCODER);
    }

    public static Feign.Builder json() {
        return builder().encoder(JACKSON_ENCODER);
    }
}
