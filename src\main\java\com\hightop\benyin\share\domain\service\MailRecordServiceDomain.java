package com.hightop.benyin.share.domain.service;

import com.github.yulichang.base.MPJBaseServiceImpl;
import com.hightop.benyin.share.infrastructure.entity.MailRecord;
import com.hightop.benyin.share.infrastructure.mapper.MailRecordMapper;
import org.springframework.stereotype.Service;

/**
 * 邮件记录领域服务
 *
 * <AUTHOR>
 * @date 2024-10-12 16:36:01
 */
@Service
public class MailRecordServiceDomain extends MPJBaseServiceImpl<MailRecordMapper, MailRecord> {
}
