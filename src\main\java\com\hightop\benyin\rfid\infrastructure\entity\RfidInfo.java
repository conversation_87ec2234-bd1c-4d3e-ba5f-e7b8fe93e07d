package com.hightop.benyin.rfid.infrastructure.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelIgnore;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.hightop.benyin.share.infrastructure.util.DictUtil;
import com.hightop.magina.standard.code.dictionary.bind.DictItemBind;
import com.hightop.magina.standard.code.dictionary.bind.DictItemEntry;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.time.LocalDateTime;
import java.util.List;

/**
 * rfid信息
 *
 * <AUTHOR>
 * @date 2024-10-12 16:36:01
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@TableName(value = "b_rfid_info", autoResultMap = true)
@ApiModel
public class RfidInfo {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @ApiModelProperty("id")
    Long id;

    @TableField("rfid_code")
    @ApiModelProperty("RFID编码")
    @Excel(name = "RFID编号", width = 30, orderNum = "0")
    String rfidCode;

    @TableField("asset_id")
    @ApiModelProperty("资产编码")
    Long assetId;

    @TableField("ip_addr")
    @ApiModelProperty("ip地址")
    String ipAddr;

    @TableField("reader_id")
    @ApiModelProperty("基站编码")
    Long readerId;

    @TableField("device_id")
    @ApiModelProperty("基站设备编码")
    @Excel(name = "上报基站", width = 15, orderNum = "5")
    String deviceId;

    @TableField("location_id")
    @ApiModelProperty("基站位置编码")
    Long locationId;

    @TableField("location")
    @ApiModelProperty("基站位置")
    @Excel(name = "上报位置", width = 30, orderNum = "6")
    String location;

    @TableField(exist = false)
    @ApiModelProperty("资产小类")
    String assetType;

    @TableField(exist = false)
    @ApiModelProperty("资产类型")
    String assetTypeName;

    @TableField(value = "department_ids", typeHandler = JacksonTypeHandler.class)
    @ApiModelProperty("位置关联的部门id列表")
    private List<Long> departmentIds;

    @TableField("department_name")
    @ApiModelProperty("位置所属单位")
    String departmentName;

    @TableField(value = "manager_ids", typeHandler = JacksonTypeHandler.class)
    @ApiModelProperty("部门负责人(可能多部门)")
    private List<Long> managerIds;

    @TableField("manager_names")
    @ApiModelProperty("部门负责人")
    String managerNames;


    @TableField(value = "keeper_id")
    @ApiModelProperty("保管人")
    private Long keeperId;

    @TableField("keeper_name")
    @ApiModelProperty("保管人")
    String keeperName;

    @TableField(value = "keeper_dept_id")
    @ApiModelProperty("保管人")
    private Long keeperDeptId;

    @TableField("keeper_dept_name")
    @ApiModelProperty("保管人")
    String keeperDeptName;

    @TableField("manager_dept_id")
    @ApiModelProperty(value = "资产责任部门", required = true)
    @ExcelIgnore
    Long managerDeptId;

    @TableField("manager_dept_name")
    @ApiModelProperty("资产责任部门")
    String managerDeptName;


    @TableField("manager_id")
    @ApiModelProperty(value = "资产责任人", required = true)
    @ExcelIgnore
    Long managerId;

    @TableField("manager_name")
    @ApiModelProperty(value = "资产责任人", required = true)
    String managerName;

    @TableField("asset_location_id")
    @ApiModelProperty("资产绑定位置id")
    Long assetLocationId;

    @TableField("asset_location")
    @ApiModelProperty("资产绑定位置")
    String assetLocation;


    @TableField("asset_reader_id")
    @ApiModelProperty("资产绑定基站")
    Long assetReaderId;

    @TableField("asset_reader")
    @ApiModelProperty("资产绑定基站")
    String assetReader;

    @TableField("bind_code")
    @ApiModelProperty("绑定码")
    String bindCode;

    @TableField("report_count")
    @Excel(name = "上报次数", width = 20, orderNum = "2")
    @ApiModelProperty("上报数次")
    Integer reportCount;

    @TableField("last_report_time")
    @ApiModelProperty("最后上报时间")
    @Excel(name = "最后上报时间", width = 30, orderNum = "8", format = "yyyy-MM-dd HH:mm:ss")
    LocalDateTime lastReportTime;

    @TableField("type")
    @ApiModelProperty("上报类型")
    @DictItemBind(DictUtil.DICT_SCAN_TYPE)
    DictItemEntry type;

    @TableField("expire_time")
    @ApiModelProperty("过期时间")
    LocalDateTime expireTime;

    @TableField("status")
    @ApiModelProperty("状态0位置异动1上报异动2已处理3位置上报4自动忽略")
    @Excel(name = "状态", width = 20, orderNum = "4",replace = {"位置异动_0","上报异动_1","自动忽略_4","已处理_2","位置上报_3"})
    Integer status;

    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @ApiModelProperty("上报时间")
    @Excel(name = "上报时间", width = 30, orderNum = "7", format = "yyyy-MM-dd HH:mm:ss")
    LocalDateTime createdAt;

    @TableField(exist = false)
    @ApiModelProperty("资产名称")
    @Excel(name = "资产名称", width = 30, orderNum = "1")
    String assetName;

    @TableField("is_scan")
    @ApiModelProperty("是否扫描")
    @Excel(name = "是否扫描", width = 30, orderNum = "2", replace = {"是_true", "否_false"})
    Boolean isScan;

    @TableField("is_report")
    @ApiModelProperty("是否上报异常")
    @Excel(name = "是否上报异常", width = 30, orderNum = "2", replace = {"是_true", "否_false"})
    Boolean isReport;

    @TableField(exist = false)
    @ApiModelProperty("上报类型")
    @Excel(name = "上报类型", width = 20, orderNum = "3")
    String typeName;

    @TableField(exist = false)
    @ApiModelProperty("资产型号")
    String model;

    @TableField(exist = false)
    @ApiModelProperty("资产编码")
    @Excel(name = "资产名称", width = 30, orderNum = "1")
    String assetCode;
    @TableField(exist = false)
    @ApiModelProperty("基站编码")
    String readerCode;

    @TableField(exist = false)
    @ApiModelProperty("是否绑定")
    Boolean isBind = true;

    public String getTypeName() {
        return type!=null?type.getLabel():null;
    }
}
