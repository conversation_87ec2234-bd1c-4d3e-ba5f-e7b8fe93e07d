package com.hightop.benyin.share.application.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.hightop.benyin.share.infrastructure.entity.Region;
import com.hightop.magina.core.custom.entry.TreeEntry;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldDefaults;

import java.util.List;

/**
 * 地区树节点
 * @Author: X.S
 * @date 2023/10/20 17:47
 */
@FieldDefaults(level = AccessLevel.PRIVATE)
@EqualsAndHashCode(callSuper = true)
@Data
public class RegionTreeVo extends Region implements TreeEntry<Integer, RegionTreeVo> {
    List<RegionTreeVo> children;

    @Override
    public Integer getId() {
        return super.getCode();
    }

    @Override
    public Integer getParentId() {
        return super.getParentCode();
    }

    @JsonIgnore
    @Override
    public Boolean getHasChildren() {
        return super.getHasChildren();
    }
}
