package com.hightop.benyin.share.api.controller;

import com.hightop.benyin.share.application.service.DictExtendService;
import com.hightop.magina.standard.code.dictionary.api.DictItemVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 字典扩展rest接口
 *
 * @Author: X.S
 * @Date: 2023/12/6 15:07
 */
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@RestController
@Api(tags = "字典扩展")
@RequestMapping("/dict-extend")
public class DictExtendController {
    DictExtendService extendService;

    @ApiOperation("字典项子集列表")
    @GetMapping("/itemList/{dictCode}/{itemValue}")
    public List<DictItemVo> itemChildrenList(@ApiParam("字典编码") @PathVariable String dictCode,
                                             @ApiParam("字典项值") @PathVariable String itemValue) {
        return this.extendService.itemChildrenList(dictCode, itemValue);
    }
}
