package com.hightop.benyin.rfid.application.listener;


import com.hightop.benyin.rfid.application.service.RfidAssetChangeLogService;
import com.hightop.benyin.rfid.domain.event.AssetChangeEvent;
import com.hightop.fario.base.util.CollectionUtils;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

/**
 * 资产变更日志监听器
 */
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Service
@Slf4j
public class AssetChangeLogListener {

    RfidAssetChangeLogService rfidAssetChangeLogService;

    @EventListener
    public void onCompleted(AssetChangeEvent event) {
        if (CollectionUtils.isEmpty(event.getAssetChangeLogDtos())) {
            log.error("资产变更日志 数量为0！");
            return;
        }
        log.info("资产变更日志 数量: {}！", event.getAssetChangeLogDtos().size());
        boolean success = rfidAssetChangeLogService.saveChangeLog(event.getAssetChangeLogDtos());
        if (success) {
            log.info("资产变更日志成功！", event.getAssetChangeLogDtos().size());
        }
    }
}
