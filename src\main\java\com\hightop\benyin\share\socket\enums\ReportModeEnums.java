package com.hightop.benyin.share.socket.enums;

import com.hightop.magina.core.custom.entry.EnumEntry;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

/**
 * 日常扫描上报方式枚举
 *
 * @Author: X.S
 * @Date: 2023/12/19 11:43
 */
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@Getter
@RequiredArgsConstructor
public enum ReportModeEnums implements EnumEntry<Integer> {
    DIS_MERGE(0x05, "合并差异上报"),
    ALL_MERGE(0x09, "合并全量上报"),
    DIS_RESPECTIVELY(0x06, "分包差异上报"),
    ALL_RESPECTIVELY(0x0A, "分包全量上报");
    /**
     * 代码
     */
    Integer code;
    /**
     * 描述
     */
    String name;

    public static ReportModeEnums getName(Integer code) {
        for (ReportModeEnums status : ReportModeEnums.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }

}