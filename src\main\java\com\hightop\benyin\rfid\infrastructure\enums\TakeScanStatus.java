package com.hightop.benyin.rfid.infrastructure.enums;

import com.hightop.magina.core.custom.entry.EnumEntry;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

/**
 * 支付凭证状态
 *
 * <AUTHOR>
 * @date 2024/5/15 13:42
 */
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Getter
public enum TakeScanStatus implements EnumEntry<String> {

    /**
     * 资产异常
     */
    ABNORMAL("资产异常"),
    /**
     * 未扫描
     */
    UNABLE("未扫描"),

    /**
     * 扫描中
     */
    EXECUTING("扫描中"),
    /**
     * 扫描完成
     */
    FINISH("扫描完成"),

    /**
     * 无需扫描
     */
    NOT("无需扫描");

    /**
     * 状态名称
     */
    String name;

    @Override
    public String getCode() {
        // 以枚举名称作为标识
        return super.name();
    }

    @Override
    public String getName() {
        return this.name;
    }

}
