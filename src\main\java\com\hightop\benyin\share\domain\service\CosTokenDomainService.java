package com.hightop.benyin.share.domain.service;

import com.hightop.benyin.share.infrastructure.restful.tencent.CosProperties;
import com.hightop.benyin.share.infrastructure.restful.tencent.sts.*;
import com.hightop.benyin.share.infrastructure.util.FeignUtils;
import com.hightop.magina.core.exception.MaginaException;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 腾讯云cos临时安全凭证领域服务
 * @Author: X.S
 * @date 2023/10/25 19:27
 */
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@Component
@Slf4j
public class CosTokenDomainService {
    StsClient stsClient;

    public CosTokenDomainService(CosProperties cosProperties) {
        this.stsClient =
            FeignUtils.json()
                .requestInterceptor(
                    new StsInterceptor(
                        "sts",
                        "2018-08-13",
                        "GetFederationToken",
                        cosProperties.getRegion(),
                        cosProperties.getSecretId(),
                        cosProperties.getSecretKey(),
                        StsClient.HOST
                    )
                )
                .target(StsClient.class, String.format("https://%s", StsClient.HOST));
    }

    /**
     * 生成cos临时凭证
     * @return {@link FederationCredentials}
     */
    public FederationCredentials token(String policy, Integer duration) {
        try {
            StsResponse<FederationTokenResponse> wrapped =
                this.stsClient.federationToken(
                    new FederationTokenRequest().setPolicy(policy).setDurationSeconds(duration)
                );
            FederationTokenResponse response = wrapped.getResponse();
            if (!response.isOk()) {
                StsResponse.StsResponseError error = response.getError();
                throw new MaginaException(
                    String.format("cos临时票据调用出错code = %s, message = %s", error.getCode(), error.getMessage())
                );
            }

            return response.getCredentials().withExpiredTime(response.getExpiredTime());
        } catch (Exception e) {
            log.error(e.getMessage(), e);

            throw new MaginaException("文件存储出错");
        }
    }
}
