package com.hightop.benyin.rfid.application.vo.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.hightop.fario.common.jackson.annotation.JsonAmount;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;

/**
 * 资产-导入实体
 *
 * <AUTHOR>
 * @date 2023-11-06 14:53:13
 */
@Data
@ApiModel("资产登记导出实体")
public class AssetApplyExport {


    @Excel(name = "资产编码", width = 30, orderNum = "2")
    @ApiModelProperty("资产编码")
    @NotBlank
    String code;

    @Excel(name = "资产名称", width = 30, orderNum = "3")
    @ApiModelProperty("资产名称")
    @NotBlank
    String name;

    @Excel(name = "上级资产编码", width = 30, orderNum = "4")
    @ApiModelProperty("上级资产编码")
    String parentCode;

    @Excel(name = "RFID编码", width = 30, orderNum = "5")
    @ApiModelProperty("RFID编码")
    @NotBlank
    String rfidCode;

    @Excel(name = "原RFID编码", width = 30, orderNum = "6")
    @ApiModelProperty("原RFID编码")
    String oriRfidCode;

    @ApiModelProperty("资产型号(选填)")
    @Excel(name = "资产型号", width = 30, orderNum = "7")
    String model;

    @JsonAmount
    @ApiModelProperty("价格(选填)")
    @Excel(name = "价格", width = 30, orderNum = "8")
    Long price;

    @Excel(name = "资产位置", width = 30, orderNum = "8")
    @ApiModelProperty("资产位置")
    @NotBlank
    String warehouseName;

    @ApiModelProperty("详细位置")
    @Excel(name = "详细位置", width = 30, orderNum = "9")
    String lcocation;


    @ApiModelProperty("储藏位置(选填)")
    @Excel(name = "储藏位置", width = 30, orderNum = "9")
    String storageLocation;

    @ApiModelProperty(value = "领用人")
    @Excel(name = "领用人", width = 30, orderNum = "12")
    String applyName;

    @ApiModelProperty("领用时间")
    @Excel(name = "领用时间", width = 30, orderNum = "13", format = "yyyy/MM/dd HH:mm:ss")
    LocalDateTime applyAt;
}
