package com.hightop.benyin.rfid.application.vo.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@ApiModel("资产盘点明细统计DTO")
public class AssetTakeTotalVo {


    @ApiModelProperty("资产数量")
    Integer number;

    @ApiModelProperty("应扫数量")
    long scanNum;

    @ApiModelProperty("不扫数量")
    long noScanNum;

    @ApiModelProperty("扫描数量")
    long scanedNum;

    @ApiModelProperty("异常数量")
    long abnormalNum;

    @ApiModelProperty("正常数量")
    long normalNum;

    @ApiModelProperty("处理数量")
    long dealNum;

    @ApiModelProperty("忽略数量")
    long ignoreNum;

    @ApiModelProperty("盘亏数量")
    long withOutNum;

    @ApiModelProperty("盘盈数量")
    long overFlowNum;



}
