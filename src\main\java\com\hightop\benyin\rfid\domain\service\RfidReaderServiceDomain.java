package com.hightop.benyin.rfid.domain.service;

import com.github.yulichang.base.MPJBaseServiceImpl;
import com.hightop.benyin.rfid.application.vo.query.RfidReaderQuery;
import com.hightop.benyin.rfid.infrastructure.entity.RfidReader;
import com.hightop.benyin.rfid.infrastructure.mapper.RfidReaderMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * rfid基站管理领域服务
 *
 * <AUTHOR>
 * @date 2024-10-12 16:36:01
 */
@Service
public class RfidReaderServiceDomain extends MPJBaseServiceImpl<RfidReaderMapper, RfidReader> {

    /**
     * 列表查询
     *
     * @param query
     * @return
     */
    public List<RfidReader> getReaderList(RfidReaderQuery query) {
        return baseMapper.getReaderList(query);
    }

    /**
     * 根据部门列表查询有效基站
     *
     * @param departmentIds
     * @return
     */
    public List<RfidReader> getReaderListByDepartmentIds(List<Long> departmentIds) {
        RfidReaderQuery query = new RfidReaderQuery();
        query.setDepartmentIds(departmentIds);
        query.setIsEnable(true);
        return baseMapper.getReaderList(query);
    }
    /**
     * 根据部门列表查询有效基站
     *
     * @param locationIds
     * @return
     */
    public List<RfidReader> getReaderListByLocationIds(List<Long> locationIds) {
        RfidReaderQuery query = new RfidReaderQuery();
        query.setLocations(locationIds);
        query.setIsEnable(true);
        return baseMapper.getReaderList(query);
    }

    public void clearReader() {
        baseMapper.clearReader();
    }

}
