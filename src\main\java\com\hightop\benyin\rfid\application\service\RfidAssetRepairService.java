package com.hightop.benyin.rfid.application.service;

import com.github.yulichang.toolkit.MPJWrappers;
import com.hightop.benyin.rfid.application.vo.query.RfidAssetRepairQuery;
import com.hightop.benyin.rfid.domain.service.RfidAssetRepairServiceDomain;
import com.hightop.benyin.rfid.infrastructure.entity.RfidAsset;
import com.hightop.benyin.rfid.infrastructure.entity.RfidAssetFlow;
import com.hightop.benyin.rfid.infrastructure.entity.RfidAssetRepair;
import com.hightop.fario.base.util.CollectionUtils;
import com.hightop.fario.base.util.StringUtils;
import com.hightop.fario.common.mybatis.DataGrid;
import com.hightop.fario.common.mybatis.PageQuery;
import com.hightop.fario.common.mybatis.util.PageHelper;
import com.hightop.magina.standard.ums.department.Department;
import com.hightop.magina.standard.ums.user.basic.UserBasic;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 资产操作单据服务
 *
 * <AUTHOR>
 * @date 2024-10-12 16:36:01
 */
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Service
@Slf4j
public class RfidAssetRepairService {

    RfidAssetRepairServiceDomain rfidAssetRepairServiceDomain;

    /**
     * 资产流水分页查询
     *
     * @param pageQuery {@link PageQuery}
     * @return {@link DataGrid}
     */
    public DataGrid<RfidAssetRepair> page(RfidAssetRepairQuery pageQuery) {
        return PageHelper.startPage(pageQuery, p ->
                this.searchList(pageQuery));
    }

    private List<RfidAssetRepair> searchList(RfidAssetRepairQuery pageQuery) {
        return this.rfidAssetRepairServiceDomain.selectJoinList(RfidAssetRepair.class, MPJWrappers.lambdaJoin()
                .selectAll(RfidAssetRepair.class)
                .selectAs(RfidAsset::getCode, RfidAssetRepair::getAssetCode)
                .selectAs(RfidAsset::getName, RfidAssetRepair::getAssetName)
                .selectAs(RfidAsset::getRfidCode, RfidAssetRepair::getRfidCode)
                .selectAs(RfidAsset::getAssetType, RfidAssetRepair::getAssetType)
                .selectAs(RfidAsset::getModel, RfidAssetRepair::getModel)
                .selectAs(RfidAsset::getPrice, RfidAssetRepair::getPrice)
                .selectAs(RfidAssetFlow::getLocation, RfidAssetRepair::getLocation)
                .selectAs(Department::getName, RfidAssetRepair::getDepartmentName)
                .leftJoin(RfidAsset.class, RfidAsset::getId, RfidAssetRepair::getAssetId)
                .leftJoin(UserBasic.class, UserBasic::getId, RfidAssetRepair::getCreatedBy)
                .leftJoin(Department.class, Department::getId, RfidAssetFlow::getDepartmentId)

                .like(StringUtils.isNotBlank(pageQuery.getCode()), RfidAssetRepair::getCode, pageQuery.getCode())
                .in(CollectionUtils.isNotEmpty(pageQuery.getAssetType()), RfidAsset::getAssetType, pageQuery.getAssetType())
                .in(CollectionUtils.isNotEmpty(pageQuery.getStatusList()), RfidAssetRepair::getStatus, pageQuery.getStatusList())
                .like(StringUtils.isNotBlank(pageQuery.getName()), RfidAsset::getName, pageQuery.getName())
                .like(StringUtils.isNotBlank(pageQuery.getRfidCode()), RfidAsset::getRfidCode, pageQuery.getRfidCode())
                .like(StringUtils.isNotBlank(pageQuery.getCode()), RfidAsset::getCode, pageQuery.getCode())
                .ge(StringUtils.isNotBlank(pageQuery.getStartDate()), RfidAssetRepair::getCreatedAt, pageQuery.getStartDate() + " 00:00:00")
                .le(StringUtils.isNotBlank(pageQuery.getEndDate()), RfidAssetRepair::getCreatedAt, pageQuery.getEndDate() + " 23:59:59")
                .ge(StringUtils.isNotBlank(pageQuery.getStartApplyDate()), RfidAssetRepair::getCreatedAt, pageQuery.getStartApplyDate() + " 00:00:00")
                .le(StringUtils.isNotBlank(pageQuery.getEndApplyDate()), RfidAssetRepair::getCreatedAt, pageQuery.getEndApplyDate() + " 23:59:59")
                .orderByDesc(RfidAssetFlow::getCreatedAt)
        );
    }


}
