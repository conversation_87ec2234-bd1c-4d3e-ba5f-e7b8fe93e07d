package com.hightop.benyin.rfid.domain.service;

import com.github.yulichang.base.MPJBaseServiceImpl;
import com.hightop.benyin.rfid.application.vo.query.RfidAssetTransferDetailQuery;
import com.hightop.benyin.rfid.infrastructure.entity.RfidAssetTransferDetail;
import com.hightop.benyin.rfid.infrastructure.mapper.RfidAssetTransferDetailMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * rfid资产变更管理领域服务
 *
 * <AUTHOR>
 * @date 2024-10-12 16:36:01
 */
@Service
public class RfidAssetTransferDetailServiceDomain extends MPJBaseServiceImpl<RfidAssetTransferDetailMapper, RfidAssetTransferDetail> {

    public List<RfidAssetTransferDetail> pageList( RfidAssetTransferDetailQuery query){
        return baseMapper.pageList(query);
    }
}
