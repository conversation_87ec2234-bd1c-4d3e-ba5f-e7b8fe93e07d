package com.hightop.benyin.share.infrastructure.restful.tencent.sts;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Objects;

/**
 * sts响应
 * @Author: X.S
 * @date 2023/10/25 19:15
 */
@Data
public class StsResponse<T extends StsResponse.StsBaseResponse> {
    @JsonProperty("Response")
    T response;

    @Data
    public static class StsBaseResponse {
        @JsonProperty("Error")
        StsResponseError error;
        @JsonProperty("RequestId")
        String requestId;

        public boolean isOk() {
            return Objects.isNull(this.error);
        }
    }

    @Data
    public static class StsResponseError {
        @JsonProperty("Code")
        String code;
        @JsonProperty("Message")
        String message;
    }
}
