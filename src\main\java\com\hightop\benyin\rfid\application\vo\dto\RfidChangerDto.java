package com.hightop.benyin.rfid.application.vo.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * 盘点DTO
 *
 * @Author: X.S
 * @date 2024/09/29 13:58
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@ApiModel("资产位置变更DTO")
public class RfidChangerDto {

    @ApiModelProperty(value = "资产明细")
    @NotNull(message = "资产明细不能为空")
    List<Long> assetIds;

    @ApiModelProperty(value = "id")
    Long id;

    @NotNull(message = "原位置编码不能为空")
    @ApiModelProperty("原位置编码")
    Long locationId;


    @NotNull(message = "新位置编码不能为空")
    @ApiModelProperty("新位置编码")
    Long newLocationId;

    @ApiModelProperty(value = "变更日期")
    @NotBlank(message = "变更不能为空")
    String transferDate;

    @ApiModelProperty("备注")
    String remark;
}