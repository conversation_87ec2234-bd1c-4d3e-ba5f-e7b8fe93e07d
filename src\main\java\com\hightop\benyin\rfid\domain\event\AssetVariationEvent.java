package com.hightop.benyin.rfid.domain.event;

import com.hightop.benyin.rfid.infrastructure.entity.RfidInfo;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.List;

/**
 * 资产盘点完成 事件
 *
 * <AUTHOR>
 * @date 2024/10/16 14:55
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@AllArgsConstructor
@NoArgsConstructor
public class AssetVariationEvent {

    /**
     * 上报异动的资产信息
     */
    List<RfidInfo> rfidInfos;

    /**
     * 设备id
     */
    String deviceId;
}
