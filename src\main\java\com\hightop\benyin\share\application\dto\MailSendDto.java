package com.hightop.benyin.share.application.dto;

import com.hightop.benyin.share.infrastructure.enums.MainChannel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 邮件发送DTO
 *
 * @Author: X.S
 * @date 2024/09/09 10:39
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@ApiModel("邮件发送DTO")
public class MailSendDto {

    @ApiModelProperty("业务id")
    @NotNull(message = "业务id不能为空")
    Long businessId;

    @ApiModelProperty("发送渠道")
    @NotNull(message = "发送渠道不能为空")
    MainChannel channel;

    @ApiModelProperty("收件人")
    @NotNull(message = "收件人不能为空")
    List<Long> sendTo;

    @ApiModelProperty("收件人")
    String sendToName;

    @ApiModelProperty("主题")
    @NotBlank(message = "主题不能为空")
    String subject;

    @ApiModelProperty("内容")
    @NotBlank(message = "主题不能为空")
    String content;

}
