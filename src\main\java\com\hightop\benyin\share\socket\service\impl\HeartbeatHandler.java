package com.hightop.benyin.share.socket.service.impl;

import com.hightop.benyin.rfid.domain.event.AssetReaderEvent;
import com.hightop.benyin.share.socket.service.CommandHandler;
import com.hightop.benyin.share.socket.util.MsgUtil;
import com.hightop.benyin.share.socket.util.SocketUtil;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

/**
 * 心跳测试命令处理器
 */
@Service
@Slf4j
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class HeartbeatHandler implements CommandHandler {

    ApplicationEventPublisher applicationEventPublisher;

    @Override
    public void handle(String clientName, String deviceId, Integer length, String params) {
        log.info("心跳测试命令处理, client : {}, params: {}", clientName, params);
        deviceId = params.substring(0, 8);
        String code = MsgUtil.hexToString(deviceId);

        //绑定设备
        SocketUtil.bindDevice(clientName, code);

        //发布设备状态上报事件
        applicationEventPublisher.publishEvent(new AssetReaderEvent(params, clientName));
    }
}
