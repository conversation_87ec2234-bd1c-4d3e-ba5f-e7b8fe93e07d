package com.hightop.benyin.rfid.application.service;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import com.github.yulichang.toolkit.MPJWrappers;
import com.google.common.collect.Lists;
import com.hightop.benyin.rfid.application.vo.dto.AssetApplyDto;
import com.hightop.benyin.rfid.application.vo.po.AssetBaseVo;
import com.hightop.benyin.rfid.application.vo.po.AssetTakeTotalVo;
import com.hightop.benyin.rfid.application.vo.po.RfidAssetTakeDetailInfoVo;
import com.hightop.benyin.rfid.application.vo.po.RfidAssetTakeDetailVo;
import com.hightop.benyin.rfid.application.vo.query.RfidAssetTakeDetailQuery;
import com.hightop.benyin.rfid.application.vo.query.RfidTakeDetailQuery;
import com.hightop.benyin.rfid.domain.service.*;
import com.hightop.benyin.rfid.infrastructure.entity.*;
import com.hightop.benyin.rfid.infrastructure.enums.*;
import com.hightop.benyin.share.application.dto.MailSendDto;
import com.hightop.benyin.share.application.service.MailRecordService;
import com.hightop.benyin.share.infrastructure.entity.MailRecord;
import com.hightop.benyin.share.infrastructure.enums.MainChannel;
import com.hightop.benyin.share.infrastructure.util.DictUtil;
import com.hightop.benyin.share.infrastructure.util.DownloadResponseUtil;
import com.hightop.benyin.system.domain.service.DepartmentInfoDomainService;
import com.hightop.benyin.system.domain.service.LocationDomainService;
import com.hightop.benyin.system.domain.service.UserInfoDomainService;
import com.hightop.benyin.system.infrastructure.entity.AssetType;
import com.hightop.benyin.system.infrastructure.entity.DepartmentInfo;
import com.hightop.benyin.system.infrastructure.entity.Location;
import com.hightop.benyin.system.infrastructure.entity.UserInfo;
import com.hightop.fario.base.util.CollectionUtils;
import com.hightop.fario.base.util.StringUtils;
import com.hightop.fario.common.core.util.ExecutorUtils;
import com.hightop.fario.common.mybatis.DataGrid;
import com.hightop.fario.common.mybatis.PageQuery;
import com.hightop.fario.common.mybatis.util.PageHelper;
import com.hightop.magina.casual.session.ApplicationSessions;
import com.hightop.magina.core.exception.MaginaException;
import com.hightop.magina.standard.code.dictionary.bind.DictItemEntry;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Rfid盘点明细管理服务
 *
 * <AUTHOR>
 * @date 2024-10-12 16:36:01
 */
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Service
public class RfidAssetTakeDetailService {

    RfidAssetTakeDetailServiceDomain rfidAssetTakeDetailServiceDomain;
    RfidAssetTakeServiceDomain rfidAssetTakeServiceDomain;
    RfidAssetTakeReaderServiceDomain rfidAssetTakeReaderServiceDomain;
    RfidInfoServiceDomain rfidInfoServiceDomain;
    RfidReaderServiceDomain rfidReaderServiceDomain;
    LocationDomainService locationDomainService;
    UserInfoDomainService userInfoDomainService;
    DepartmentInfoDomainService departmentInfoDomainService;
    MailRecordService mailRecordService;
    RfidAssetService rfidAssetService;
    RfidAssetServiceDomain rfidAssetServiceDomain;
    RfidReaderService rfidReaderService;
    RfidAssetTakeService rfidAssetTakeService;
    RfidAssetFlowService rfidAssetFlowService;

    /**
     * 资产盘点明细分页查询
     *
     * @param pageQuery {@link PageQuery}
     * @return {@link DataGrid}
     */
    public DataGrid<RfidAssetTakeDetail> detailPage(RfidAssetTakeDetailQuery pageQuery) {
        return PageHelper.startPage(pageQuery, p ->
                this.getList(pageQuery)
        );
    }

    private List<RfidAssetTakeDetail> getList(RfidAssetTakeDetailQuery pageQuery) {
        return this.rfidAssetTakeDetailServiceDomain.selectJoinList(RfidAssetTakeDetail.class, MPJWrappers.lambdaJoin()
                .selectAll(RfidAssetTakeDetail.class)
                .selectAs(RfidAsset::getCode, RfidAssetTakeDetail::getCode)
                .selectAs(RfidAsset::getModel, RfidAssetTakeDetail::getModel)
                .selectAs(RfidAsset::getName, RfidAssetTakeDetail::getName)
                .selectAs(RfidAsset::getAssetType, RfidAssetTakeDetail::getAssetType)
                .selectAs(RfidAsset::getUseState, RfidAssetTakeDetail::getUseState)
                .selectAs(AssetType::getName, RfidAssetTakeDetail::getAssetTypeName)
                .selectAs(UserInfo::getName, RfidAssetTakeDetail::getCreatedName)
                .selectAs(DepartmentInfo::getName, RfidAssetTakeDetail::getCreatedDeptName)

                .leftJoin(RfidAsset.class, RfidAsset::getId, RfidAssetTakeDetail::getAssetId)
                .leftJoin(AssetType.class, AssetType::getCode, RfidAsset::getAssetType)
                .leftJoin(RfidReader.class, RfidReader::getId, RfidAsset::getReaderId)
                .leftJoin(RfidAssetTake.class, RfidAssetTake::getCode, RfidAssetTakeDetail::getTakeCode)
                .leftJoin(UserInfo.class, UserInfo::getCode, RfidAssetTake::getCreatedBy)
                .leftJoin(DepartmentInfo.class, DepartmentInfo::getId, RfidAssetTake::getCreateDept)

                .eq(RfidAssetTakeDetail::getTakeCode, pageQuery.getTakeCode())
                .like(StringUtils.isNotBlank(pageQuery.getName()), RfidAsset::getName, pageQuery.getName())
                .like(StringUtils.isNotBlank(pageQuery.getCode()), RfidAsset::getCode, pageQuery.getCode())
                .like(StringUtils.isNotBlank(pageQuery.getRfidCode()), RfidAssetTakeDetail::getRfidCode, pageQuery.getRfidCode())
                .like(StringUtils.isNotBlank(pageQuery.getName()), RfidAsset::getName, pageQuery.getName())
                .like(StringUtils.isNotBlank(pageQuery.getModel()), RfidAsset::getModel, pageQuery.getModel())
                .like(StringUtils.isNotBlank(pageQuery.getDeviceId()), RfidAssetTakeDetail::getDeviceId, pageQuery.getDeviceId())
                .eq(pageQuery.getReaderId() != null, RfidAssetTakeDetail::getReaderId, pageQuery.getReaderId())
                .eq(pageQuery.getHasTag() != null, RfidAssetTakeDetail::getHasTag, pageQuery.getHasTag())
                .eq(pageQuery.getIsTake() != null, RfidAssetTakeDetail::getIsTake, pageQuery.getIsTake())
                .eq(StringUtils.isNotBlank(pageQuery.getTakeType()), RfidAssetTake::getTakeType, pageQuery.getTakeType())
                .eq(pageQuery.getTakeRange() != null, RfidAssetTake::getTakeRange, pageQuery.getTakeRange())
                .isNotNull(pageQuery.getIsBind() != null && pageQuery.getIsBind(), RfidAsset::getRfidCode)
                .ne(pageQuery.getIsBind() != null && pageQuery.getIsBind(), RfidAsset::getRfidCode, "")
                .isNull(pageQuery.getIsBind() != null && !pageQuery.getIsBind(), RfidAsset::getRfidCode)
                .isNotNull(pageQuery.getIsBindReader() != null && pageQuery.getIsBindReader(), RfidReader::getId)
                .isNull(pageQuery.getIsBindReader() != null && !pageQuery.getIsBindReader(), RfidReader::getId)
                .in(CollectionUtils.isNotEmpty(pageQuery.getManagerDeptIds()), RfidAssetTakeDetail::getManagerDeptId, pageQuery.getManagerDeptIds())
                .in(CollectionUtils.isNotEmpty(pageQuery.getProcessType()), RfidAssetTakeDetail::getProcessType, pageQuery.getProcessType())
                .in(CollectionUtils.isNotEmpty(pageQuery.getManagerIds()), RfidAssetTakeDetail::getManagerId, pageQuery.getManagerIds())
                .in(CollectionUtils.isNotEmpty(pageQuery.getApplyIds()), RfidAssetTakeDetail::getApplyId, pageQuery.getApplyIds())
                .in(CollectionUtils.isNotEmpty(pageQuery.getAssetType()), RfidAsset::getAssetType, pageQuery.getAssetType())
                .in(CollectionUtils.isNotEmpty(pageQuery.getUseState()), RfidAsset::getUseState, pageQuery.getUseState())
                .in(CollectionUtils.isNotEmpty(pageQuery.getDepartmentIds()), RfidAssetTakeDetail::getDepartmentId, pageQuery.getDepartmentIds())
                .in(CollectionUtils.isNotEmpty(pageQuery.getLocations()), RfidAssetTakeDetail::getLocationId, pageQuery.getLocations())
                .in(CollectionUtils.isNotEmpty(pageQuery.getStatus()), RfidAssetTakeDetail::getStatus, pageQuery.getStatus())
                .in(CollectionUtils.isNotEmpty(pageQuery.getTakeStatus()), RfidAssetTakeDetail::getTakeStatus, pageQuery.getTakeStatus())
                .in(CollectionUtils.isNotEmpty(pageQuery.getScanStatus()), RfidAssetTakeDetail::getScanStatus, pageQuery.getScanStatus())
                .ge(StringUtils.isNotBlank(pageQuery.getStartDate()), RfidAssetTakeDetail::getCreatedAt, pageQuery.getStartDate() + " 00:00:00")
                .le(StringUtils.isNotBlank(pageQuery.getEndDate()), RfidAssetTakeDetail::getCreatedAt, pageQuery.getEndDate() + " 23:59:59")
                .orderByAsc(RfidAssetTakeDetail::getTakeStatus)
                .orderByAsc(RfidAssetTakeDetail::getRfidCode)
                .orderByAsc(RfidAssetTakeDetail::getId)
        );
    }

    /**
     * 资产盘点明细分页查询
     *
     * @param pageQuery {@link PageQuery}
     * @return {@link DataGrid}
     */
    public AssetTakeTotalVo detailTotal(RfidAssetTakeDetailQuery pageQuery) {
        List<RfidAssetTakeDetail> list = this.getList(pageQuery);
        AssetTakeTotalVo total = new AssetTakeTotalVo();

        RfidAssetTake rfidAssetTake = this.rfidAssetTakeServiceDomain.lambdaQuery().eq(RfidAssetTake::getCode, pageQuery.getTakeCode()).one();
        List<RfidAssetTakeReader> rfidAssetTakeReaders = this.rfidAssetTakeReaderServiceDomain.lambdaQuery()
                .eq(RfidAssetTakeReader::getTakeCode, pageQuery.getTakeCode()).list();

        total.setScanNum(rfidAssetTakeReaders.stream().map(RfidAssetTakeReader::getBindNum).reduce(Integer::sum).orElse(0));
        total.setScanedNum(list.stream().filter(item -> CollectionUtils.isNotEmpty(item.getScanLocationIds())).count());
        total.setAbnormalNum(list.stream().filter(item -> item.getScanStatus().equals(TakeScanStatus.ABNORMAL)).count());

        total.setNumber(rfidAssetTake.getBeforeNum());
        total.setNoScanNum(list.stream().filter(item -> item.getScanStatus().equals(TakeScanStatus.NOT)).count());
        total.setNormalNum(rfidAssetTake.getNormalNum());
        total.setIgnoreNum(rfidAssetTake.getIgnoreNum());
        total.setDealNum(rfidAssetTake.getDealNum());
        total.setOverFlowNum(list.stream().filter(item -> item.getStatus().equals(DiscrepancyStatus.OVERFLOW)).count());
        total.setWithOutNum(list.stream().filter(item -> item.getStatus().equals(DiscrepancyStatus.WITHOUT)).count());
        return total;
    }

    public boolean refresh(String takeCode) {
        List<RfidAssetTakeDetail> list = this.rfidAssetTakeDetailServiceDomain.lambdaQuery().eq(RfidAssetTakeDetail::getTakeCode, takeCode)
                .eq(RfidAssetTakeDetail::getScanStatus, TakeScanStatus.ABNORMAL)
                .list();

        //刷新资产
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(item -> {
                RfidAsset asset = this.rfidAssetServiceDomain.lambdaQuery().eq(RfidAsset::getId, item.getAssetId()).one();
                if (asset != null) {
                    item.setRemark(rfidAssetTakeService.checkAsset(asset));
                    item.setIsTake(asset.getIsTake());
                    item.setIsTakeStatis(asset.getIsTakeStatis());
                    item.setHasTag(asset.getHasTag());
                    item.setRfidCode(asset.getRfidCode());
                    if (StringUtils.isNotBlank(item.getRemark())) {
                        item.setScanStatus(TakeScanStatus.ABNORMAL);
                    } else {
                        //没有标签默认正常
                        if (!item.getHasTag()) {
                            item.setScanStatus(TakeScanStatus.NOT);
                            item.setTakeStatus(TakeStatus.YES);
                            item.setStatus(DiscrepancyStatus.NORMAL);
                        } else {
                            item.setScanStatus(TakeScanStatus.EXECUTING);
                            item.setTakeStatus(TakeStatus.EXECUTING);
                            item.setStatus(DiscrepancyStatus.EXECUTING);
                        }
                    }
                }
            });
            this.rfidAssetTakeDetailServiceDomain.updateBatchById(list);
        }
        return true;
    }

    /**
     * 查询资产盘点扫描列表
     *
     * @param query
     * @return
     */
    public DataGrid<RfidAssetTakeDetailVo> findAssetTakeScanList(RfidTakeDetailQuery query) {
        return PageHelper.startPage(query, p ->
                this.rfidAssetTakeServiceDomain.findAssetTakeScanList(query)
        );
    }

    public boolean update(RfidAssetTakeDetail detail) {
        detail.setUpdatedBy(ApplicationSessions.id());
        return this.rfidAssetTakeDetailServiceDomain.updateById(detail);
    }

    public RfidAssetTakeDetailInfoVo getTakeDetailsInfo(Long id) {
        RfidAssetTakeDetailInfoVo detail = this.rfidAssetTakeDetailServiceDomain
                .selectJoinOne(RfidAssetTakeDetailInfoVo.class, MPJWrappers.lambdaJoin()
                        .selectAll(RfidAssetTakeDetail.class)
                        .selectAs(RfidAsset::getCode, RfidAssetTakeDetailInfoVo::getCode)
                        .selectAs(RfidAsset::getModel, RfidAssetTakeDetailInfoVo::getModel)
                        .selectAs(RfidAsset::getName, RfidAssetTakeDetailInfoVo::getName)
                        .selectAs(RfidAsset::getAssetType, RfidAssetTakeDetailInfoVo::getAssetType)
                        .selectAs(RfidAsset::getUseState, RfidAssetTakeDetailInfoVo::getUseState)
                        .selectAs(RfidAsset::getPrice, RfidAssetTakeDetailInfoVo::getPrice)
                        .selectAs(RfidAsset::getHasTag, RfidAssetTakeDetailInfoVo::getHasTag)
                        .selectAs(AssetType::getName, RfidAssetTakeDetailInfoVo::getAssetTypeName)
                        .leftJoin(RfidAsset.class, RfidAsset::getId, RfidAssetTakeDetail::getAssetId)
                        .leftJoin(AssetType.class, AssetType::getCode, RfidAsset::getAssetType)
                        .eq(RfidAssetTakeDetail::getId, id)
                );

        AssetBaseVo oriAsset = new AssetBaseVo();
        oriAsset.setApplyId(detail.getApplyId());
        oriAsset.setApplyName(detail.getApplyName());
        oriAsset.setDepartmentId(detail.getDepartmentId());
        oriAsset.setDepartmentName(detail.getDepartmentName());
        oriAsset.setManagerId(detail.getManagerId());
        oriAsset.setManagerName(detail.getManagerName());
        oriAsset.setManagerDeptId(detail.getManagerDeptId());
        oriAsset.setManagerDeptName(detail.getManagerDeptName());
        oriAsset.setLocation(detail.getLocation());
        oriAsset.setLocationId(detail.getLocationId());
        oriAsset.setReaderId(detail.getReaderId());

        String takeCode = detail.getTakeCode();
        List<RfidInfo> rfidInfos = this.rfidInfoServiceDomain.lambdaQuery()
                .eq(RfidInfo::getAssetId, detail.getAssetId())
                .eq(RfidInfo::getType, DictUtil.LOOK_SCAN).eq(RfidInfo::getBindCode, takeCode)
                .list();
        if (CollectionUtils.isNotEmpty(rfidInfos)) {
            RfidInfo rfidInfo = rfidInfos.stream().filter(r -> r.getType().getValue().equals(DictUtil.LOOK_SCAN)).findFirst().orElse(null);
            if (Objects.isNull(rfidInfo)) {
                rfidInfo = rfidInfos.stream().filter(r -> r.getType().getValue().equals(DictUtil.LOOK_SCAN)).findFirst().orElse(null);
            }
            if (rfidInfo != null) {
                AssetBaseVo nowAsset = new AssetBaseVo();
                nowAsset.setApplyId(rfidInfo.getKeeperId());
                nowAsset.setApplyName(rfidInfo.getKeeperName());
                nowAsset.setDepartmentId(rfidInfo.getKeeperDeptId());
                nowAsset.setDepartmentName(rfidInfo.getKeeperDeptName());
                nowAsset.setManagerId(rfidInfo.getManagerId());
                nowAsset.setManagerName(rfidInfo.getManagerName());
                nowAsset.setManagerDeptId(rfidInfo.getManagerDeptId());
                nowAsset.setManagerDeptName(rfidInfo.getManagerDeptName());
                nowAsset.setLocation(rfidInfo.getLocation());
                nowAsset.setLocationId(rfidInfo.getLocationId());
                nowAsset.setReaderId(rfidInfo.getReaderId());
                nowAsset.setDeviceId(rfidInfo.getDeviceId());
                detail.setNowAssetVo(nowAsset);
            }
        } else {
            AssetBaseVo nowAsset = new AssetBaseVo();
            nowAsset.setApplyId(detail.getApplyId());
            nowAsset.setApplyName(detail.getApplyName());
            nowAsset.setDepartmentId(detail.getDepartmentId());
            nowAsset.setDepartmentName(detail.getDepartmentName());
            nowAsset.setManagerId(detail.getManagerId());
            nowAsset.setManagerName(detail.getManagerName());
            nowAsset.setManagerDeptId(detail.getManagerDeptId());
            nowAsset.setManagerDeptName(detail.getManagerDeptName());
            detail.setNowAssetVo(nowAsset);
        }
        detail.setOriAssetVo(oriAsset);
        detail.setDeviceId(detail.getDeviceId());
        detail.setLocation(detail.getLocation());
        detail.setProcessType(detail.getProcessType());
        detail.setAbnormalAnalyze(detail.getAbnormalAnalyze());
        return detail;
    }

    public RfidAssetTakeDetail getProcessInfo(Long id) {
        RfidAssetTakeDetail detail = this.rfidAssetTakeDetailServiceDomain.selectJoinOne(RfidAssetTakeDetail.class, MPJWrappers.lambdaJoin()
                .selectAll(RfidAssetTakeDetail.class)
                .selectAs(RfidAsset::getCode, RfidAssetTakeDetail::getCode)
                .selectAs(RfidAsset::getModel, RfidAssetTakeDetail::getModel)
                .selectAs(RfidAsset::getName, RfidAssetTakeDetail::getName)
                .selectAs(RfidAsset::getAssetType, RfidAssetTakeDetail::getAssetType)
                .selectAs(RfidAsset::getUseState, RfidAssetTakeDetail::getUseState)
                .selectAs(RfidAsset::getPrice, RfidAssetTakeDetail::getPrice)
                .selectAs(RfidAsset::getHasTag, RfidAssetTakeDetail::getHasTag)
                .selectAs(AssetType::getName, RfidAssetTakeDetail::getAssetTypeName)
                .leftJoin(RfidAsset.class, RfidAsset::getId, RfidAssetTakeDetail::getAssetId)
                .leftJoin(AssetType.class, AssetType::getCode, RfidAsset::getAssetType)
                .eq(RfidAssetTakeDetail::getId, id)
        );
        return detail;
    }

    /**
     * 获取异常分析
     *
     * @param rfidAssetTakeDetail
     * @return
     */
    public String getAbnormalAnalyze(RfidAssetTakeDetail rfidAssetTakeDetail) {
        String abnormalAnalyze = "";
        Long userId = rfidAssetTakeDetail.getApplyId() != null ? rfidAssetTakeDetail.getApplyId() : rfidAssetTakeDetail.getManagerId();
        UserInfo userInfo = this.userInfoDomainService.getUserFullInfo(userId);
        String mobileNumber = userInfo.getMobileNumber() == null ? "无" : userInfo.getMobileNumber().getValue();
        switch (rfidAssetTakeDetail.getStatus()) {
            case WITHOUT:
                abnormalAnalyze = String.format(DictUtil.TAKE_LOST_HEAD,
                        rfidAssetTakeDetail.getName(), rfidAssetTakeDetail.getCode(), rfidAssetTakeDetail.getRfidCode(),
                        userInfo.getName(), mobileNumber, rfidAssetTakeDetail.getLocation());
                break;
            case CHANGE:
                abnormalAnalyze = String.format(DictUtil.TAKE_CHANGE_OUT_HEAD,
                        rfidAssetTakeDetail.getName(), rfidAssetTakeDetail.getCode(), rfidAssetTakeDetail.getRfidCode(),
                        userInfo.getName(), mobileNumber, rfidAssetTakeDetail.getLocation(), rfidAssetTakeDetail.getScanLocation());
                break;
            case OVERFLOW:
                abnormalAnalyze = String.format(DictUtil.TAKE_SURPLUS_HEAD,
                        rfidAssetTakeDetail.getRfidCode(), rfidAssetTakeDetail.getScanLocation());
                break;
        }
        return abnormalAnalyze;
    }

    /**
     * 通知信息
     *
     * @param id
     * @return
     */
    public MailRecord noticeInfo(Long id) {
        RfidAssetTakeDetail detail = this.rfidAssetTakeDetailServiceDomain.selectJoinOne(RfidAssetTakeDetail.class, MPJWrappers.lambdaJoin()
                .selectAll(RfidAssetTakeDetail.class)
                .selectAs(RfidAsset::getCode, RfidAssetTakeDetail::getCode)
                .selectAs(RfidAsset::getModel, RfidAssetTakeDetail::getModel)
                .selectAs(RfidAsset::getName, RfidAssetTakeDetail::getName)
                .selectAs(RfidAsset::getUseState, RfidAssetTakeDetail::getUseState)
                .selectAs(RfidAsset::getPrice, RfidAssetTakeDetail::getPrice)
                .selectAs(RfidAsset::getHasTag, RfidAssetTakeDetail::getHasTag)
                .leftJoin(RfidAsset.class, RfidAsset::getId, RfidAssetTakeDetail::getAssetId)
                .eq(RfidAssetTakeDetail::getId, id)
        );
        if (detail.getStatus().equals(DiscrepancyStatus.NORMAL)) {
            throw new MaginaException("当前数据未找到异常！");
        }
        String takeCode = detail.getTakeCode();
        MailRecord mailRecord = new MailRecord();
        mailRecord.setChannel(MainChannel.TAKE);
        mailRecord.setBusinessId(detail.getId());
        //报损
        if (detail.getStatus().equals(DiscrepancyStatus.WITHOUT)) {
            Long userId = detail.getApplyId() != null ? detail.getApplyId() : detail.getManagerId();
            UserInfo userInfo = this.userInfoDomainService.getUserFullInfo(userId);
            mailRecord.setSendTo(Lists.newArrayList(userId));
            mailRecord.setSendToName(userInfo.getName() + "(" + userInfo.getEmail().getValue() + ")");

            List<RfidInfo> rfidInfos = this.rfidInfoServiceDomain.lambdaQuery()
                    .eq(RfidInfo::getAssetId, detail.getAssetId())
                    .eq(RfidInfo::getType, DictUtil.LOOK_SCAN).eq(RfidInfo::getBindCode, takeCode)
                    .list();
            List<String> locations = rfidInfos.stream().map(RfidInfo::getLocation).distinct().collect(Collectors.toList());

            //未找到该资产，需要通知责任人
            mailRecord.setSubject(DictUtil.TAKE_LOST_SUBJECT);
            String content = String.format(DictUtil.TAKE_LOST_CONTENT,
                    detail.getName(), detail.getCode(), detail.getRfidCode(), userInfo.getName(), userInfo.getMobileNumber() == null ? "无" : userInfo.getMobileNumber().getValue(), detail.getLocation()
                    , String.join(",", locations));
            mailRecord.setContent(content);

        }

        //全局扫描到了该资产
        if (detail.getStatus().equals(DiscrepancyStatus.CHANGE)) {
            Long userId = detail.getApplyId() != null ? detail.getApplyId() : detail.getManagerId();
            UserInfo userInfo = this.userInfoDomainService.getUserFullInfo(userId);
            mailRecord.setSubject(DictUtil.TAKE_CH_SUBJECT);
            String content = String.format(DictUtil.TAKE_CH_CONTENT,
                    detail.getName(), detail.getCode(), detail.getRfidCode(), userInfo.getName(), userInfo.getMobileNumber() == null ? "无" : userInfo.getMobileNumber().getValue(), detail.getLocation(), detail.getScanLocation());
            mailRecord.setContent(content);
        }

        if (detail.getStatus().equals(DiscrepancyStatus.OVERFLOW)) {
            //找部门资产负责人 报溢才会有
            List<Long> locationIds = detail.getScanLocationIds();
            List<Location> locations = this.locationDomainService.lambdaQuery()
                    .in(Location::getId, locationIds)
                    .list();
            if (CollectionUtils.isNotEmpty(locations)) {
                List<Long> departmentIds = new ArrayList<>();
                locations.forEach(l -> {
                    if (l.getDepartmentIds() == null) {
                        departmentIds.addAll(l.getDepartmentIds());
                    }
                });
                List<DepartmentInfo> departmentInfos = this.departmentInfoDomainService.lambdaQuery()
                        .in(DepartmentInfo::getId, departmentIds)
                        .list();

                List<Long> userIds = departmentInfos.stream().map(DepartmentInfo::getManagerId).collect(Collectors.toList());
                List<String> sendTos = Lists.newArrayList();
                for (Long uid : userIds) {
                    UserInfo user = this.userInfoDomainService.getUserFullInfo(uid);
                    sendTos.add(user.getName() + "(" + user.getEmail().getValue() + ")");
                }
                mailRecord.setSendTo(userIds);
                mailRecord.setSendToName(String.join(",", sendTos));
            }

            if (detail.getAssetId() == null) {
                //发现新标签 未关联资产
                mailRecord.setSubject(DictUtil.TAKE_SURPLUS_SUBJECT);
                String content = String.format(DictUtil.TAKE_SURPLUS_CONTENT, detail.getRfidCode(), detail.getScanLocation());
                mailRecord.setContent(content);
            } else {
                //发现新标签 已关联资产
                Long userId = detail.getApplyId() != null ? detail.getApplyId() : detail.getManagerId();
                UserInfo userInfo = this.userInfoDomainService.getUserFullInfo(userId);
                mailRecord.setSubject(DictUtil.TAKE_OVER_SUBJECT);
                String content = String.format(DictUtil.TAKE_CH_CONTENT,
                        detail.getName(), detail.getCode(), detail.getRfidCode(), userInfo.getName(), userInfo.getMobileNumber() == null ? "无" : userInfo.getMobileNumber().getValue(), detail.getLocation());
                mailRecord.setContent(content);
            }
        }

        return mailRecord;
    }

    public boolean process(RfidAssetTakeDetailInfoVo rfidAssetTakeDetailInfoVo) {
        RfidAssetTakeDetail detail = this.rfidAssetTakeDetailServiceDomain.getById(rfidAssetTakeDetailInfoVo.getId());
        detail.setTakeStatus(TakeStatus.YES);
        detail.setProcessType(rfidAssetTakeDetailInfoVo.getProcessType());
        detail.setAbnormalAnalyze(rfidAssetTakeDetailInfoVo.getAbnormalAnalyze());
        detail.setRemark(rfidAssetTakeDetailInfoVo.getRemark());
        detail.setUpdatedBy(ApplicationSessions.id());
        RfidAsset rfidAsset = this.rfidAssetServiceDomain.getById(detail.getAssetId());
        Long oriReaderId = rfidAsset.getReaderId();

        //处理方式
        if (rfidAssetTakeDetailInfoVo.getProcessType().equals(ProcessType.CHANGE)) {
            if (rfidAssetTakeDetailInfoVo.getChangeLocation() == null
                    && rfidAssetTakeDetailInfoVo.getChangeManagerId() == null
                    && rfidAssetTakeDetailInfoVo.getChangeManagerDeptId() == null
                    && rfidAssetTakeDetailInfoVo.getChangeApplyId() == null
                    && rfidAssetTakeDetailInfoVo.getChangeApplyDeptId() == null
            ) {
                throw new MaginaException("请至少选择一种资产变更信息！");
            }
            if (rfidAssetTakeDetailInfoVo.getChangeManagerId() == null
                    && rfidAssetTakeDetailInfoVo.getChangeManagerDeptId() != null) {
                throw new MaginaException("请选择责任人信息！");
            }
            if (rfidAssetTakeDetailInfoVo.getChangeApplyId() == null
                    && rfidAssetTakeDetailInfoVo.getChangeApplyDeptId() != null) {
                throw new MaginaException("请选择保管人信息！");
            }

            if (rfidAssetTakeDetailInfoVo.getChangeLocation() != null) {
                if (rfidAssetTakeDetailInfoVo.getChangeReader() == null) {
                    throw new MaginaException("请选择变更基站信息！");
                } else {
                    detail.setChangeLocation(rfidAssetTakeDetailInfoVo.getChangeLocation());
                    detail.setChangeReader(rfidAssetTakeDetailInfoVo.getChangeReader());
                    //更新资产表
                    rfidAsset.setLocationId(rfidAssetTakeDetailInfoVo.getChangeLocation());
                    rfidAsset.setReaderId(rfidAssetTakeDetailInfoVo.getChangeReader());
                }
            }

            if (rfidAssetTakeDetailInfoVo.getChangeApplyId() != null) {
                UserInfo userInfo = userInfoDomainService.getById(rfidAssetTakeDetailInfoVo.getChangeApplyId());
                detail.setChangeApplyId(rfidAssetTakeDetailInfoVo.getChangeApplyId());
                detail.setChangeApplyDeptId(userInfo.getDepartmentId());
                //更新资产表
                rfidAsset.setDepartmentId(userInfo.getDepartmentId());
                rfidAsset.setApplyId(userInfo.getId());
                rfidAsset.setApplyName(userInfo.getName());
                rfidAsset.setApplyAt(LocalDateTime.now());
            }
            if (rfidAssetTakeDetailInfoVo.getChangeManagerId() != null) {
                detail.setChangeManagerId(rfidAssetTakeDetailInfoVo.getChangeManagerId());
                UserInfo userInfo = userInfoDomainService.getById(rfidAssetTakeDetailInfoVo.getChangeManagerId());
                detail.setChangeManagerDeptId(userInfo.getDepartmentId());
                //更新资产表
                rfidAsset.setManagerDeptId(userInfo.getDepartmentId());
                rfidAsset.setManagerId(rfidAssetTakeDetailInfoVo.getChangeManagerId());
                rfidAsset.setManagerName(userInfo.getName());
            }
        }

        rfidAsset.setUpdatedBy(ApplicationSessions.id());
        this.rfidAssetServiceDomain.updateById(rfidAsset);
        //处理方式
        if (rfidAssetTakeDetailInfoVo.getProcessType().equals(ProcessType.DAMAGE)) {
            rfidAsset.setUseState(new DictItemEntry().setValue(DictUtil.BREAKAGE));
            rfidAsset.setBreakageAt(LocalDate.now());
            rfidAsset.setUpdatedBy(ApplicationSessions.id());
            this.rfidAssetServiceDomain.updateById(rfidAsset);
        }
        this.rfidAssetTakeDetailServiceDomain.updateById(detail);

        //主表是否 全部完成
        long count = rfidAssetTakeDetailServiceDomain.lambdaQuery().eq(RfidAssetTakeDetail::getTakeCode, detail.getTakeCode())
                .notIn(RfidAssetTakeDetail::getTakeStatus, Lists.newArrayList(TakeStatus.WAIT_DEAL, TakeStatus.EXECUTING))
                .ne(RfidAssetTakeDetail::getId, detail.getId()).count();
        if (count == 0L) {
            RfidAssetTake rfidAssetTake = this.rfidAssetTakeServiceDomain.lambdaQuery()
                    .eq(RfidAssetTake::getCode, detail.getTakeCode()).one();
            rfidAssetTake.setTakeStatus(TakeStatus.YES);
            rfidAssetTake.setStatus(DiscrepancyStatus.NORMAL);
            rfidAssetTake.setCompletedAt(LocalDateTime.now());
            rfidAssetTake.setUpdatedBy(ApplicationSessions.id());
            rfidAssetTakeServiceDomain.updateById(rfidAssetTake);
        }
        List<AssetApplyDto> assetApplyDtos = this.buildFlowDto(detail);
        Long oriReaderId1 = oriReaderId;
        ExecutorUtils.doAfterCommit(() -> {
            //盘点核算
            rfidAssetTakeService.repeatComplete(detail.getTakeCode());
            //处理业务记录
            if (CollectionUtils.isNotEmpty(assetApplyDtos)) {
                assetApplyDtos.forEach(assetApplyDto -> {
                    rfidAssetFlowService.apply(assetApplyDto);
                });
            }
            //基站变更下发
            if (detail.getChangeLocation() != null) {
                if (oriReaderId1 != null) {
                    rfidReaderService.sendAsset(oriReaderId1);
                }
                rfidReaderService.sendAsset(rfidAssetTakeDetailInfoVo.getReaderId());
            }
        });
        return Boolean.TRUE;
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean notice(MailSendDto mailSendDto) {
        mailRecordService.send(mailSendDto);
        RfidAssetTakeDetail rfidAssetTakeDetail = rfidAssetTakeDetailServiceDomain.getById(mailSendDto.getBusinessId());
        rfidAssetTakeDetail.setTakeStatus(TakeStatus.YES);
        rfidAssetTakeDetail.setProcessType(ProcessType.NOTICE);
        rfidAssetTakeDetail.setUpdatedBy(ApplicationSessions.id());
        rfidAssetTakeDetailServiceDomain.updateById(rfidAssetTakeDetail);
        ExecutorUtils.doAfterCommit(() -> {
            rfidAssetTakeService.repeatComplete(rfidAssetTakeDetail.getTakeCode());
        });
        return Boolean.TRUE;
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean instore(RfidAsset rfidAsset, Long id) {
        //保存资产
        rfidAsset.setDataSource(2);
        rfidAsset.setInStatus(true);
        rfidAsset.setStatus(AssetApplyStatus.IN_STOCK);
        rfidAsset.setId(null);
        rfidAssetService.stash(rfidAsset);
        RfidAssetTakeDetail rfidAssetTakeDetail = rfidAssetTakeDetailServiceDomain.getById(id);
        rfidAssetTakeDetail.setAssetId(rfidAsset.getId());
        rfidAssetTakeDetail.setProcessType(ProcessType.INSTORE);
        rfidAssetTakeDetail.setTakeStatus(TakeStatus.YES);
        rfidAssetTakeDetail.setAssetId(rfidAsset.getId());
        rfidAssetTakeDetail.setHasTag(rfidAsset.getHasTag());
        rfidAssetTakeDetail.setIsTake(rfidAsset.getIsTake());
        rfidAssetTakeDetail.setIsTakeStatis(rfidAsset.getIsTakeStatis());
        rfidAssetTakeDetail.setRfidCode(rfidAsset.getRfidCode());
        rfidAssetTakeDetail.setPrice(rfidAsset.getPrice());
        rfidAssetTakeDetail.setManagerDeptId(rfidAsset.getManagerDeptId());
        rfidAssetTakeDetail.setManagerDeptName(rfidAsset.getManagerDeptName());
        rfidAssetTakeDetail.setManagerId(rfidAsset.getManagerId());
        rfidAssetTakeDetail.setManagerName(rfidAsset.getManagerName());
        rfidAssetTakeDetail.setDepartmentId(rfidAsset.getDepartmentId());
        rfidAssetTakeDetail.setDepartmentName(rfidAsset.getDepartmentName());
        rfidAssetTakeDetail.setApplyId(rfidAsset.getApplyId());
        rfidAssetTakeDetail.setApplyName(rfidAsset.getApplyName());
        rfidAssetTakeDetail.setLocation(rfidAsset.getLocation());
        rfidAssetTakeDetail.setLocationId(rfidAsset.getLocationId());
        return rfidAssetTakeDetailServiceDomain.updateById(rfidAssetTakeDetail);
    }


    /**
     * 导出资产盘点明细数据
     *
     * @param response
     * @return
     */
    public Boolean downloadDetailData(HttpServletResponse response, RfidAssetTakeDetailQuery pageQuery) {
        try {
            //查询数据
            List<RfidAssetTakeDetailVo> excelList = this.rfidAssetTakeServiceDomain.findAssetTakeDetailList(pageQuery);
            excelList.forEach(item -> {
                List<Long> rangeInfos = item.getRangeInfo();
                String rangeStr = "";
                switch (item.getTakeRange()) {
                    case DEPARTMENT:
                        List<DepartmentInfo> departments = this.departmentInfoDomainService.lambdaQuery()
                                .select(DepartmentInfo::getName)
                                .in(DepartmentInfo::getId, rangeInfos)
                                .list();
                        rangeStr = departments.stream().map(v -> v.getName().getValue()).collect(Collectors.joining(","));
                        break;
                    case USER:
                        List<UserInfo> userInfos = this.userInfoDomainService.lambdaQuery()
                                .select(UserInfo::getName)
                                .in(UserInfo::getId, rangeInfos)
                                .list();
                        rangeStr = userInfos.stream().map(v -> v.getName()).collect(Collectors.joining(","));
                        break;
                    case LOCATION:
                        List<Location> locations = this.locationDomainService.lambdaQuery()
                                .select(Location::getFullName)
                                .in(Location::getId, rangeInfos)
                                .list();
                        rangeStr = locations.stream().map(v -> v.getFullName()).collect(Collectors.joining(","));
                        break;
                    case ALL:
                        rangeStr = "";
                        break;
                }
                item.setRangeInfoName(rangeStr);
            });
            //页面下载设置
            DownloadResponseUtil.addDownLoadHeader(response, "资产盘点明细数据.xlsx");
            //将并添加信息，实体类结构，类的数据做成excel表格对象workbook
            Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams(), RfidAssetTakeDetailVo.class, excelList);

            workbook.write(response.getOutputStream());
        } catch (IOException e) {
            e.printStackTrace();
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    /**
     * 构建业务申请流程
     *
     * @param rfidAssetTakeDetail
     * @return
     */
    public List<AssetApplyDto> buildFlowDto(RfidAssetTakeDetail rfidAssetTakeDetail) {
        List<AssetApplyDto> assetApplyDtos = Lists.newArrayList();

        AssetApplyDto assetApplyDto = new AssetApplyDto();
        assetApplyDto.setAutoAudit(true);
        switch (rfidAssetTakeDetail.getProcessType()) {
            case CHANGE: //变更
                assetApplyDto.setOperateType(AssetBusinessType.APPLY);
                assetApplyDto.setApplyId(rfidAssetTakeDetail.getChangeApplyId());
                //原保管人不为空 需要多走一条退库流程
                if (rfidAssetTakeDetail.getApplyId() != null) {
                    AssetApplyDto returnApply = new AssetApplyDto();
                    returnApply.setAutoAudit(true);
                    returnApply.setOperateType(AssetBusinessType.RETURN);
                    returnApply.setIds(Lists.newArrayList(rfidAssetTakeDetail.getAssetId()));
                    returnApply.setApplyAt(LocalDateTime.now());
                    returnApply.setApplyId(rfidAssetTakeDetail.getApplyId());
                    returnApply.setApproveId(ApplicationSessions.id());
                    returnApply.setApproveName(ApplicationSessions.name());
                    returnApply.setRemark("盘点单" + rfidAssetTakeDetail.getTakeCode() + "发起");
                    assetApplyDtos.add(returnApply);
                }
                break;
            case DAMAGE:     //损坏
                assetApplyDto.setOperateType(AssetBusinessType.BREAKAGE);
                assetApplyDto.setApplyId(ApplicationSessions.id());
                break;
            case INSTORE:     //报溢
                assetApplyDto.setOperateType(AssetBusinessType.OVERFLOW);
                assetApplyDto.setApplyId(ApplicationSessions.id());
                break;
            default:
                return null;
        }
        assetApplyDto.setIds(Lists.newArrayList(rfidAssetTakeDetail.getAssetId()));
        assetApplyDto.setApplyAt(LocalDateTime.now());
        assetApplyDto.setApproveId(ApplicationSessions.id());
        assetApplyDto.setApproveName(ApplicationSessions.name());
        assetApplyDto.setRemark("盘点单" + rfidAssetTakeDetail.getTakeCode() + "发起");
        assetApplyDtos.add(assetApplyDto);
        return assetApplyDtos;
    }
}
