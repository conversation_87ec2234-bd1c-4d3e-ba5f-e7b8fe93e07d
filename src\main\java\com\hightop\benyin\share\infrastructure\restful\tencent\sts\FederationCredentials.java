package com.hightop.benyin.share.infrastructure.restful.tencent.sts;

import com.fasterxml.jackson.annotation.JsonAlias;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 临时凭证信息
 * @Author: X.S
 * @date 2023/10/25 19:44
 */
@Data
public class FederationCredentials {
    @JsonAlias("Token")
    @ApiModelProperty("令牌")
    String token;
    @JsonAlias("TmpSecretId")
    @ApiModelProperty("临时证书密钥id")
    String tmpSecretId;
    @JsonAlias("TmpSecretKey")
    @ApiModelProperty("临时证书密钥")
    String tmpSecretKey;
    @ApiModelProperty("临时访问凭证有效的时间")
    Long expiredTime;

    public FederationCredentials withExpiredTime(Long expiredTime) {
        this.expiredTime = expiredTime;

        return this;
    }
}
