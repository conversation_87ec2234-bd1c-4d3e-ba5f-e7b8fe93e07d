package com.hightop.benyin.rfid.application.vo.dto;

import com.hightop.benyin.share.socket.enums.CommandType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Description: 资产领用DTO
 * @Author: X.S
 * @Date: 2023/12/22 17:16
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@ApiModel("下发指令DTO")
public class CommandDto {

    @ApiModelProperty("基站编码")
    Long readerId;

    @ApiModelProperty("部门id")
    Long departmentId;

    @ApiModelProperty("ip地址")
    String ipAddr;

    @ApiModelProperty("命令")
    @NotNull(message = "下发指令不能为空")
    CommandType commandType;


    @ApiModelProperty("基站编码集合")
    List<Long> readerIds;

    @ApiModelProperty("部门id集合")
    List<Long> departmentIds;

}
