package com.hightop.benyin.rfid.application.listener;


import com.hightop.benyin.rfid.application.service.RfidVariationService;
import com.hightop.benyin.rfid.domain.event.AssetVariationEvent;
import com.hightop.fario.base.util.CollectionUtils;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

/**
 * 资产异动监听器
 */
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Service
@Slf4j
public class AssetVariationListener {

    RfidVariationService rfidVariationService;

    @EventListener
    public void onVariation(AssetVariationEvent event) {
        if(CollectionUtils.isEmpty(event.getRfidInfos())){
            log.info("{}资产异动 数量为0！",event.getDeviceId());
            return;
        }
        log.info("{}开始上报资产异动 数量: {}！", event.getDeviceId(),event.getRfidInfos().size());
        boolean success = rfidVariationService.save(event.getRfidInfos());
        if (success) {
            log.info("{}上报资产异动成功！", event.getDeviceId());
        }
    }
}
