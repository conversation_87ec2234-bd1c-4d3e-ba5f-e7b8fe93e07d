package com.hightop.benyin.system.infrastructure.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hightop.benyin.configurer.annotation.RecordLog;
import com.hightop.benyin.configurer.annotation.RecordLogField;
import com.hightop.benyin.system.infrastructure.enums.BusinessType;
import com.hightop.fario.base.util.UuidUtils;
import com.hightop.fario.common.crypto.digest.Sm3Digest;
import com.hightop.magina.standard.cipher.CipherText;
import com.hightop.magina.standard.ums.user.privacy.UserPrivacy;
import com.hightop.magina.standard.ums.user.privacy.UserSex;
import io.swagger.annotations.ApiModel;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.experimental.FieldDefaults;

import java.time.LocalDateTime;
import java.util.Objects;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@Accessors(chain = true)
@ApiModel
@TableName("st_user_privacy")
@RecordLog(businessType = BusinessType.USER)
public class UserPrivacyInfo {
    /**
     * 用户id
     */
    @TableId(value = "id", type = IdType.NONE)
    Long id;
    /**
     * 头像base64
     */
    @TableField("head_image")
    String headImage;
    /**
     * 密码摘要
     */
    @TableField("password")
    String password;
    /**
     * 密码盐
     */
    @TableField("salt")
    String salt;
    /**
     * 邮箱
     */
    @TableField(value = "email")
    @RecordLogField(value = "邮箱")
    CipherText email;
    /**
     * 电话号码
     */
    @TableField("mobile_number")
    @RecordLogField(value = "电话号码")
    CipherText mobileNumber;
    /**
     * 身份证号码
     */
    @TableField("identity_card_number")
    @RecordLogField(value = "身份证号码")
    CipherText identityCardNumber;
    /**
     * 性别
     */
    @TableField("sex")
    @RecordLogField(value = "性别")
    UserSex sex;
    /**
     * 上次修改密码时间
     */
    @TableField("last_change_password_at")
    LocalDateTime lastChangePasswordAt;

    /**
     * 比较当前密码和目标密码是否一致
     *
     * @param target 目标密码
     * @return true/false
     */
    public boolean isPasswordMatch(String target) {
        return Objects.equals(this.password, UserPrivacyInfo.hash(this.id, target, this.salt));
    }

    /**
     * 初始化hash盐及密码
     *
     * @return {@link UserPrivacy}
     */
    public UserPrivacyInfo initPassword() {
        if (Objects.isNull(this.salt)) {
            this.setSalt(UuidUtils.withoutMinus());
        }

        return this.hashPassword();
    }

    /**
     * 密码has
     *
     * @return {@link UserPrivacy}
     */
    public UserPrivacyInfo hashPassword() {
        Objects.requireNonNull(this.id);
        Objects.requireNonNull(this.password);
        Objects.requireNonNull(this.salt);

        return this.setPassword(UserPrivacyInfo.hash(this.id, this.password, this.salt));
    }

    /**
     * 密码哈希算法
     *
     * @param id       用户id
     * @param password 明文密码
     * @param salt     盐值
     * @return 密码哈希值
     */
    protected static String hash(Long id, String password, String salt) {
        // 国密sm3哈希算法
        return Sm3Digest.of().update(String.format("%d;%s;%s", id, password, salt)).hexLower();
    }
}
