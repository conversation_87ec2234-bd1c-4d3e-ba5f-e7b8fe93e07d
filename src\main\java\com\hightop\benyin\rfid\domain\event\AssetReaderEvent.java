package com.hightop.benyin.rfid.domain.event;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

/**
 * 资产基站心跳上报 事件
 *
 * <AUTHOR>
 * @date 2024/10/16 14:55
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@AllArgsConstructor
@NoArgsConstructor
public class AssetReaderEvent {

    /**
     * 心跳上报消息
     */
    String hearbeatMsg;

    /**
     * 心跳上报消息
     */
    String client;
}
