package com.hightop.benyin.system.api.vo.excel;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelIgnore;
import com.hightop.benyin.share.application.vo.ExcelBaseInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

@Data
@ApiModel("资产类型信息导入模板")
public class AssetTypeExcel extends ExcelBaseInfo {


    @Excel(name = "资产类型编码", width = 30, orderNum = "0")
    @NotBlank
    @Size(min = 1, max = 64, message = "资产类型编码长度在{min}至{max}之间")
    String code;

    @Excel(name = "资产类型名称", width = 30, orderNum = "1")
    @NotBlank
    @Size(min = 1, max = 64, message = "资产类型名称长度在{min}至{max}之间")
    String name;

    @Excel(name = "上级类型名称", width = 30, orderNum = "3")
    @ApiModelProperty("上级类型名称")
    String parentName;

    @Excel(name = "上级类型编码", width = 30, orderNum = "２")
    @ApiModelProperty("上级类型编码")
    @Size(min = 1, max = 64, message = "上级资产类型编码长度在{min}至{max}之间")
    String parentCode;

    @ApiModelProperty("贴标签类型(是否)")
    @Excel(name = "是否贴标签", width = 20, orderNum = "4")
    String hasTagStr;

    @ApiModelProperty("是否扫描")
    @Excel(name = "是否扫描", width = 20, orderNum = "5")
    String isScanStr;

    @ApiModelProperty("是否上报异常")
    @Excel(name = "是否上报异常", width = 20, orderNum = "6")
    String isReportStr;

    @ApiModelProperty("是否盘点")
    @Excel(name = "是否盘点", width = 20, orderNum = "7")
    String isTakeStr;

    @ApiModelProperty("是否盘点统计")
    @Excel(name = "是否盘点统计", width = 20, orderNum = "8")
    String isTakeStatisStr;

    @ApiModelProperty("是否财务统计")
    @Excel(name = "是否财务统计", width = 20, orderNum = "9")
    String isStatisStr;

    @ApiModelProperty("是否启用")
    @Excel(name = "是否启用", width = 30, orderNum = "10")
    String isEnableStr;

    @ExcelIgnore
    Long parentId;
    @ExcelIgnore
    Long id;

    @ApiModelProperty("有无标签")
    @ExcelIgnore
    Boolean hasTag;

    @ApiModelProperty("是否扫描")
    @ExcelIgnore
    Boolean isScan;

    @ApiModelProperty("是否上报异常")
    @ExcelIgnore
    Boolean isReport;

    @ApiModelProperty("是否盘点")
    @ExcelIgnore
    Boolean isTake;

    @ApiModelProperty("盘点是否统计")
    @ExcelIgnore
    Boolean isTakeStatis;

    @ApiModelProperty("财务是否统计")
    @ExcelIgnore
    Boolean isStatis;

    @ApiModelProperty("是否启用")
    @ExcelIgnore
    Boolean isEnable;

}
