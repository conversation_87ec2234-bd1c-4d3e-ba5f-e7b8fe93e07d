package com.hightop.benyin.share.api.controller;

import com.hightop.benyin.share.application.dto.MailSendDto;
import com.hightop.benyin.share.application.query.MailRecordQuery;
import com.hightop.benyin.share.application.service.MailRecordService;
import com.hightop.benyin.share.infrastructure.entity.MailRecord;
import com.hightop.fario.base.web.Operation;
import com.hightop.fario.base.web.RestResponse;
import com.hightop.fario.common.mybatis.DataGrid;
import com.hightop.magina.standard.behavior.operation.IgnoreOperationLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping("/mail")
@RestController
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Api(tags = "RFID管理")
public class MailController {

    MailRecordService mailRecordService;

    @PostMapping()
    @ApiOperation("分页查询我的邮件")
    @IgnoreOperationLog
    public RestResponse<DataGrid<MailRecord>> page(@RequestBody MailRecordQuery pageQuery) {
        return RestResponse.ok(this.mailRecordService.page(pageQuery));
    }

    @PostMapping("/send")
    @ApiOperation("发送邮件")
    @IgnoreOperationLog
    public RestResponse<Void> send( @RequestBody @Validated MailSendDto mailSendDto) {
        return  Operation.UPDATE.response(this.mailRecordService.send(mailSendDto));
    }


}
