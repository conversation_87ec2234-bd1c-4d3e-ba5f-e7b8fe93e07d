package com.hightop.benyin.rfid.infrastructure.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.*;
import com.hightop.benyin.share.infrastructure.util.DictUtil;
import com.hightop.fario.common.jackson.annotation.JsonAmount;
import com.hightop.magina.standard.code.dictionary.bind.DictItemBind;
import com.hightop.magina.standard.code.dictionary.bind.DictItemEntry;
import com.hightop.magina.standard.ums.user.bind.UserBind;
import com.hightop.magina.standard.ums.user.bind.UserEntry;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 资产维修记录表
 *
 * <AUTHOR>
 * @date 2023-11-15 17:19:37
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@TableName("b_asset_repair")
@ApiModel
public class RfidAssetRepair {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @ApiModelProperty("id")
    Long id;

    @TableField(value = "code")
    @ApiModelProperty("code")
    String code;

    @TableField("asset_id")
    @ApiModelProperty("资产id")
    Long assetId;


    @TableField("images")
    @ApiModelProperty("故障图片")
    List<String> images;

    @TableField("description")
    @ApiModelProperty("故障描述")
    String description;

    @TableField("labor_amount")
    @ApiModelProperty("人工费")
    @JsonAmount
    Long labnorAmount;

    @TableField("consumable_amount")
    @ApiModelProperty("耗材费")
    @JsonAmount
    Long consumableAmount;

    @TableField("repair_course")
    @ApiModelProperty("维修详情")
    String repairCourse;

    @TableField("consumable")
    @ApiModelProperty("更换耗材")
    String consumable;

    @TableField("status")
    @ApiModelProperty("状态")
    Integer status;

    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty("最近更新时间")
    @Excel(name = "最近更新时间", width = 30, format = "yyyy/MM/dd HH:mm:ss", orderNum = "8")
    LocalDateTime updatedAt;

    @TableField(value = "updated_by")
    @ApiModelProperty("最近更新人")
    Long updatedBy;

    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @ApiModelProperty("创建时间")
    LocalDateTime createdAt;

    @TableField("created_by")
    @ApiModelProperty("创建人")
    @UserBind
    UserEntry createdBy;

    @TableField(exist = false)
    @ApiModelProperty("资产名称")
    String assetName;


    @TableField(exist = false)
    @ApiModelProperty("资产编码")
    String assetCode;


    @TableField(exist = false)
    @ApiModelProperty("规格")
    String model;


    @TableField(exist = false)
    @ApiModelProperty("价格")
    @JsonAmount
    Long price;

    @TableField(exist = false)
    @ApiModelProperty("资产小类")
    String assetType;

    @TableField(exist = false)
    @ApiModelProperty("资产类型")
    String assetTypeName;


    @TableField(exist = false)
    @ApiModelProperty("位置编码")
    String locationCode;

    @TableField(exist = false)
    @ApiModelProperty("原位置")
    String location;

    @TableField(exist = false)
    @ApiModelProperty("rfid编码")
    String rfidCode;

    @TableField(exist = false)
    @ApiModelProperty("所属单位")
    String departmentName;

}
