package com.hightop.benyin;

import com.hightop.benyin.rfid.application.service.RfidReaderService;
import com.hightop.benyin.share.application.service.MailRecordService;
import com.hightop.benyin.share.socket.common.SocketMessage;
import com.hightop.benyin.share.socket.enums.ActivateEnums;
import com.hightop.benyin.share.socket.enums.CommandType;
import com.hightop.benyin.share.socket.util.MsgUtil;
import com.hightop.benyin.share.socket.util.SocketUtil;
import com.hightop.fario.base.web.Operation;
import com.hightop.fario.base.web.RestResponse;
import com.hightop.fario.common.core.annotation.Anonymous;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.client.RestHighLevelClient;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 技术上匿名操作的接口
 * 主要作用用户初始化一下数据：如果相关开发做了其他非法操作，请根据GIT提交记录处理
 */
@RequestMapping({"/init/anno"})
@RestController
@Slf4j
public class InitController {

    @Resource
    private RestHighLevelClient restHighLevelClient;

    @Resource
    private RfidReaderService rfidReaderService;

    @Resource
    private MailRecordService mailSenderService;


    @PostMapping("/receive")
    @ApiOperation("接收消息")
    @Anonymous
    public RestResponse<Void> receive(@RequestParam("message") String message) {
        byte[]  bytes = MsgUtil.convertMessage(message);
        List<SocketMessage> socketMessageList = MsgUtil.getSocketMessageList(bytes, message);
        for (SocketMessage socketMessage : socketMessageList) {
            log.info("receive message:{}", socketMessage.toString());
        }
        return Operation.UPDATE.response(SocketUtil.sendToALL(new SocketMessage(message)));
    }


    @PutMapping("/sendAll")
    @ApiOperation("发送socket消息")
    @Anonymous
    public RestResponse<Void> send(@RequestParam("message") String message) {
        return Operation.UPDATE.response(SocketUtil.sendToALL(new SocketMessage(message)));
    }

    @PutMapping("/sendTo")
    @ApiOperation("单一发送socket消息")
    @Anonymous
    public RestResponse<Void> send(@RequestParam("deviceId") String deviceId, @RequestParam("message") String message) {
        return Operation.UPDATE.response(SocketUtil.send(deviceId, new SocketMessage(message)));
    }

    @PutMapping("/sendMail")
    @ApiOperation("邮件发送")
    @Anonymous
    public RestResponse<Void> sendMail(@RequestParam("to") String to, @RequestParam("title") String title, @RequestParam("content") String content) {
        mailSenderService.send(to, title, content);
        return Operation.UPDATE.response(true);

    }

    @PutMapping("/down")
    @ApiOperation("下发资产")
    @Anonymous
    public RestResponse<Void> down(@RequestParam("id") Long id) {
        return Operation.UPDATE.response(rfidReaderService.sendAsset(id));
    }


    @PutMapping("/take")
    @ApiOperation("一键盘点")
    @Anonymous
    public RestResponse<Void> take(@RequestParam("deviceId") String deviceId) {
        return Operation.UPDATE.response(SocketUtil.send(deviceId, new SocketMessage(CommandType.TAKE_STOCK, 1, MsgUtil.integerToHexTwo(ActivateEnums.OFF.getCode())))
        );
    }


    @PutMapping("/getAll")
    @ApiOperation("查看绑定标签数")
    @Anonymous
    public RestResponse<Void> getAll(@RequestParam("deviceId") String deviceId) {
        return Operation.UPDATE.response(SocketUtil.send(deviceId, new SocketMessage(CommandType.MANAGER_TAG, 1, MsgUtil.integerToHexTwo(ActivateEnums.ON.getCode()))));
    }


}
