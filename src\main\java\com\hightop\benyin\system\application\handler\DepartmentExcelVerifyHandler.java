package com.hightop.benyin.system.application.handler;

import cn.afterturn.easypoi.excel.entity.result.ExcelVerifyHandlerResult;
import cn.afterturn.easypoi.handler.inter.IExcelVerifyHandler;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.hightop.benyin.system.api.vo.excel.DepartmentExcel;
import com.hightop.benyin.system.domain.service.DepartmentInfoDomainService;
import com.hightop.benyin.system.infrastructure.entity.DepartmentInfo;
import com.hightop.fario.base.util.StringUtils;
import com.hightop.magina.core.component.ApplicationContexts;
import com.hightop.magina.standard.ums.user.basic.UserBasic;
import com.hightop.magina.standard.ums.user.basic.UserBasicDomainService;

import java.util.Objects;
import java.util.StringJoiner;

public class DepartmentExcelVerifyHandler implements IExcelVerifyHandler<DepartmentExcel> {
    @Override
    public ExcelVerifyHandlerResult verifyHandler(DepartmentExcel data) {
        DepartmentInfoDomainService departmentDomainService = ApplicationContexts.getBean(DepartmentInfoDomainService.class);
        UserBasicDomainService userBasicDomainService = ApplicationContexts.getBean(UserBasicDomainService.class);
        StringJoiner joiner = new StringJoiner(",");

        if (StringUtils.isNotBlank(data.getManagerCode())) {
            UserBasic userBasic = userBasicDomainService.lambdaQuery()
                    .eq(UserBasic::getCode, data.getManagerCode()).one();
            if (Objects.isNull(userBasic)) {
                joiner.add("负责人编码有误");
            } else {
                data.setManagerId(userBasic.getId());
            }
        }
        if (StringUtils.isNotBlank(data.getParentCode())) {
            DepartmentInfo department = departmentDomainService.lambdaQuery()
                    .eq(DepartmentInfo::getCode, data.getParentCode()).one();
            if (Objects.isNull(department)) {
//                joiner.add("上级部门编码有误");
            } else {
                data.setParentId(department.getId());
            }
        }
        // 校验部门编码是否存在 允许修改
        DepartmentInfo department = departmentDomainService.lambdaQuery()
                .eq(DepartmentInfo::getCode, data.getCode()).one();
        if (Objects.nonNull(department)) {
            data.setId(department.getId());
        } else {
            data.setId(IdWorker.getId());
        }

        String errorMsg = joiner.toString();
        if (StringUtils.isNotBlank(errorMsg)) {
            return new ExcelVerifyHandlerResult(false, errorMsg);
        }
        return new ExcelVerifyHandlerResult(true);
    }
}
