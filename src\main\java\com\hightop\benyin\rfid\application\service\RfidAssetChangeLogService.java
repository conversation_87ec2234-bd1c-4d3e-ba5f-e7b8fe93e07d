package com.hightop.benyin.rfid.application.service;

import com.github.yulichang.toolkit.MPJWrappers;
import com.google.common.collect.Lists;
import com.hightop.benyin.rfid.application.vo.dto.AssetChangeLogDto;
import com.hightop.benyin.rfid.application.vo.query.RfidAssetChangeLogQuery;
import com.hightop.benyin.rfid.domain.service.RfidAssetChangeLogServiceDomain;
import com.hightop.benyin.rfid.infrastructure.entity.RfidAsset;
import com.hightop.benyin.rfid.infrastructure.entity.RfidAssetChangeLog;
import com.hightop.benyin.rfid.infrastructure.entity.RfidReader;
import com.hightop.fario.base.util.StringUtils;
import com.hightop.fario.common.mybatis.DataGrid;
import com.hightop.fario.common.mybatis.util.PageHelper;
import com.hightop.magina.standard.ums.user.bind.UserEntry;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 资产变更日志service服务
 *
 * <AUTHOR>
 * @date 2024-10-12 16:36:01
 */
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Service
public class RfidAssetChangeLogService {

    RfidAssetChangeLogServiceDomain assetChangeLogServiceDomain;

    /**
     * 分页查询资产变更日志
     *
     * @param pageQuery
     * @return
     */
    public DataGrid<RfidAssetChangeLog> pageList(RfidAssetChangeLogQuery pageQuery) {
        return PageHelper.startPage(pageQuery, p ->
                        this.assetChangeLogServiceDomain.selectJoinList(RfidAssetChangeLog.class, MPJWrappers.lambdaJoin()
                                .selectAll(RfidAssetChangeLog.class)
                                .selectAs(RfidAsset::getRfidCode, RfidAssetChangeLog::getRfidCode)
                                .selectAs(RfidAsset::getCode, RfidAssetChangeLog::getAssetCode)
                                .selectAs(RfidAsset::getName, RfidAssetChangeLog::getAssetName)
                                .leftJoin(RfidAsset.class, RfidAsset::getId, RfidAssetChangeLog::getAssetId)
                                .leftJoin(RfidReader.class, RfidReader::getId, RfidAsset::getReaderId)
                                .eq(pageQuery.getAssetId() != null, RfidAssetChangeLog::getAssetId, pageQuery.getAssetId())
                                .like(StringUtils.isNotBlank(pageQuery.getAssetName()), RfidAsset::getName, pageQuery.getAssetName())
                                .like(StringUtils.isNotBlank(pageQuery.getRfidCode()), RfidAsset::getRfidCode, pageQuery.getRfidCode())
                                .like(StringUtils.isNotBlank(pageQuery.getAssetCode()), RfidAsset::getCode, pageQuery.getAssetCode())
                                .like(StringUtils.isNotBlank(pageQuery.getReaderCode()), RfidReader::getCode, pageQuery.getReaderCode())
                                .like(StringUtils.isNotBlank(pageQuery.getOperationType()), RfidAssetChangeLog::getOperationType, pageQuery.getOperationType())
                                .like(StringUtils.isNotBlank(pageQuery.getChangeCode()), RfidAssetChangeLog::getChangeCode, pageQuery.getChangeCode())
                                .like(StringUtils.isNotBlank(pageQuery.getSource()), RfidAssetChangeLog::getSource, pageQuery.getSource())
                                .ge(StringUtils.isNotBlank(pageQuery.getStartDate()), RfidAssetChangeLog::getCreatedAt, pageQuery.getStartDate() + " 00:00:00")
                                .le(StringUtils.isNotBlank(pageQuery.getEndDate()), RfidAssetChangeLog::getCreatedAt, pageQuery.getEndDate() + " 23:59:59")
                                .orderByDesc(RfidAssetChangeLog::getCreatedAt))
                );
    }

    /**
     * 保存资产变更日志
     *
     * @param changeLogDtoList
     * @return
     */
    public boolean saveChangeLog(List<AssetChangeLogDto> changeLogDtoList) {
        List<RfidAssetChangeLog> assetChangeLogs = Lists.newArrayList();
        for (AssetChangeLogDto changeLogDto : changeLogDtoList) {
            RfidAssetChangeLog assetChangeLog = new RfidAssetChangeLog();
            assetChangeLog.setAssetId(changeLogDto.getAssetId());
            assetChangeLog.setBeforeData(changeLogDto.getBefore());
            assetChangeLog.setAfterData(changeLogDto.getAfter());
            assetChangeLog.setOperationType(changeLogDto.getOperatType());
            assetChangeLog.setSource(changeLogDto.getSource());
            assetChangeLog.setChangeCode(changeLogDto.getChangeCode());
            if (changeLogDto.getCreatedBy() != null) {
                assetChangeLog.setCreatedBy(new UserEntry().setId(changeLogDto.getCreatedBy()));
            }
            assetChangeLogs.add(assetChangeLog);
        }
        return assetChangeLogServiceDomain.saveBatch(assetChangeLogs);
    }


}
