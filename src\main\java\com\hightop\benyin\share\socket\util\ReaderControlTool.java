package com.hightop.benyin.share.socket.util;

import com.hightop.benyin.rfid.infrastructure.entity.RfidReader;
import com.hightop.benyin.share.infrastructure.util.DictUtil;
import com.hightop.benyin.share.infrastructure.util.RedisLockUtils;
import com.hightop.benyin.share.socket.common.SocketMessage;
import com.hightop.benyin.share.socket.enums.ActivateEnums;
import com.hightop.benyin.share.socket.enums.CommandType;
import com.hightop.benyin.share.socket.common.SocketStation;
import com.hightop.magina.core.exception.MaginaException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.concurrent.TimeUnit;

@Slf4j
public class ReaderControlTool {
    public static volatile int totalCount = 1;

    /**
     * 重置 复位设备
     *
     * @param deviceId 设备id
     * @return
     */
    public static boolean reset(String deviceId) {
        SocketUtil.send(deviceId, new SocketMessage(CommandType.REST, 0, null));
        return true;
    }

    /**
     * 擦除配置数据
     *
     * @param deviceId 设备id
     * @return
     */
    public static boolean clear(String deviceId) {
        SocketUtil.send(deviceId, new SocketMessage(CommandType.CLEAR, 0, null));
        return true;
    }

    /**
     * 设置网络连接
     *
     * @param deviceId 设备id
     * @return
     */
    public static boolean setNetwork(String deviceId,Integer ssidLength,String ssid ,Integer passwordLength,String password,Integer ipLength,String ip,String port) {
        String ssidStr = MsgUtil.integerToHexTwo(ssidLength);
        String passwordStr = MsgUtil.integerToHexTwo(passwordLength);
        String ipLengthStr = MsgUtil.integerToHexTwo(passwordLength);
        Integer lenth = ssidLength+ipLength+ipLength;
        SocketUtil.send(deviceId, new SocketMessage(CommandType.SET_NET_SETTINGS, lenth+7, ssidStr+ssid+passwordStr+password+ipLengthStr+ip+port));
        return true;
    }


    /**
     * 设置基站的序列号
     *
     * @param deviceId 设备id
     * @return
     */
    public static boolean setDeviceId(String deviceId,String newDeviceId) {
        SocketUtil.send(deviceId, new SocketMessage(CommandType.SET_DEVICEID, 4, newDeviceId));
        return true;
    }

    /**
     * 节能模式
     *
     * @param deviceId 设备id
     * @param status   状态 1关闭2 开启
     * @return
     */
    public static boolean dormancy(String deviceId, int status) {
        String strStatus = MsgUtil.integerToHexTwo(status);
        SocketUtil.send(deviceId, new SocketMessage(CommandType.SET_DORMANCY, 1, strStatus));
        return true;
    }


    /**
     * 设置读卡器的自动空闲状态
     *
     * @param deviceId 设备id
     * @param status   状态 1关闭2 开启
     * @return
     */
    public static boolean setAutoFree(String deviceId, int status) {
        String strStatus = MsgUtil.integerToHexTwo(status);
        SocketUtil.send(deviceId, new SocketMessage(CommandType.SET_LEISURE, 1, strStatus));
        return true;
    }


    /**
     * 设置空闲时间/描时间的参数
     *
     * @param deviceId 设备id
     * @param freeTime  空间时间
     * @param scanTime  扫描时间
     * @return
     */
    public static boolean setFreeTime(String deviceId, int freeTime,int scanTime) {
        String freeTimeStr = MsgUtil.integerToHexTwo(freeTime);
        String scanTimeStr = MsgUtil.integerToHexTwo(scanTime);

        SocketUtil.send(deviceId, new SocketMessage(CommandType.SET_SCAN_TIME, 2, freeTimeStr+scanTimeStr));
        return true;
    }

    /**
     * 蜂鸣器
     *
     * @param deviceId 设备id
     * @param status   状态 1关闭2 开启
     * @return
     */
    public static boolean bell(String deviceId, int status) {
        String strStatus = MsgUtil.integerToHexTwo(status);
        SocketUtil.send(deviceId, new SocketMessage(CommandType.SET_BELL, 1, strStatus));
        return true;
    }


    /**
     * 设置透传开关的状态
     *
     * @param deviceId 设备id
     * @param status   状态 1关闭2 开启
     * @return
     */
    public static boolean setDisrect(String deviceId, int status) {
        String strStatus = MsgUtil.integerToHexTwo(status);
        SocketUtil.send(deviceId, new SocketMessage(CommandType.SET_DIRECT, 1, strStatus));
        return true;
    }


    /**
     * 打开扫描状态
     * 对比上报数据type 0x02
     *
     * @param deviceId 设备id
     * @param interval 扫描间隔
     * @return
     */
    public static boolean openScan(String deviceId, int interval, int duration) {
        String intervalStr = MsgUtil.intToHex(interval);      // 双字节
        String durationStr = MsgUtil.intToHex(duration);      // 双字节  
        String status = MsgUtil.integerToHexTwo(DictUtil.ENABLE);
        String type = MsgUtil.integerToHexTwo(DictUtil.ON);  // 根据注释应该是OFF
        SocketUtil.send(deviceId, new SocketMessage(CommandType.SET_SCAN_STATE, 6, intervalStr + durationStr + status + type));
        return true;
    }


    /**
     * 设置心跳间隔
     *
     * @param deviceId 设备id
     * @param interval 心跳间隔
     * @return
     */
    public static boolean setHeatbeat(String deviceId, int interval) {
        String strInterval = MsgUtil.intToHex(interval);
        String status = MsgUtil.integerToHexTwo(1); 
        SocketUtil.send(deviceId, new SocketMessage(CommandType.SET_HEARTBEAT, 3, strInterval + status));
        return true;
    }


    /**
     * 关闭扫描状态
     *
     * @param deviceId 设备id
     * @return
     */
    public static boolean closeScan(String deviceId, int interval, int duration) {
//        String status = MsgUtil.integerToHexTwo(DictUtil.DISABLE);
//        SocketUtil.send(deviceId, new SocketMessage(CommandType.SET_SCAN_STATE, 1, status));
        String intervalStr = MsgUtil.intToHex(interval);      // 双字节
        String durationStr = MsgUtil.intToHex(duration);      // 双字节
        String status = MsgUtil.integerToHexTwo(DictUtil.DISABLE);
        String type = MsgUtil.integerToHexTwo(DictUtil.OFF);
        SocketUtil.send(deviceId, new SocketMessage(CommandType.SET_SCAN_STATE, 6, intervalStr + durationStr + status + type));
        return true;
    }

    /**
     * 发送无参数命令
     *
     * @param deviceId 设备id
     * @return
     */
    public static boolean sendCommand(String deviceId,CommandType commandType ) {
        log.info("sendCommand commandType:{}",commandType.getName());
        return  SocketUtil.send(deviceId, new SocketMessage(commandType, 0, null));
    }


    /**
     * 配置集中扫描参数
     * 自动上报一次所有数据type 0x01
     *
     * @param deviceId 设备id
     * @param interval 扫描间隔
     * @return
     */
    public static boolean openFocusScan(String deviceId, int interval, int duration) {
        String intervalStr = MsgUtil.intToHex(interval);      // 双字节
        String durationStr = MsgUtil.intToHex(duration);      // 双字节
        // 参数顺序: intervalStr + durationStr
        // 数据长度: 4字节
        SocketUtil.sendScan(deviceId, new SocketMessage(CommandType.SET_FOCUS_SCAN, 4, intervalStr + durationStr));
        return true;
    }

    /**
     * 查询绑定标签
     *
     * @param deviceId 设备id
     * @return
     */
    public static boolean searchTag(String deviceId) {
        return SocketUtil.send(deviceId, new SocketMessage(CommandType.MANAGER_TAG, 0, null));
    }


    /**
     * 发送扫描命令
     *
     * @param deviceId     设备id
     * @param rfidReader   扫描次数
     * @param scanType     扫描类型
     * @param businessCode 关联业务编码
     * @return
     */
    public static boolean sendScan(RedisTemplate redisTemplate, String deviceId, RfidReader rfidReader, String scanType, String businessCode) {
        String lockKey = DictUtil.LOCK + deviceId;
        Boolean lock = RedisLockUtils.lock(lockKey,businessCode, 10);
        if (!lock) {
            throw new MaginaException("设备正在扫描中，请稍候重试！");
        }

        SocketStation socketStation = SocketUtil.getSocketStation(deviceId);
        if (socketStation == null) {
            throw new MaginaException("设备已掉线，请检查设备是否正常连接！");
        }
        String key = DictUtil.SCAN_CACHE + DictUtil.STATUS + businessCode + ":" + deviceId;
        if (redisTemplate.hasKey(key)) {
            redisTemplate.delete(key);
        }
        redisTemplate.opsForValue().set(key, DictUtil.SEND, 30, TimeUnit.MINUTES);

        String scanCodeKey = DictUtil.SCAN_CACHE + DictUtil.SCAN_CODE + deviceId;
        String scanTypeKey = DictUtil.SCAN_CACHE + DictUtil.SCAN_TYPE + deviceId;
        if (redisTemplate.hasKey(scanCodeKey)) {
            redisTemplate.delete(scanCodeKey);
        }
        if (redisTemplate.hasKey(scanTypeKey)) {
            redisTemplate.delete(scanTypeKey);
        }
        redisTemplate.opsForValue().set(scanCodeKey, businessCode, 30, TimeUnit.MINUTES);
        redisTemplate.opsForValue().set(scanTypeKey, scanType, 30, TimeUnit.MINUTES);

        //等待时长为扫描次数*扫描间隔+当前日常扫描间隔
        int retries = 0;
        while (retries < 3) {
            try {
                //发送集中扫描命令
                boolean result =  SocketUtil.send(deviceId, new SocketMessage(CommandType.TAKE_STOCK, 0, null));
                if (result) {
                    redisTemplate.opsForValue().set(key, DictUtil.RECEIVING, 30, TimeUnit.MINUTES);
                    log.info("{}发送集中扫描命令成功！", deviceId);
                    break;
                }
                Thread.sleep(2000);
            } catch (Exception e) {
                e.printStackTrace();
            }
            retries++;
        }
        return true;
    }


}
