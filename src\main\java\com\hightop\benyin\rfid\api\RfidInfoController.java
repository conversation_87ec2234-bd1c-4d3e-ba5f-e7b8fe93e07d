package com.hightop.benyin.rfid.api;

import com.hightop.benyin.rfid.application.service.RfidAssetTakeService;
import com.hightop.benyin.rfid.application.service.RfidInfoService;
import com.hightop.benyin.rfid.application.vo.dto.AssetScanDto;
import com.hightop.benyin.rfid.application.vo.query.RfidInfoQuery;
import com.hightop.benyin.rfid.infrastructure.entity.RfidAsset;
import com.hightop.benyin.rfid.infrastructure.entity.RfidInfo;
import com.hightop.benyin.share.infrastructure.util.DictUtil;
import com.hightop.fario.base.web.Operation;
import com.hightop.fario.base.web.RestResponse;
import com.hightop.fario.common.mybatis.DataGrid;
import com.hightop.magina.standard.behavior.operation.IgnoreOperationLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * RFID管理rest接口
 *
 * <AUTHOR>
 * @date 2024-10-12 16:36:01
 */
@RequestMapping("/rfid-info")
@RestController
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Api(tags = "RFID管理")
public class RfidInfoController {
    RfidInfoService rfidInfoService;
    RfidAssetTakeService rfidAssetTakeService;

    @PostMapping()
    @ApiOperation("分页查询")
    @IgnoreOperationLog
    public RestResponse<DataGrid<RfidInfo>> page(@RequestBody RfidInfoQuery pageQuery) {
        return RestResponse.ok(this.rfidInfoService.page(pageQuery));
    }

    @PostMapping("getList")
    @ApiOperation("根据位置查询扫描结果")
    @IgnoreOperationLog
    public RestResponse<List<RfidInfo>> getRfidInfoList(@RequestBody RfidInfoQuery pageQuery) {
        return RestResponse.ok(this.rfidInfoService.getRfidInfoList(pageQuery));
    }

    @GetMapping("getBindAssetList/{deviceId}")
    @ApiOperation("查询已绑定当前基站的资产列表")
    public RestResponse<List<RfidAsset>> getBindAssetList(@PathVariable @ApiParam("deviceId") String deviceId) {
        return RestResponse.ok(this.rfidInfoService.getBindAssetList(deviceId));
    }

    @GetMapping("bind/{readerId}")
    @ApiOperation("获取绑定码")
    @IgnoreOperationLog
    public RestResponse<String> bind(@PathVariable @ApiParam("readerId") Long readerId) {
        return RestResponse.ok(this.rfidInfoService.getBindCode(readerId));
    }


    @PostMapping("scan/{readerId}")
    @ApiOperation("执行扫描")
    @IgnoreOperationLog
    public RestResponse<Void> sendScan(@PathVariable @ApiParam("readerId") Long readerId) {
        return Operation.UPDATE.response(this.rfidInfoService.sendScan(readerId));
    }

    @GetMapping("isComplete/{bindCode}/{readerId}")
    @ApiOperation("是否完成扫描")
    @IgnoreOperationLog
    public RestResponse<Boolean> isCompleted(
            @PathVariable @ApiParam("bindCode") String bindCode, @PathVariable @ApiParam("count") Long readerId) {
        return RestResponse.ok(this.rfidInfoService.isCompleted(DictUtil.BIND_SCAN, bindCode, readerId));
    }

    @PostMapping("receiveHandler")
    @ApiOperation("接收扫描数据")
    @IgnoreOperationLog
    public RestResponse<Void> receiveHandler(@RequestBody @Validated AssetScanDto assetScanDto) {
        return Operation.UPDATE.response(this.rfidInfoService.receiveHandler(assetScanDto));
    }

    @DeleteMapping("/{readerId}/{bindCode}")
    @ApiOperation("删除数据")
    @IgnoreOperationLog
    public RestResponse<Void> deleteCode(@PathVariable @ApiParam("readerId") Long readerId,@PathVariable @ApiParam("bindCode") String bindCode) {
        return Operation.DELETE.response(this.rfidAssetTakeService.deleteReaderCache(readerId, DictUtil.BIND_SCAN,bindCode));
    }


    @DeleteMapping("clearBindList/{deviceId}")
    @ApiOperation("删除获取到的绑定数据缓存")
    @IgnoreOperationLog
    public RestResponse<Void> clearBindList(@PathVariable @ApiParam("deviceId") String deviceId) {
        return Operation.DELETE.response(this.rfidInfoService.clearBindList(deviceId));
    }


    @DeleteMapping("clear")
    @ApiOperation("清理上报数据")
    public RestResponse<Void> delete() {
        return Operation.UPDATE.response(this.rfidInfoService.clear());
    }


    /**
     * 导出标签上报数据
     *
     * @param response
     * @param pageQuery
     * @return
     */
    @ApiOperation("/导出标签上报数据")
    @GetMapping("/export")
    public RestResponse<Void> export(HttpServletResponse response, RfidInfoQuery pageQuery) throws IOException {
        Boolean b = rfidInfoService.downloadData(response, pageQuery);
        if (!b) {
            return new RestResponse(500, "导出失败，请联系技术人员！", null, null);
        }
        return RestResponse.message("导出成功");
    }

}
