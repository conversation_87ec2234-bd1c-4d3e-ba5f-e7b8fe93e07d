package com.hightop.benyin.rfid.application.vo.query;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.hightop.fario.common.mybatis.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldDefaults;

import java.util.List;

/**
 * 资产变动查询DTO
 *
 * <AUTHOR>
 * @date 2024-10-12 16:36:01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@FieldDefaults(level = AccessLevel.PRIVATE)
@ApiModel("rfid变动信息查询DTO")
public class RfidVariationQuery extends PageQuery {

    @ApiModelProperty("异动编码")
    String code;

    @ApiModelProperty("异动编码")
    String variationCode;

    @ApiModelProperty("编码")
    String readerCode;

    @ApiModelProperty("基站设备id")
    String deviceId;

    @ApiModelProperty("rfid编码")
    String rfidCode;

    @ApiModelProperty("资产型号(选填)")
    String model;

    @ApiModelProperty("资产编码")
    String assetCode;

    @ApiModelProperty("资产名称")
    String assetName;


    @ApiModelProperty("位置负责人")
    String managerName;

    @ApiModelProperty("所在位置")
    List<Long> locations;

    @ApiModelProperty("所在位置模糊")
    String location;

    @ApiModelProperty("状态")
    List<String> status;

    @ApiModelProperty("所属单位")
    List<Long> departmentIds;


    @ApiModelProperty("处理类型")
    List<String> processType;

    @ApiModelProperty("上报类型")
    List<String> types;

    @ApiModelProperty("上报类型")
    String type;

    @ApiModelProperty("开始时间")
    String startDate;

    @ApiModelProperty("结束时间")
    String endDate;

}
