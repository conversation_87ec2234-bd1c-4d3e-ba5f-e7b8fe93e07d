package com.hightop.benyin.system.api.vo.query;

import cn.afterturn.easypoi.excel.annotation.ExcelIgnore;
import com.hightop.magina.standard.ums.user.manage.UserPageQuery;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldDefaults;

import java.util.List;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AssetTypeQuery  {

    @ApiModelProperty("资产类型id")
    Long id;
    
    @ApiModelProperty("上级id")
    Long parentId;

    @ApiModelProperty("资产类型编码")
    String parentCode;

    @ApiModelProperty("资产类型编码")
    String code;

    @ApiModelProperty("资产类型名称")
    String name;

    @ApiModelProperty("有无标签")
    Boolean hasTag;

    @ApiModelProperty("是否扫描")
    Boolean isScan;

    @ApiModelProperty("是否上报异常")
    Boolean isReport;

    @ApiModelProperty("是否盘点")
    Boolean isTake;

    @ApiModelProperty("是否启用")
    Boolean isEnable;

    @ApiModelProperty("盘点是否统计")
    Boolean isTakeStatis;

    @ApiModelProperty("财务是否统计")
    Boolean isStatis;

    @ApiModelProperty("启用时间-起始")
    String startEnableDate;

    @ApiModelProperty("启用时间-终止")
    String endEnableDate;

    @ApiModelProperty("停用时间-起始")
    String startDisableDate;

    @ApiModelProperty("停用时间-终止")
    String endDisableDate;


    @ApiModelProperty("资产类型id")
    List<Long> ids;

}
