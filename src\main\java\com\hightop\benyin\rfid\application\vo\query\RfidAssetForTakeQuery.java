package com.hightop.benyin.rfid.application.vo.query;

import com.hightop.benyin.rfid.infrastructure.enums.AssetFlowStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.util.List;

/**
 * 资产查询DTO
 *
 * <AUTHOR>
 * @date 2024-10-12 16:36:01
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@ApiModel("资产查询DTO")
public class RfidAssetForTakeQuery {

    @ApiModelProperty("资产id")
    List<Long> ids;

    @ApiModelProperty("Rfid编码")
    List<String> rfidCodes;

    @ApiModelProperty("使用状态")
    List<String> useState;

    @ApiModelProperty("状态多选")
    List<AssetFlowStatus> statusList;

    @ApiModelProperty("领用人id")
    Long applyId;

    @ApiModelProperty("负责人id")
    Long managerId;

    @ApiModelProperty(value = "负责人(可多选)")
    List<Long> managerIds;

    @ApiModelProperty(value = "保管人(可多选)")
    List<Long> applyIds;

    @ApiModelProperty("位置")
    List<Long> locations;

    @ApiModelProperty("部门列表")
    List<Long> departmentIds;
}
