package com.hightop.benyin.rfid.infrastructure.mapper;

import com.github.yulichang.base.MPJBaseMapper;
import com.hightop.benyin.rfid.application.vo.query.RfidAssetTransferQuery;
import com.hightop.benyin.rfid.infrastructure.entity.RfidAssetFlow;
import com.hightop.benyin.rfid.infrastructure.entity.RfidAssetTransfer;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 资产变更mapper
 *
 * <AUTHOR>
 * @date 2024-10-12 16:36:01
 */
public interface RfidAssetTransferMapper extends MPJBaseMapper<RfidAssetTransfer> {

    List<RfidAssetTransfer> pageList(@Param("qo") RfidAssetTransferQuery query);
}
