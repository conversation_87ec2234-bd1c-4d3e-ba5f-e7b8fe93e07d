package com.hightop.benyin.rfid.application.service;

import com.github.yulichang.toolkit.MPJWrappers;
import com.google.common.collect.Lists;
import com.hightop.benyin.rfid.application.vo.query.RfidAssetApplyQuery;
import com.hightop.benyin.rfid.domain.service.RfidAssetApplyServiceDomain;
import com.hightop.benyin.rfid.infrastructure.entity.RfidAsset;
import com.hightop.benyin.rfid.infrastructure.entity.RfidAssetApply;
import com.hightop.benyin.rfid.infrastructure.entity.RfidAssetFlow;
import com.hightop.benyin.system.infrastructure.entity.AssetType;
import com.hightop.benyin.system.infrastructure.entity.DepartmentInfo;
import com.hightop.benyin.system.infrastructure.entity.Location;
import com.hightop.benyin.system.infrastructure.entity.UserInfo;
import com.hightop.fario.base.util.CollectionUtils;
import com.hightop.fario.base.util.StringUtils;
import com.hightop.fario.common.mybatis.DataGrid;
import com.hightop.fario.common.mybatis.PageQuery;
import com.hightop.fario.common.mybatis.util.PageHelper;
import com.hightop.magina.standard.ums.department.Department;
import com.hightop.magina.standard.ums.user.basic.UserBasic;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 资产操作单据服务
 *
 * <AUTHOR>
 * @date 2024-10-12 16:36:01
 */
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Service
@Slf4j
public class RfidAssetApplyService {

    RfidAssetApplyServiceDomain rfidAssetApplyServiceDomain;

    /**
     * 资产流水分页查询
     *
     * @param pageQuery {@link PageQuery}
     * @return {@link DataGrid}
     */
    public DataGrid<RfidAssetApply> page(RfidAssetApplyQuery pageQuery) {
        List<Long> childIds = Lists.newArrayList();

        DataGrid<RfidAssetApply> dataGrid =  PageHelper.startPage(pageQuery, p ->
                this.searchList(pageQuery)).peek(v -> {
            if (v.getType()!=null&&v.getType().equals(1)) {
                pageQuery.setParentCode(v.getCode());
                List<RfidAssetApply> childassets = this.searchList(pageQuery);
                if (CollectionUtils.isNotEmpty(childassets)) {
                    childIds.addAll(childassets.stream().map(RfidAssetApply::getAssetId).collect(Collectors.toList()));
                }
                v.setChildren(childassets);
            }
        });
        List<RfidAssetApply> rfidAssets = Lists.newArrayList();
        for (RfidAssetApply rfidAssetApply : dataGrid.getRows()) {
            if (!childIds.contains(rfidAssetApply.getAssetId())) {
                rfidAssets.add(rfidAssetApply);
            }
        }
        dataGrid.setRows(rfidAssets);
        return dataGrid;
    }

    private List<RfidAssetApply> searchList(RfidAssetApplyQuery pageQuery) {
        return this.rfidAssetApplyServiceDomain.selectJoinList(RfidAssetApply.class, MPJWrappers.lambdaJoin()
                .selectAll(RfidAssetApply.class)
                .selectAs(RfidAsset::getCode, RfidAssetApply::getCode)
                .selectAs(RfidAsset::getName, RfidAssetApply::getName)
                .selectAs(RfidAsset::getRfidCode, RfidAssetApply::getRfidCode)
                .selectAs(RfidAsset::getAssetType, RfidAssetApply::getAssetType)
                .selectAs(RfidAsset::getType, RfidAssetApply::getType)
                .selectAs(AssetType::getName, RfidAssetApply::getAssetTypeName)
                .selectAs(RfidAsset::getUseState, RfidAssetApply::getUseState)
                .selectAs(RfidAsset::getModel, RfidAssetApply::getModel)
                .selectAs(RfidAsset::getPrice, RfidAssetApply::getPrice)
                .selectAs(Location::getFullName, RfidAssetApply::getLocation)
                .selectAs(Department::getName, RfidAssetApply::getDepartmentName)
                .selectAs(DepartmentInfo::getName, RfidAssetApply::getManagerDeptName)
                .selectAs(RfidAsset::getManagerName, RfidAssetApply::getManagerName)
                .selectAs(RfidAssetFlow::getOperateType, RfidAssetApply::getOperateType)
                .selectAs(RfidAssetFlow::getApproveName, RfidAssetApply::getApproveName)
                .selectAs(RfidAssetFlow::getApproveAt, RfidAssetApply::getApproveAt)
                .selectAs(RfidAssetFlow::getChangeReader, RfidAssetApply::getChangeReader)
                .selectAs(RfidAssetFlow::getChangeLocation, RfidAssetApply::getChangeLocation)

                .leftJoin(RfidAssetFlow.class, RfidAssetFlow::getCode, RfidAssetApply::getFlowCode)
                .leftJoin(RfidAsset.class, RfidAsset::getId, RfidAssetApply::getAssetId)
                .leftJoin(Location.class, Location::getId, RfidAsset::getLocationId)
                .leftJoin(UserBasic.class, UserBasic::getId, RfidAssetApply::getCreatedBy)
                .leftJoin(Department.class, Department::getId, RfidAssetFlow::getDepartmentId)
                .leftJoin(DepartmentInfo.class, DepartmentInfo::getId, RfidAsset::getManagerDeptId)
                .leftJoin(AssetType.class, AssetType::getCode, RfidAsset::getAssetType)

                .like(StringUtils.isNotBlank(pageQuery.getFlowCode()), RfidAssetApply::getFlowCode, pageQuery.getFlowCode())
                .in(CollectionUtils.isNotEmpty(pageQuery.getAssetType()), RfidAsset::getAssetType, pageQuery.getAssetType())
                .in(CollectionUtils.isNotEmpty(pageQuery.getOperateTypes()), RfidAssetFlow::getOperateType, pageQuery.getOperateTypes())
                .in(CollectionUtils.isNotEmpty(pageQuery.getOperateTypeList()), RfidAssetFlow::getOperateType, pageQuery.getOperateTypeList())
                .eq(StringUtils.isNotBlank(pageQuery.getParentCode()), RfidAsset::getParentCode, pageQuery.getParentCode())
                .like(StringUtils.isNotBlank(pageQuery.getName()), RfidAsset::getName, pageQuery.getName())
                .like(StringUtils.isNotBlank(pageQuery.getRfidCode()), RfidAsset::getRfidCode, pageQuery.getRfidCode())
                .like(StringUtils.isNotBlank(pageQuery.getCode()), RfidAsset::getCode, pageQuery.getCode())
                .like(StringUtils.isNotBlank(pageQuery.getApplyName()), RfidAssetFlow::getApplyName, pageQuery.getApplyName())
                .like(StringUtils.isNotBlank(pageQuery.getCreatedByName()), UserBasic::getName, pageQuery.getCreatedByName())
                .ge(StringUtils.isNotBlank(pageQuery.getStartDate()), RfidAssetFlow::getCreatedAt, pageQuery.getStartDate() + " 00:00:00")
                .le(StringUtils.isNotBlank(pageQuery.getEndDate()), RfidAssetFlow::getCreatedAt, pageQuery.getEndDate() + " 23:59:59")
                .ge(StringUtils.isNotBlank(pageQuery.getStartApplyDate()), RfidAssetFlow::getCreatedAt, pageQuery.getStartApplyDate() + " 00:00:00")
                .le(StringUtils.isNotBlank(pageQuery.getEndApplyDate()), RfidAssetFlow::getCreatedAt, pageQuery.getEndApplyDate() + " 23:59:59")
                .orderByDesc(RfidAssetFlow::getCreatedAt)
        );
    }


}
