package com.hightop.benyin.share.socket.service.impl;

import com.github.yulichang.toolkit.SpringContentUtils;
import com.hightop.benyin.rfid.application.service.RfidReaderService;
import com.hightop.benyin.rfid.domain.service.RfidAssetServiceDomain;
import com.hightop.benyin.rfid.domain.service.RfidReaderServiceDomain;
import com.hightop.benyin.rfid.infrastructure.entity.RfidAsset;
import com.hightop.benyin.rfid.infrastructure.entity.RfidReader;
import com.hightop.benyin.share.domain.event.ApiLogEvent;
import com.hightop.benyin.share.infrastructure.enums.MessageType;
import com.hightop.benyin.share.infrastructure.util.DictUtil;
import com.hightop.benyin.share.socket.enums.CommandType;
import com.hightop.benyin.share.socket.service.CommandHandler;
import com.hightop.benyin.share.socket.util.MsgUtil;
import com.hightop.fario.base.util.StringUtils;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 查询基站保存的标签-命令处理器
 */
@Service
@Slf4j
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class ManagerTagHandler implements CommandHandler {
    RfidReaderServiceDomain rfidReaderServiceDomain;
    RfidAssetServiceDomain rfidAssetServiceDomain;
    RedisTemplate<String, String> redisTemplate;
    RfidReaderService rfidReaderService;

    @Override
    public void handle(String clientName, String deviceId, Integer length, String params) {


        log.info("查询基站保存的标签-命令处理器, clientName: {}, params: {}", clientName, params);
        ApplicationContext applicationContext = SpringContentUtils.getApplicationContext();
        RfidReader rfidReader = rfidReaderServiceDomain.lambdaQuery().eq(RfidReader::getIpAddr, clientName).one();
        if (rfidReader == null) {
            log.error("查询基站保存的标签-处理器, clientName: {},  rfidReader is null", clientName);
            ApiLogEvent apiLogEvent = new ApiLogEvent(this, MessageType.SEND, CommandType.DOWNLOAD_TAG,
                    deviceId, null, "ip地址找不到对应的基站！", false);
            applicationContext.publishEvent(apiLogEvent);
            return;
        }
        if(StringUtils.isBlank(params)){
            log.info("查询基站保存的标签, deviceId: {},  未绑定标签", deviceId);
            return ;
        }
        List<String> rfidList = MsgUtil.splitString(params, MsgUtil.RFID_STR_LENGTH);

        String resultKey = DictUtil.BIND_CACHE + DictUtil.LIST  + deviceId;
        if (redisTemplate.hasKey(resultKey)) {
            redisTemplate.delete(resultKey);
        }
        redisTemplate.opsForList().leftPushAll(resultKey, rfidList);
        redisTemplate.expire(resultKey, 10, TimeUnit.MINUTES);
        log.info("{}绑定检查，已绑定数量{}，", clientName, rfidList.size());
        long count = rfidAssetServiceDomain.lambdaQuery()
                .eq(RfidAsset::getReaderId, rfidReader.getId())
                .isNotNull(RfidAsset::getRfidCode)
                .count();
        if (count != rfidList.size()) {
            log.info("{}绑定结果不匹配，已绑定{}个,应绑{}个", clientName, rfidList.size(), count);
            rfidReaderService.sendAsset(rfidReader.getId());
        }
    }
}
