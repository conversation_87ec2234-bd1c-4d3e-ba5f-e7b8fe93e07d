spring:
  datasource:
    # mysql 可以省略驱动名称
    url: ******************************************************************
    username: root
    password: root
  #    url: ************************************************************************
  #    username: root
  #    password: By@ws%^uf&@#Mysql
  mail:
    host: smtp.qq.com
    port: 587
    username: <EMAIL>
    password: khcliffbxeqabged
    protocol: smtp
    default-encoding: UTF-8
    properties:
      mail.smtp.auth: true
      mail.smtp.starttls.enable: true
      mail.smtp.starttls.required: true
      mail.smtp.socketFactory.port: 465
      mail.smtp.socketFactory.class: javax.net.ssl.SSLSocketFactory
      mail.smtp.socketFactory.fallback: false

  redis:
    host: 127.0.0.1
    port: 6379
    database: 2
socket:
  enable: true
  maxConnectSize: 100
  port: 8160
magina:
  task:
    enable:
      schedule: true