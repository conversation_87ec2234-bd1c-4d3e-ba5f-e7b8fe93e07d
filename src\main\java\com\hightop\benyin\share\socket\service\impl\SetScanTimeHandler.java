package com.hightop.benyin.share.socket.service.impl;

import com.hightop.benyin.rfid.domain.service.RfidAssetServiceDomain;
import com.hightop.benyin.rfid.domain.service.RfidReaderServiceDomain;
import com.hightop.benyin.share.socket.enums.ResultEnums;
import com.hightop.benyin.share.socket.service.CommandHandler;
import com.hightop.benyin.share.socket.util.MsgUtil;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 查询心跳参数回执-命令处理器
 */
@Service
@Slf4j
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class SetScanTimeHandler implements CommandHandler {
    RfidReaderServiceDomain rfidReaderServiceDomain;
    RfidAssetServiceDomain rfidAssetServiceDomain;

    @Override
    public void handle(String clientName, String deviceId, Integer length, String params) {
        log.info("设置空闲时间/扫描时间参数-回执处理, clientName: {}, 结果: {}", clientName, ResultEnums.getName(MsgUtil.stringToInteger(params)));
    }
}
