package com.hightop.benyin.rfid.domain.service;

import com.github.yulichang.base.MPJBaseServiceImpl;
import com.hightop.benyin.rfid.infrastructure.entity.RfidAssetRecord;
import com.hightop.benyin.rfid.infrastructure.mapper.RfidAssetRecordMapper;
import org.springframework.stereotype.Service;

/**
 * rfid资产审核记录领域服务
 *
 * <AUTHOR>
 * @date 2024-10-12 16:36:01
 */
@Service
public class RfidAssetRecordServiceDomain extends MPJBaseServiceImpl<RfidAssetRecordMapper, RfidAssetRecord> {
}
