package com.hightop.benyin.rfid.domain.service;

import com.github.yulichang.base.MPJBaseServiceImpl;
import com.hightop.benyin.rfid.infrastructure.entity.RfidAssetTakeReader;
import com.hightop.benyin.rfid.infrastructure.mapper.RfidAssetTakeReaderMapper;
import org.springframework.stereotype.Service;

/**
 * 资产盘点明细领域服务
 *
 * <AUTHOR>
 * @date 2024-10-12 16:36:01
 */
@Service
public class RfidAssetTakeReaderServiceDomain extends MPJBaseServiceImpl<RfidAssetTakeReaderMapper, RfidAssetTakeReader> {
    public void clear() {
        baseMapper.clear();
    }
}
