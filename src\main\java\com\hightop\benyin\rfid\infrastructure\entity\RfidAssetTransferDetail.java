package com.hightop.benyin.rfid.infrastructure.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.hightop.benyin.share.infrastructure.util.DictUtil;
import com.hightop.fario.common.jackson.annotation.JsonAmount;
import com.hightop.magina.standard.code.dictionary.bind.DictItemBind;
import com.hightop.magina.standard.code.dictionary.bind.DictItemEntry;
import com.hightop.magina.standard.ums.user.bind.UserBind;
import com.hightop.magina.standard.ums.user.bind.UserEntry;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.time.LocalDateTime;

/**
 * 资产变更表
 *
 * <AUTHOR>
 * @date 2023-11-15 17:19:37
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@TableName("b_rfid_asset_transfer_detail")
@ApiModel
public class RfidAssetTransferDetail {

    @TableId(value = "id", type =IdType.ASSIGN_ID)
    @ApiModelProperty("id")
    Long id;

    @TableField("asset_id")
    @ApiModelProperty("资产id")
    Long assetId;

    @TableField("rfid_code")
    @ApiModelProperty("rfid编码")
    String rfidCode;

    @TableField(exist = false)
    @ApiModelProperty("资产编码")
    String code;

    @TableField("transfer_code")
    @ApiModelProperty("变更单号")
    String transferCode;


    @TableField(exist = false)
    @ApiModelProperty("资产名称")
    String name;


    @TableField(exist = false)
    @ApiModelProperty("规格")
    String model;


    @TableField(exist = false)
    @ApiModelProperty("价格")
    @JsonAmount
    Long price;

    @TableField(exist = false)
    @ApiModelProperty("资产小类")
    String assetType;

    @TableField(exist = false)
    @ApiModelProperty("资产类型")
    String assetTypeName;

    @TableField(exist = false)
    @ApiModelProperty("位置编码")
    String locationCode;


    @TableField(exist = false)
    @ApiModelProperty("原位置")
    String location;

    @TableField(exist = false)
    @ApiModelProperty("原领用人姓名")
    String applyName;


    @TableField(exist = false)
    @ApiModelProperty("位置编码")
    String newLocationCode;

    @TableField(exist = false)
    @ApiModelProperty("位置")
    String newLocation;

    @TableField(exist = false)
    @ApiModelProperty("领用人id")
    Long newApplyId;

    @TableField(exist = false)
    @ApiModelProperty("领用人姓名")
    String newApplyName;


    @TableField(exist = false)
    @ApiModelProperty("原部门/单位")
    String departmentName;

    @TableField(exist = false)
    @ApiModelProperty("新部门/单位")
    String newDepartmentName;


    @TableField(exist = false)
    @ApiModelProperty("变更时间")
    LocalDateTime transferDate;

    @TableField(exist = false)
    @ApiModelProperty("状态")
    String status;

    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @ApiModelProperty("创建时间")
    LocalDateTime createdAt;

    @TableField("deleted")
    @ApiModelProperty("是否删除  0未删除  1删除")
    @TableLogic
    Integer deleted;

}
