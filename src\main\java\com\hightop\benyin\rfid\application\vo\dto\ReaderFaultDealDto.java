package com.hightop.benyin.rfid.application.vo.dto;

import com.hightop.benyin.rfid.infrastructure.enums.HandleType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotNull;

/**
 * @Description: 基站故障处理DTO
 * @Author: X.S
 * @Date: 2023/12/22 17:16
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@ApiModel("基站故障处理DTO")
public class ReaderFaultDealDto {

    @ApiModelProperty("id")
    @NotNull(message = "id不能为空")
    Long id;
    @ApiModelProperty("readerId")
    @NotNull(message = "基站id不能为空")
    Long readerId;

    @ApiModelProperty("处理类型")
    @NotNull(message = "处理类型不能为空")
    HandleType handleType;

    @ApiModelProperty("更换的基站设备")
    String changeDeviceId;

    @ApiModelProperty("备注")
    String remark;

}
