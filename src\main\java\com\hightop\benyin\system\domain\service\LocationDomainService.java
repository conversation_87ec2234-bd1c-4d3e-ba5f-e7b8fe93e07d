package com.hightop.benyin.system.domain.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.github.yulichang.base.MPJBaseServiceImpl;
import com.google.common.collect.Lists;
import com.hightop.benyin.system.api.vo.LocationVo;
import com.hightop.benyin.system.api.vo.query.LocationQuery;
import com.hightop.benyin.system.infrastructure.entity.Location;
import com.hightop.benyin.system.infrastructure.mapper.LocaltionMapper;
import com.hightop.fario.base.constant.StringConstants;
import com.hightop.fario.base.util.CollectionUtils;
import com.hightop.magina.standard.cipher.CipherText;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.stereotype.Service;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Service
public class LocationDomainService extends MPJBaseServiceImpl<LocaltionMapper, Location> {
    /**
     * 通过位置编码获得位置
     *
     * @param LocationCode 位置编码
     * @return {@link Location}
     */
    public Location getByCode(CipherText LocationCode) {
        return super.lambdaQuery().eq(Location::getCode, LocationCode).one();
    }

    /**
     * 获得未删除的位置映射
     *
     * @return {@link Map}
     */
    public Map<Long, Location> getAvailableLocationMapping() {
        return super.baseMapper.getAvailableLocationMapping();
    }

    /**
     * 列表树查询
     *
     * @param query
     * @return List
     */
    public List<LocationVo> getLocationTreeList(LocationQuery query) {
        return baseMapper.getLocationTreeList(query);
    }

    /**
     * 获得启用的位置映射
     *
     * @return {@link Map}
     */
    public Map<Long, Location> getEnabledLocationMapping() {
        return super.baseMapper.getEnabledLocationMapping();
    }

    @Override
    public boolean removeById(Serializable id) {
        return this.removeByIds(Collections.singletonList(id));
    }

    @Override
    public boolean removeById(Location entity) {
        return this.removeByIds(Collections.singletonList(entity.getId()));
    }

    @Override
    public boolean removeByIds(Collection<? extends Serializable> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return false;
        }

        if (idList.size() == 1) {
            return this.remove(super.lambdaUpdate().eq(Location::getId, idList.iterator().next()).getWrapper());
        }

        return this.remove(super.lambdaUpdate().in(Location::getId, idList).getWrapper());
    }

    /**
     * 根据部门ID获得完整的位置
     *
     * @param departmentId 部门ID
     * @return {@link Location}
     */
    public List<LocationVo> getFullLocationTree(Long departmentId) {
        LocationQuery query = new LocationQuery();
        query.setDepartmentIds(Lists.newArrayList(departmentId));
        List<LocationVo> locationTrees = this.baseMapper.getLocationTreeList(query);
        locationTrees.forEach(v -> {
            v.setLocation(this.getLocalFullPath(v.getFullIdPath()));
        });
        return locationTrees;
    }
    /**
     * 根据部门ID获得完整的位置
     *
     * @param departmentId 部门ID
     * @return {@link Location}
     */
    public List<LocationVo> getByDepartmentId(Long departmentId) {
        LocationQuery query = new LocationQuery();
        query.setDepartmentIds(Lists.newArrayList(departmentId));
        return this.baseMapper.getLocationTreeList(query);
    }


    /**
     * 获得位置全路径
     * 如：/1/2/3/4
     *
     * @param fullIdPath 位置全路径
     * @return 位置全路径
     */
    public String getLocalFullPath(String fullIdPath) {
        List<String> locationIds = Arrays.stream(fullIdPath.split(StringConstants.SLASH))
                // 去掉头
                .skip(1).collect(Collectors.toList());
        List<Location> locationTrees = this.lambdaQuery()
                .in(Location::getId, locationIds).list();
        StringJoiner joiner = new StringJoiner(StringConstants.SLASH);

        for (Location location : locationTrees) {
            joiner.add(location.getName());
        }
        return joiner.toString();
    }

    @Override
    public boolean remove(Wrapper<Location> queryWrapper) {
        return super.update(new Location().setIsAvailable(false), queryWrapper);
    }

    @Override
    public boolean removeByMap(Map<String, Object> columnMap) {
        throw new UnsupportedOperationException("位置删除不支持此方法");
    }

    public void clearLocation() {
        baseMapper.clearLocation();
    }
}
