package com.hightop.benyin.share.infrastructure.util;


import com.google.common.collect.Lists;

import java.util.List;

public class DictUtil {


    /**
     * 启用状态
     */
    public static final Integer ENABLE = 1;
    public static final Integer DISABLE = 0;

    public static final Integer TOP = 0;
    public static final Integer SCANING = -1;
    /**
     * 出登记类型
     */
    public static final Integer IN_STOCK = 1;
    public static final Integer OUT_STOCK = 2;
    public static final Integer BORROW_STOCK = 3;
    public static final String ROOT_CODE = "0";
    public static final Integer HAS_CHILD = 1;
    public static final String SEQ_ASSET_APPLY = "FW";


    /**
     * 系统属性 -
     * 资产异动上报次数
     */
    public static final String ABNORMAL_RULE = "abnormal.rule";
    public static final String ABNORMAL_MINUTE = "abnormal.minute";
    public static final String ABNORMAL_OVERTIME = "abnormal.overtime";
    public static final String ABNORMAL_IGNORE = "abnormal.ignore";


    /**
     * 日志来源
     */
    public static final String SOURCE = "LOG_SOURCE";
    /**
     * 日志来源来源编码
     */
    public static final String BUSINESS_CODE = "BUSINESS_CODE";
    /**
     * 角色 资产 管理员
     */
    public static final String MANAGER_ROLE = "manager";
    /**
     * 角色 领导
     */
    public static final String LEADER_ROLE = "zleader";
    /**
     * 角色 员工
     */
    public static final String STAFF_ROLE = "staff";
    /**
     * 盘点类型 员工
     */
    public static final String TAKE_TYPE_USER = "USER";
    /**
     * 盘点类型 全单位
     */
    public static final String TAKE_TYPE_ALL = "ALL";
    /**
     * 角色 超级管理员
     */
    public static final String ADMIN_ROLE = "administrator";
    /**
     * 账号 超级管理员
     */
    public static final String ADMIN = "sa";

    public static final String DICT_ASSET_PURPOSE = "asset_purpose";
    public static final String DICT_CARD_TYPE = "card_type";
    public static final String DICT_SCAN_TYPE = "scan_type";
    public static final String DICT_USE_STATE = "use_state";
    public static final String DICT_ACQUIRE_MODE = "acquire_mode";
    public static final String DICT_FINANCIAL_CLASSIFY = "financial_classify";
    public static final String DICT_TAKE_MODE = "take_mode";
    public static final String DICT_TAKE_TYPE = "take_type";
    public static final String CYCLE_INDEX = "take_type";

    public static final Long SUB_DICT_ID = 1859527516473876481L;
    public static final String SUB_DICT_NAME = "迁出上报";
    public static final String BUY = "Buy";
    public static final String UNBLE = "20";
    public static final Integer SELF_UNIT =300;
    public static final String S ="S";



    /**
     * redis key
     */
    public static final String RESULT = "RESULT:";
    public static final String LIST = "LIST:";
    public static final String READER = "READER:";
    public static final String LOCK = "LOCK:";
    public static final String STATUS = "STATUS:";
    // 扫描状态发送 接收中 已接收
    public static final String SEND = "SEND";
    public static final String RECEIVING = "RECEIVING";
    public static final String RECEIVED = "RECEIVED";


    public static final String SCAN_TYPE = "SCAN:";
    public static final String SCAN_CODE = "CODE:";
    public static final String SCAN_COUNT = "COUNT:";

    public static final String SCAN_CACHE = "CACHE:SCAN:";
    public static final String BIND_CACHE = "BIND:";
    public static final String LOOK_CACHE = "LOOK:";
    public static final String PRIVATE_KEY = "ASSET-API:CACHE:PRIVATE_KEY:";


    // 基站设置参数 redis key
    public static final String  READER_INFO= "CACHE:READER_INFO:";

    public static final String  READER_SETTING= "CACHE:READER_SETTING:";
    //心跳间隔
    public static final String HEAT_INTERVAL = "HEAT_INTERVAL:";
    //扫描方式
    public static final String SCAN_MODE = "SCAN_MODE:";
    //扫描间隔
    public static final String SCAN_INTERVAL = "SCAN_INTERVAL:";
    //扫描持续时间
    public static final String SCAN_DURATION = "SCAN_DURATION:";
    //扫描状态
    public static final String SCAN_STATUS = "SCAN_STATUS:";
    //透传、节能状态
    public static final String DIRECT_STATUS = "DIRECT_STATUS:";
    //蜂鸣器状态
    public static final String BELL_STATUS = "BELL_STATUS:";
    //集中扫描状态
    public static final String FOCUS_STATUS = "FOCUS_STATUS:";
    //集中扫描间隔
    public static final String FOCUS_INTERVAL = "FOCUS_INTERVAL:";
    //集中扫描持续时间
    public static final String FOCUS_DURATION = "FOCUS_DURATION:";

    /**
     * 绑定扫描唯一标识redis key
     */
    public static final String BIND_SCAN_KEY = "BD";

    /**
     * 资产使用状态
     */
    public static final String USE = "10";//在用
    public static final String IDLE = "20";//待修
    public static final String REPAIR = "30";//在修
    public static final String STANDBY = "40";//备用
    public static final String UNUSE = "50";//闲置
    public static final String BORROW = "60";//调剂
    public static final String DAMAGE = "70";//待报废
    public static final String BREAKAGE = "80";//报废


    /**
     * 基站扫描开关
     */
    public static final int ON = 2; //开始扫描
    public static final int OFF = 1; //停止扫描
    public static final int SUB = 2;
    public static final int ADD = 1;
    /**
     * 异动类型数据字典 scan_type
     */
    //搜索扫描
    public static final String LOOK_SCAN = "30";
    //盘点扫描
    public static final String TAKE_SCAN = "20";
    //绑定扫描
    public static final String BIND_SCAN = "10";

    public static final String SUB_SCAN = "50";
    public static final String ADD_SCAN = "40";


    /**
     * 0资产异动1已上报2已处理3位置上报4忽略
     */
    public static final int REPORT = 0;
    public static final int LOCATION = 3;
    public static final int IGNORE = 4;
    public static final int VARIATION = 1;
    public static final int DEAL = 2;

    public static final String TAKE_LOST_SUBJECT = "您的资产未找到，请尽快处理";
    public static final String TAKE_LOST_CONTENT =
            "未找到资产【%s】，编号【%s】，RFID编号【%s】,保管人【%s】，保管人电话【%s】，应该在的位置【%s】。</br>" +
                    "针对上述资产情况有如下处理建议：</br>" +
                    "1.查看该资产的“监测轨迹”，了解该资产在遗失前最后几次出现的时间、房间，查看监控，或者在该区域寻找一下。</br>" +
                    "2.如果仍然没有找到，那么该资产可能已经遗失，或者被同事拿出覆盖了RFID基站的所有房间，建议询问该资产保管人，让其找找是否带去了其他的地方。</br>" +
                    "3.实在无法找到，建议对该资产做报损处理，在报损后下次盘点就不会再盘点该资产，但是该资产的标签出现在基站范围内仍将被识别到，那时需要做报溢处理。</br>" +
                    "4.不排除该资产处于房间某个铁皮柜等能屏蔽电磁波的环境中，建议查看监测轨迹中的最后轨迹变化，亦或去现场检查确认。</br>" +
                    "5.不排除该资产办理了领用出库，并关联了标签，但资产上并没有粘贴该标签，而空白标签平时存放在铁皮柜或者没有覆盖基站的房间，建议检查该资产是否贴了RFID标签。";

    public static final String TAKE_CH_SUBJECT = "您的资产位置异常，保管位置没有找到该资产，但在其他地方发现了该资产";
    public static final String TAKE_CH_CONTENT =
            "资产【%s】，编号为【%s】，RFID编号【%s】，保管人【%s】，保管人电话【%s】，应该在的位置【%s】，实际被发现在位置【%s】</br>" +
                    "针对上述资产有如下处理建议：</br>" +
                    "1.如果保管人仍在使用，建议将该资产搬回该资产的保管位置，或者变更该资产的保管位置。</br>" +
                    "2.如果保管人没有使用，建议做退还入库操作，并搬回仓库或者闲置资产的存放位置。</br>" +
                    "3.如果其他人在使用该资产，建议当前使用人做领用出库操作。</br>" +
                    "4.如果该资产保管人为空，则可能该资产没有办理领用出库，就被搬到其他地方使用了，建议办理领用出库操作。</br>" +
                    "5.如果当前位置为仓库或者闲置资产的位置，应保管位置是保管人的房间，此情况为保管人办理了领用出库，但没有将该资产领走，建议保管人去仓库领走。</br>" +
                    "6.如果当前位置在办公区域房间，但资产位置在仓库或者闲置资产存放地，此情况为保管人没有办理领用出库，直接将该资产搬走了，建议该房间的人员办理领用出库。</br>" +
                    "7.如果出现两个资产的保管位置和当前位置刚好相反并对应，不排除两个资产贴反了RFID标签，建议检查并交换。</br>";

    public static final String TAKE_SURPLUS_SUBJECT = "所在位置发现了新的标签，但没有关联资产";
    public static final String TAKE_SURPLUS_CONTENT =
            "发现到新的标签，编号【%s】，资产位置在【%s】。</br>" +
                    "针对上述资产有如下处理建议：</br>" +
                    "1.根据资产位置，查看资产上的RFID标签，查看是否本系统提供的标签以及其上的资产编号。如果不是，拍照给资产管理员，请其对该资产进行采购入库或调拨入库、捐赠入库，这个资产可能是期初建立档案时未盘点到，或者从其他单位过来没有对其补录采购入库。</br>" +
                    "2.如果是本系统标签，请资产管理员完善该RFID标签的资产属性数据编辑。此资产可能是贴完标签后就被领用出去了，但是没有在系统中完善资产数据，也没有办领用流程。</br>" +
                    "3.注意现场多了一些资产（未粘贴本系统标签），此资产无法被系统识别并进行报溢，必须先对没有RFDI标签的资产粘贴RFID标签，然后盘点该资产进行资产属性补录，或者先登记资产然后按照流程粘贴标签，并进行领用出库。具体操作步骤请咨询技术人员。</br>";

    public static final String TAKE_OVER_SUBJECT = "所在位置发现了其他资产";


    /**
     * 盘点异常说明-盘亏
     */
    public static final String TAKE_LOST_HEAD =
            "本次盘点未找到资产【%s】，编号为【%s】，RFID编号【%s】,保管人【%s】，保管人电话【%s】，应该在的位置【%s】。</br>" +
                    "针对上述资产有如下处理建议：</br>"+
                    "1.该资产可能在其它房间，可以点击上级页面【启动全局扫描】对所有房间进行查找。</br>" +
                    "2.该资产可能处于屏蔽电磁波的环境中(如铁皮柜)，此类情况可通知保管人拿出井重新扫描，或者亲自到现场进行确认。</br>" +
                    "3.查看该资产的”监测轨迹”，了解该资产在遗失前最后几次出现的时间、房间，查看监控，或者在该区域寻找一下。</br>" +
                    "4.检查RFID标签:检查该资产是否贴了RFID标签;确认空白标签是否存放在铁皮柜或者没有覆盖基站的房间。</br>" +
                    "5.报损出库，此操作将快速处理资产报损，报损后下次盘点就不会再盘点该资产。";
    /**
     * 盘点异常说明-异动
     */
    public static final String TAKE_CHANGE_OUT_HEAD =
            "资产【%s】，编号为【%s】，RFID编号【%s】,保管人【%s】，保管人电话【%s】，应该在的位置【%s】,实际被发现在位置【%s】。</br>" +
                    "针对上述资产有如下处理建议：</br>"
                    + "1.线下处理，线下通知相关人员对该资产进行相应业务操作，如退还、领用等。</br>"
                    + "2.资产变更，如果知晓当前资产的保管人已变更，此操作将资产变更为新保管人，并将原保管人信息添加到变更记录中。</br>"
                    + "3.找到该资产并取回放在对应位置上。</br>";

    /**
     * 盘点异常说明-报溢
     */
    public static final String TAKE_SURPLUS_HEAD =
            "盘点时发现到了RFID编号【%s】，资产位置【%s】。</br>" +
                    "针对上述资产有如下处理建议：</br>" +
                    "1.线下处理，找到该标签以确认是否绑定资产，然后选择拍照发给资产管理员确认资产是否正确登记。</br>" +
                    "2.报溢入库，如果你知晓此资产的相关信息，可在此直接添加资产信息，并登记入库。</br>";



    /**
     * 异动-位置变动
     */
    public static final String VARIATION_MOVE_HEAD =
            "1.资产【%s】从【%s】搬到了【%s】";


    /**
     * 异动-迁出未知
     */
    public static final String VARIATION_LOST_HEAD =
            "1.资产还在【%s】，但扫描不到，需要保管人去现场确认下</br>" +
            "2.资产已经从【%s】，拿走了，目前的位置不可知，最后出现的位置在【%s室】";


    /**
     * 异动-迁入
     */
    public static final String VARIATION_UNKNOW_HEAD =
            "资产【%s】，从【%s】搬到了【%s】</br>" +
                    "1.它还在这里</br>" +
                    "2.它又从这里被搬到了没有信号的地方，或者被搬出了办公楼";

}
