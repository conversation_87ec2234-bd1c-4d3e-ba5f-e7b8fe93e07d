package com.hightop.benyin.share.socket.service.impl;

import com.github.yulichang.toolkit.SpringContentUtils;
import com.hightop.benyin.rfid.domain.service.RfidReaderServiceDomain;
import com.hightop.benyin.rfid.infrastructure.entity.RfidReader;
import com.hightop.benyin.rfid.infrastructure.enums.ReaderStatus;
import com.hightop.benyin.share.domain.event.ApiLogEvent;
import com.hightop.benyin.share.infrastructure.enums.MessageType;
import com.hightop.benyin.share.socket.enums.CommandType;
import com.hightop.benyin.share.socket.enums.ResultEnums;
import com.hightop.benyin.share.socket.service.CommandHandler;
import com.hightop.benyin.share.socket.util.MsgUtil;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

/**
 * 查询心跳参数回执-命令处理器
 */
@Service
@Slf4j
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class SetHeartbeatHandler implements CommandHandler {

    RfidReaderServiceDomain rfidReaderServiceDomain;

    @Override
    public void handle(String clientName, String deviceId, Integer length, String params) {
        log.info("基站设置心跳参数回应-命令处理器, clientName: {}, params: {}", clientName, params);
        ApplicationContext applicationContext = SpringContentUtils.getApplicationContext();
        RfidReader rfidReader = rfidReaderServiceDomain.lambdaQuery().eq(RfidReader::getIpAddr, clientName).one();
        if (rfidReader == null) {
            log.error("基站设置心跳参数回应-处理器, clientName: {},  rfidReader is null", clientName);
            ApiLogEvent apiLogEvent = new ApiLogEvent(this, MessageType.SEND, CommandType.DOWNLOAD_TAG,
                    deviceId, null, "ip地址找不到对应的基站！", false);
            applicationContext.publishEvent(apiLogEvent);
            return;
        }
        //扫描间隔、扫描状态、心跳周期
        String[] paramsArray = new String[params.length() / 2];
        for (int i = 0; i < paramsArray.length; i++) {
            // 提取每两个字符组成的子字符串
            String substring = params.substring(i * 2, i * 2 + 2);
            paramsArray[i] = substring;
        }
        Integer result = paramsArray.length > 2 ? MsgUtil.stringToInteger(paramsArray[2]) : null;

        if (result != null && result.equals(ResultEnums.SUCCESS.getCode())) {
            log.info("基站心跳参数设置成功, clientName: {}", clientName);
        }
        rfidReader.setStatus(ReaderStatus.NORMAL);
        // 更新基站状态和最后活跃时间
        rfidReader.setUpdatedAt(java.time.LocalDateTime.now());
        rfidReaderServiceDomain.updateById(rfidReader);
    }
}
