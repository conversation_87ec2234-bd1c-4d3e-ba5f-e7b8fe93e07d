package com.hightop.benyin.rfid.application.vo.query;

import com.hightop.benyin.rfid.infrastructure.enums.TakeRangeEnums;
import com.hightop.fario.common.mybatis.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldDefaults;

import java.util.List;

/**
 * 资产盘点查询DTO
 *
 * <AUTHOR>
 * @date 2024-10-12 16:36:01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@FieldDefaults(level = AccessLevel.PRIVATE)
@ApiModel("资产盘点查询DTO")
public class RfidAssetTakeQuery extends PageQuery {

    @ApiModelProperty("盘点编码")
    String code;

    @ApiModelProperty("部门")
    List<Long> departmentIds;

    @ApiModelProperty("位置")
    List<Long> locations;

    @ApiModelProperty("位置")
    List<String> scanStatus;

    @ApiModelProperty("盘点人")
    String createdBy;

    @ApiModelProperty("盘点类型")
    List<String> takeType;

    @ApiModelProperty("盘点范围")
    List<TakeRangeEnums> takeRange;

    @ApiModelProperty("审核人")
    String auditName;

    @ApiModelProperty("状态")
    String status;

    @ApiModelProperty("盘点状态")
    List<String> takeStatus;

    @ApiModelProperty("起始时间")
    String startDate;

    @ApiModelProperty("截止时间")
    String endDate;

    @ApiModelProperty("审核起始时间")
    String startAuditDate;

    @ApiModelProperty("审核截止时间")
    String endAuditDate;

    @ApiModelProperty("盘点开始时间")
    String startCompletedDate;

    @ApiModelProperty("盘点完成时间")
    String endCompletedDate;

}
