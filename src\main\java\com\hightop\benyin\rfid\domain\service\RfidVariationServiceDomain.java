package com.hightop.benyin.rfid.domain.service;

import com.github.yulichang.base.MPJBaseServiceImpl;
import com.hightop.benyin.rfid.application.vo.query.RfidVariationQuery;
import com.hightop.benyin.rfid.infrastructure.entity.RfidVariation;
import com.hightop.benyin.rfid.infrastructure.mapper.RfidVariationMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 *  RFID异动信息领域服务
 * <AUTHOR>
 * @date 2024-10-12 16:36:01
 */
@Service
public class RfidVariationServiceDomain extends MPJBaseServiceImpl<RfidVariationMapper, RfidVariation> {

    /**
     * 查询最后一次的RFID异动信息
     * @param query
     * @return
     */
    public List<RfidVariation> lastRfidRvariationList(RfidVariationQuery query){
        return baseMapper.lastRfidRvariationList(query);
    }

    public void clear() {
        baseMapper.clear();
    }
}
