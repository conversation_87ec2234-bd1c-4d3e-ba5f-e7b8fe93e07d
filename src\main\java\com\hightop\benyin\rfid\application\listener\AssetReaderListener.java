package com.hightop.benyin.rfid.application.listener;


import com.hightop.benyin.rfid.application.service.RfidReaderFaultService;
import com.hightop.benyin.rfid.application.service.RfidReaderService;
import com.hightop.benyin.rfid.domain.event.AssetReaderEvent;
import com.hightop.benyin.rfid.domain.service.RfidReaderServiceDomain;
import com.hightop.benyin.rfid.infrastructure.entity.RfidReader;
import com.hightop.benyin.rfid.infrastructure.enums.ReaderStatus;
import com.hightop.benyin.share.infrastructure.util.DictUtil;
import com.hightop.benyin.share.socket.enums.ReportModeEnums;
import com.hightop.benyin.share.socket.util.MsgUtil;
import com.hightop.benyin.share.socket.util.ReaderControlTool;
import com.hightop.fario.base.util.StringUtils;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 资产盘点完成监听器
 */
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Service
@Slf4j
public class AssetReaderListener {

    RfidReaderService rfidReaderService;
    RfidReaderServiceDomain rfidReaderServiceDomain;
    RfidReaderFaultService rfidReaderFaultService;

    @EventListener
    public void onCompleted(AssetReaderEvent event) {
        String params = event.getHearbeatMsg();
        String client = event.getClient();
        String deviceIdHex = params.substring(0, 8);
        String deviceId = MsgUtil.hexToString(deviceIdHex);

        log.info("{}设备状态上报事件处理: {}！", deviceId, params);
        RfidReader rfidReader = rfidReaderServiceDomain.lambdaQuery().eq(RfidReader::getDeviceId, deviceId).one();
        if (Objects.isNull(rfidReader)) {
            rfidReader = new RfidReader();
            rfidReader.setCode(deviceId);
            rfidReader.setDeviceId(deviceId);
        } else {
            //判断ip是否改变
            if (StringUtils.isNotBlank(rfidReader.getIpAddr())
                    && !rfidReader.getIpAddr().equals(client)) {
                rfidReaderServiceDomain.lambdaUpdate()
                        .set(RfidReader::getIpAddr, null)
                        .eq(RfidReader::getIpAddr, client)
                        .ne(RfidReader::getId, rfidReader.getId())
                        .update();
            }
        }
        rfidReader.setStatus(ReaderStatus.NORMAL);
        rfidReader.setIpAddr(client);
        String[] paramsArray = new String[params.length() / 2];
        for (int i = 0; i < paramsArray.length; i++) {
            // 提取每两个字符组成的子字符串
            String substring = params.substring(i * 2, i * 2 + 2);
            paramsArray[i] = substring;
        }
        //#FEEFB000145A5A5A5A 00/保存标签数 0014心跳间隔  01心跳状态 003C日常空闲 000F日常扫描时长 001E 集中空闲 0014集中扫描 06上报方式02 透传 01日常扫描开关
        int tagNum = paramsArray.length > 4 ? MsgUtil.stringToInteger(paramsArray[4]) : 0;
        String firstHearBeatParam = paramsArray.length > 5 ? paramsArray[5] : null;
        String secondHearBeatParam = paramsArray.length > 6 ? paramsArray[6] : null;
        rfidReader.setHeatInterval(MsgUtil.stringHexToInt(firstHearBeatParam + secondHearBeatParam));
        Integer heatStatus = paramsArray.length > 7 ? MsgUtil.stringToInteger(paramsArray[7]) : null;
        rfidReader.setHeatStatus(heatStatus);

        String firstScanInterval = paramsArray.length > 8 ? paramsArray[8] : null;
        String secondScanInterval = paramsArray.length > 9 ? paramsArray[9] : null;
        rfidReader.setScanInterval(MsgUtil.stringHexToInt(firstScanInterval + secondScanInterval));

        String firstScanDuration = paramsArray.length > 10 ? paramsArray[10] : null;
        String secondScanDuration = paramsArray.length > 11 ? paramsArray[11] : null;
        rfidReader.setScanDuration(MsgUtil.stringHexToInt(firstScanDuration + secondScanDuration));

        String firstFocusInterval = paramsArray.length > 12 ? paramsArray[12] : null;
        String secondFocusInterval = paramsArray.length > 13 ? paramsArray[13] : null;
        rfidReader.setFocusInterval(MsgUtil.stringHexToInt(firstFocusInterval + secondFocusInterval));

        String firstFocusDuration = paramsArray.length > 14 ? paramsArray[14] : null;
        String secondFocusDuration = paramsArray.length > 15 ? paramsArray[15] : null;
        rfidReader.setFocusDuration(MsgUtil.stringHexToInt(firstFocusDuration + secondFocusDuration));

        Integer reportType = paramsArray.length > 16 ? MsgUtil.stringToInteger(paramsArray[16]) : null;
        Integer directStatus = paramsArray.length > 17 ? MsgUtil.stringToInteger(paramsArray[17]) : null;
        Integer scanStatus = paramsArray.length > 18 ? MsgUtil.stringToInteger(paramsArray[18]) : null;

        rfidReader.setIpAddr(client);
        if(reportType!=null){
            ReportModeEnums reportModeEnums = ReportModeEnums.getName(reportType);
            rfidReader.setScanMode(reportModeEnums);
        }
        // 对设备上报的扫描状态进行转换，0转为1，1转为2
        if (scanStatus != null) {
            if (scanStatus == 0) {
                rfidReader.setScanStatus(DictUtil.OFF); // 0(关闭) -> 1(系统内部关闭)
            } else if (scanStatus == 1) {
                rfidReader.setScanStatus(DictUtil.ON); // 1(开启) -> 2(系统内部开启)
            } else {
                rfidReader.setScanStatus(scanStatus); // 其他情况保持原值
            }
        }
        rfidReader.setDirectStatus(directStatus);
        rfidReader.setTagNum(tagNum);
        rfidReader.setStatus(ReaderStatus.NORMAL);

        if (tagNum == DictUtil.DISABLE) {
            // 全量发送一次标签数据
            boolean sendAsset = rfidReaderService.sendAsset(rfidReader.getId());
            if (sendAsset) {
                rfidReader.setTagNum(DictUtil.ENABLE);
            }
            if (rfidReader.getScanStatus().equals(DictUtil.OFF)) {
                //打开扫描状态
                ReaderControlTool.openScan(rfidReader.getDeviceId(), 15, 10);
            }
        }
        rfidReaderFaultService.recover(rfidReader.getId());
        
        // 显式设置更新时间，确保掉线检测能正确工作
        rfidReader.setUpdatedAt(java.time.LocalDateTime.now());
        rfidReaderServiceDomain.saveOrUpdate(rfidReader);
    }
}