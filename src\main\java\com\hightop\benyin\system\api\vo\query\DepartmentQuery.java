package com.hightop.benyin.system.api.vo.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldDefaults;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@FieldDefaults(level = AccessLevel.PRIVATE)
public class DepartmentQuery extends AuthQuery {

    @ApiModelProperty("部门id")
    Long id;

    @ApiModelProperty("部门编码")
    String code;

    @ApiModelProperty("部门名称")
    String name;

    @ApiModelProperty("资产管理员")
    List<Long> managerId;

    @ApiModelProperty("部门ids")
    List<Long> departmentIds;

    @ApiModelProperty("启停状态1 启用 0 停用")
    Boolean isEnable;

    @ApiModelProperty("启用时间-起始")
    String startEnableDate;

    @ApiModelProperty("启用时间-终止")
    String endEnableDate;

    @ApiModelProperty("停用时间-起始")
    String startDisableDate;

    @ApiModelProperty("停用时间-终止")
    String endDisableDate;

    @ApiModelProperty("关联的部门公司编码")
    String companyCode;
    @ApiModelProperty("有权限部门编码")
    List<String> departmentCodes;

}
