package com.hightop.benyin.rfid.application.vo.query;

import com.hightop.fario.common.mybatis.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldDefaults;

import java.util.List;

/**
 * 资产变更查询DTO
 *
 * <AUTHOR>
 * @date 2024-10-12 16:36:01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@FieldDefaults(level = AccessLevel.PRIVATE)
@ApiModel("rfid变动信息查询DTO")
public class RfidAssetTransferQuery extends PageQuery {

    @ApiModelProperty("新领用人")
    String newApplyName;

    @ApiModelProperty("原领用人")
    String applyName;

    @ApiModelProperty("单号")
    String transferCode;


    @ApiModelProperty("变更类型")
    String transferType;

    @ApiModelProperty("原位置编码")
    List<Long> locations;

    @ApiModelProperty("新位置编码")
    List<Long> newLocations;

    @ApiModelProperty("原部门")
    List<Long> departmentIds;

    @ApiModelProperty("新部门")
    List<Long> newDepartmentIds;

    @ApiModelProperty("审核起始时间")
    String startAuditDate;

    @ApiModelProperty("审核截止时间")
    String endAuditDate;

    @ApiModelProperty("创建起始时间")
    String startDate;

    @ApiModelProperty("创建截止时间")
    String endDate;

    @ApiModelProperty("变更起始日期")
    String startTransferDate;

    @ApiModelProperty("变更截止日期")
    String endTransferDate;

    @ApiModelProperty("状态")
    String status;

}
