package com.hightop.benyin.rfid.infrastructure.enums;

import com.hightop.magina.core.custom.entry.EnumEntry;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

/**
 * 支付凭证状态
 * <AUTHOR>
 * @date 2024/5/15 13:42
 */
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Getter
public enum TakeStatus implements EnumEntry<String> {
    /**
     * 待处理
     */
    WAIT_DEAL("待处理"),
    /**
     * 盘点中
     */
    EXECUTING("盘点中"),

    /**
     * 待审核
     */
    WAIT_APPROVE("待审核"),
    /**
     * 驳回
     */
    REJECT("驳回"),
    /**
     * 完成
     */
    YES("已完成");

    /**
     * 状态名称
     */
    String name;

    @Override
    public String getCode() {
        // 以枚举名称作为标识
        return super.name();
    }

    @Override
    public String getName() {
        return this.name;
    }

    /**
     * 是否通过状态
     * @return true/false
     */
    public boolean isFinished() {
        // 待审核以上状态均为完成状态
        return this.ordinal() > WAIT_APPROVE.ordinal();
    }
}
