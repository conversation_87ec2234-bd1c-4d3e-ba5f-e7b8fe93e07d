package com.hightop.benyin.rfid.infrastructure.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.*;
import com.hightop.benyin.share.infrastructure.util.DictUtil;
import com.hightop.magina.standard.code.dictionary.bind.DictItemBind;
import com.hightop.magina.standard.code.dictionary.bind.DictItemEntry;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.time.LocalDateTime;

/**
 * rfid异动分析
 *
 * <AUTHOR>
 * @date 2024-10-12 16:36:01
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@TableName(value = "b_rfid_variation_analyze", autoResultMap = true)
@ApiModel
public class RfidVariationAnalyze {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @ApiModelProperty("id")
    Long id;

    @TableField("rfid_code")
    @ApiModelProperty("RFID编码")
    @Excel(name = "RFID编号", width = 30, orderNum = "0")
    String rfidCode;

    @TableField("asset_id")
    @ApiModelProperty("资产编码")
    Long assetId;

    @TableField("info_id")
    @ApiModelProperty("资产编码")
    Long infoId;

    @TableField("report_type")
    @ApiModelProperty("上报类型")
    @DictItemBind(DictUtil.DICT_SCAN_TYPE)
    DictItemEntry reportType;

    @TableField("report_time")
    @Excel(name = "上报时间", width = 20, orderNum = "2")
    @ApiModelProperty("上报时间")
    Integer reportTime;

    @TableField("last_report_time")
    @ApiModelProperty("最后上报时间")
    @Excel(name = "最后上报时间", width = 30, orderNum = "8", format = "yyyy-MM-dd HH:mm:ss")
    LocalDateTime lastReportTime;

    @TableField("has_join_report")
    @ApiModelProperty("是否找到迁入数据")
    Boolean hasJoinReport;

    @TableField("curr_reader_id")
    @ApiModelProperty("迁入上报基站id")
    Long currReaderId;

    @TableField("curr_device_id")
    @ApiModelProperty("迁入上报设备编码")
    @Excel(name = "迁入上报基站", width = 15, orderNum = "5")
    String currDeviceId;

    @TableField("curr_location_id")
    @ApiModelProperty("迁入上报位置编码")
    Long currLocationId;

    @TableField("curr_location")
    @ApiModelProperty("迁入上报位置")
    @Excel(name = "迁入上报位置", width = 30, orderNum = "6")
    String currLocation;


    @TableField("status")
    @ApiModelProperty("状态0上报1上报异动2已处理3位置上报4自动忽略")
    @Excel(name = "状态", width = 20, orderNum = "4", replace = {"位置异动_0", "上报异动_1", "已处理_2", "位置上报_3", "自动忽略_4"})
    Integer status;


    @TableField("备注")
    @ApiModelProperty("备注")
    @Excel(name = "备注", width = 20, orderNum = "4")
    String remark;


    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @ApiModelProperty("创建时间")
    @Excel(name = "创建时间", width = 30, orderNum = "7", format = "yyyy-MM-dd HH:mm:ss")
    LocalDateTime createdAt;

    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty("修改时间")
    @Excel(name = "修改时间", width = 30, orderNum = "7", format = "yyyy-MM-dd HH:mm:ss")
    LocalDateTime updatedAt;

    @TableField("deleted")
    @ApiModelProperty("是否删除  0未删除  1删除")
    @TableLogic
    Integer deleted;

    @TableField(exist = false)
    @ApiModelProperty("资产名称")
    @Excel(name = "资产名称", width = 30, orderNum = "1")
    String assetName;


    @TableField(exist = false)
    @ApiModelProperty("资产型号")
    String model;

    @TableField(exist = false)
    @ApiModelProperty("资产编码")
    @Excel(name = "资产名称", width = 30, orderNum = "1")
    String assetCode;
    @TableField(exist = false)
    @ApiModelProperty("基站编码")
    String readerCode;

    @TableField(exist = false)
    @ApiModelProperty("是否绑定")
    Boolean isBind = true;

}
