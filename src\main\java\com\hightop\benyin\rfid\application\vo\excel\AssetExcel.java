package com.hightop.benyin.rfid.application.vo.excel;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelIgnore;
import com.hightop.benyin.rfid.infrastructure.entity.RfidAsset;
import com.hightop.benyin.share.application.vo.ExcelBaseInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 资产-导入实体
 *
 * <AUTHOR>
 * @date 2023-11-06 14:53:13
 */
@Data
@ApiModel("资产-导入实体")
public class AssetExcel extends ExcelBaseInfo {


    @Excel(name = "当前rfid编码", width = 30, orderNum = "0")
    @ApiModelProperty("当前rfid编码")
    String rfidCode;

    @Excel(name = "原rfid编码", width = 30, orderNum = "1")
    @ApiModelProperty("原rfid编码")
    String oriRfidCode;

    @Excel(name = "资产编码", width = 30, orderNum = "2")
    @ApiModelProperty("资产编码")
    @NotBlank
    String code;

    @Excel(name = "资产名称", width = 30, orderNum = "3")
    @ApiModelProperty("资产名称")
    @NotBlank
    String name;

    @ApiModelProperty("规格型号(选填)")
    @Excel(name = "规格型号", width = 30, orderNum = "4")
    String model;

    @Excel(name = "上级资产编码", width = 30, orderNum = "5")
    @ApiModelProperty("上级资产编码")
    String parentCode;

    @Excel(name = "资产分类编码", width = 30, orderNum = "6")
    @ApiModelProperty("资产分类编码")
    String assetTypeCode;

    @Excel(name = "资产大类", width = 30, orderNum = "7")
    @ApiModelProperty("资产大类")
    @NotBlank
    String assetParentType;

    @Excel(name = "资产小类", width = 30, orderNum = "8")
    @ApiModelProperty("资产小类")
    String assetMiddleType;

    @Excel(name = "分类名称", width = 30, orderNum = "9")
    @ApiModelProperty("分类名称")
    String assetType;

    @Excel(name = "资产用途", width = 30, orderNum = "10")
    @ApiModelProperty("资产用途")
    String assetPurpose;

    @ApiModelProperty("使用状态")
    @Excel(name = "使用状态", width = 30, orderNum = "11")
    String useState;

    @ApiModelProperty("卡片类型")
    @Excel(name = "卡片类型", width = 30, orderNum = "12")
    String cardType;

    @ApiModelProperty("久其编码(选填)")
    @Excel(name = "久其编码", width = 30, orderNum = "13")
    String cheerCode;

    @Excel(name = "取得方式", width = 30, orderNum = "14")
    @ApiModelProperty("取得方式")
    String acquireMode;

    @ExcelIgnore
    @ApiModelProperty("资产大类")
    Long assetParentId;

    @ExcelIgnore
    @ApiModelProperty("资产小类")
    Long assetMiddleId;

    @Excel(name = "取得日期(yyyy-MM-dd)", width = 30, orderNum = "15")
    @ApiModelProperty("取得日期")
    String acquireDate;

    @Excel(name = "入账日期(yyyy-MM-dd)", width = 30, orderNum = "16")
    @ApiModelProperty("入账日期")
    String accountDate;


    @Excel(name = "基站编码", width = 30, orderNum = "17")
    @ApiModelProperty("基站编码")
    String readerCode;


    @Excel(name = "基站设备ID", width = 30, orderNum = "18")
    @ApiModelProperty("基站编码")
    String readerDeviceId;


    @ApiModelProperty("位置编码")
    @Excel(name = "位置编码", width = 30, orderNum = "19")
    String locationCode;

    @ApiModelProperty("位置名称")
    @Excel(name = "位置名称", width = 30, orderNum = "20")
    String location;

    @ApiModelProperty("存放位置(选填)")
    @Excel(name = "存放位置", width = 30, orderNum = "21")
    String storageLocation;

    @ApiModelProperty("财务归口")
    @Excel(name = "财务归口", width = 20, orderNum = "22")
    String financialClassify;

    @ApiModelProperty("资产来源1期初导入0业务新增")
    @Excel(name = "数据来源", width = 20, orderNum = "23")
    String dataSourceStr;

    @ApiModelProperty("贴标签类型(是否)")
    @Excel(name = "是否贴标签", width = 20, orderNum = "24")
    String hasTagStr;

    @ApiModelProperty("是否扫描")
    @Excel(name = "是否扫描", width = 20, orderNum = "25")
    String isScanStr;

    @ApiModelProperty("是否上报异常")
    @Excel(name = "是否上报异常", width = 20, orderNum = "26")
    String isReportStr;

    @ApiModelProperty("是否盘点")
    @Excel(name = "是否盘点", width = 20, orderNum = "27")
    String isTakeStr;

    @ApiModelProperty("是否盘点统计")
    @Excel(name = "是否盘点统计", width = 20, orderNum = "28")
    String isTakeStatisStr;

    @ApiModelProperty("是否财务统计")
    @Excel(name = "是否财务统计", width = 20, orderNum = "29")
    String isStatisStr;

    @ApiModelProperty(value = "采购价格")
    @Excel(name = "采购价格", width = 30, orderNum = "30")
    String priceStr;

    @ApiModelProperty("初始残值 ")
    @Excel(name = "初始残值", width = 30, orderNum = "31")
    String initSalvageStr;

    @ApiModelProperty("当前残值")
    @Excel(name = "当前残值", width = 30, orderNum = "32")
    String nowSalvageStr;

    @ApiModelProperty("报废时间")
    @Excel(name = "报废时间", width = 30, orderNum = "33")
    String breakageAt;


    @ApiModelProperty(value = "责任部门编码")
    @Excel(name = "责任部门编码", width = 30, orderNum = "34")
    String managerDeptCode;

    @ApiModelProperty(value = "责任部门")
    @Excel(name = "责任部门", width = 30, orderNum = "35")
    String managerDeptName;

    @ApiModelProperty(value = "责任人账号")
    @Excel(name = "责任人账号", width = 30, orderNum = "36")
    String managerCode;

    @ApiModelProperty(value = "责任人姓名")
    @Excel(name = "责任人姓名", width = 30, orderNum = "37")
    String managerName;


    @ApiModelProperty(value = "当前保管部门编码")
    @Excel(name = "当前保管部门编码", width = 30, orderNum = "38")
    String departmentCode;

    @ApiModelProperty(value = "当前保管部门名称")
    @Excel(name = "当前保管部门名称", width = 30, orderNum = "39")
    String departmentName;

    @ApiModelProperty(value = "当前保管人账号")
    @Excel(name = "当前保管人账号", width = 30, orderNum = "40")
    String applyCode;

    @ApiModelProperty(value = "当前保管人姓名")
    @Excel(name = "当前保管人姓名", width = 30, orderNum = "41")
    String applyName;

    @ApiModelProperty("领用时间")
    @Excel(name = "领用时间(yyyy-MM-dd)", width = 30, orderNum = "42", format = "yyyy-MM-dd")
    LocalDateTime applyAt;


    @ExcelIgnore
    @ApiModelProperty("领用人ID")
    Long applyId;

    @ExcelIgnore
    @ApiModelProperty("部门ID")
    Long departmentId;
    @ExcelIgnore
    @ApiModelProperty("位置id")
    Long locationId;
    @ExcelIgnore
    @ApiModelProperty("责任人姓名")
    Long managerId;
    @ExcelIgnore
    @ApiModelProperty("责任人姓名")
    Long managerDeptId;
    @ExcelIgnore
    @ApiModelProperty("基站id")
    Long readerId;
    @ExcelIgnore
    @ApiModelProperty("财务归口列表")
    List<Integer> financialClassifyList;

    @ApiModelProperty("有无标签")
    @ExcelIgnore
    Boolean hasTag;

    @ApiModelProperty("是否扫描")
    @ExcelIgnore
    Boolean isScan;

    @ApiModelProperty("是否上报异常")
    @ExcelIgnore
    Boolean isReport;

    @ApiModelProperty("是否盘点")
    @ExcelIgnore
    Boolean isTake;

    @ApiModelProperty("盘点是否统计")
    @ExcelIgnore
    Boolean isTakeStatis;

    @ApiModelProperty("财务是否统计")
    @ExcelIgnore
    Boolean isStatis;


    @ApiModelProperty("价格")
    @ExcelIgnore
    Long price;

    @ApiModelProperty("初始残值 ")
    @ExcelIgnore
    Long initSalvage;

    @ApiModelProperty("当前残值")
    @ExcelIgnore
    Long nowSalvage;

    @ApiModelProperty("来源1期初导入0业务新增")
    @ExcelIgnore
    Integer dataSource;

    @ApiModelProperty("原数据")
    @ExcelIgnore
    RfidAsset oriRfidAsset;


}
