package com.hightop.benyin.share.infrastructure.restful;

import com.hightop.magina.core.exception.MaginaException;

import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Objects;
import java.util.Optional;

/**
 * feign元素据工具
 * @Author: X.S
 * @date 2024/02/27 10:31
 */
public interface FeignMetas {
    /**
     * 获取接口上的元素据
     * @param metas {@link ClientMetas}
     * @param key   元素据key
     * @return 元素据值
     */
    static String getMeta(ClientMetas metas, String key) {
        if (Objects.isNull(metas)) {
            return null;
        }
        
        return
            Arrays.stream(metas.value())
                .filter(it -> Objects.equals(it.key(), key))
                .map(ClientMeta::value)
                .findFirst()
                .orElse(null);
    }

    /**
     * 获取接口方法上的或接口上元素据
     * @param method {@link Method}
     * @param key    元素据key
     * @return 元素据值
     */
    static String getMeta(Method method, String key) {
        return
            Optional.ofNullable(method.getAnnotation(ClientMetas.class))
                .map(metas -> FeignMetas.getMeta(metas, key))
                .orElseGet(() ->
                    Optional.ofNullable(method.getDeclaringClass().getAnnotation(ClientMetas.class))
                        .map(metas -> FeignMetas.getMeta(metas, key))
                        .orElse(null)
                );
    }
    
    /**
     * 获取接口方法上的或接口上必须的元素据
     * @param method {@link ClientMetas}
     * @param key    元素据key
     * @return 元素据值
     */
    static String getRequiredMeta(Method method, String key) {
        return
            Optional.ofNullable(FeignMetas.getMeta(method, key))
                .orElseThrow(() -> new MaginaException(String.format("未找到必须的%s元素据", key)));
    }
}
