package com.hightop.benyin.system.api.vo.excel;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelIgnore;
import com.hightop.benyin.share.application.vo.ExcelBaseInfo;
import com.hightop.benyin.system.infrastructure.entity.DepartmentInfo;
import com.hightop.magina.standard.ums.department.Department;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

@Data
@ApiModel("部门信息导入模板")
public class DepartmentExcel extends ExcelBaseInfo {

    @Excel(name = "部门名称", width = 30, orderNum = "1")
    @NotBlank
    @Size(min = 1, max = 64, message = "部门名称长度在{min}至{max}之间")
    String name;

    @Excel(name = "部门编码", width = 30, orderNum = "0")
    @NotBlank
    @Size(min = 1, max = 64, message = "部门编码长度在{min}至{max}之间")
    String code;

    @Excel(name = "上级部门名称", width = 30, orderNum = "3")
    @ApiModelProperty("上级部门名称")
    String parentName;
    //上下都修改过
    @Excel(name = "上级部门编码", width = 30, orderNum = "2")
    @ApiModelProperty("上级部门编码")
    @Size(min = 1, max = 64, message = "上级部门编码长度在{min}至{max}之间")
    String parentCode;

    @Excel(name = "部门资管员姓名", width = 30, orderNum = "5")
    String managerName;
    //上下都修改过
    @Excel(name = "部门资管员账号", width = 30, orderNum = "4")
    String managerCode;

    @ApiModelProperty("排序号")
    @Excel(name = "排序号", width = 30, orderNum = "6")
    Integer sort;

    @ExcelIgnore
    Long managerId;

    @ExcelIgnore
    Long parentId;

    @ExcelIgnore
    Long id;

}
