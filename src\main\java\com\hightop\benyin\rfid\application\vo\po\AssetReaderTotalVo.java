package com.hightop.benyin.rfid.application.vo.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@ApiModel("基站数量统计")
public class AssetReaderTotalVo {

    @ApiModelProperty("总数量")
    Integer number;

    @ApiModelProperty("在线数量")
    long normalNum;

    @ApiModelProperty("断线数量")
    long breakNum;


}
