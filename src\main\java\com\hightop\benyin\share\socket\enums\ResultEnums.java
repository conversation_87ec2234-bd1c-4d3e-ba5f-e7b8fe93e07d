package com.hightop.benyin.share.socket.enums;

import com.hightop.benyin.rfid.infrastructure.enums.AssetUseStatus;
import com.hightop.magina.core.custom.entry.EnumEntry;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

/**
 * 基站响应枚举
 *
 * @Author: X.S
 * @Date: 2023/12/19 11:43
 */
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@Getter
@RequiredArgsConstructor
public enum ResultEnums implements EnumEntry<Integer> {
    SUCCESS(0x01, "成功"),
    RPT_RECLEN(0x02, "数据包头解析错误"),
    RPT_LENGTH(0x03, "参数错误，报文长度不对"),
    RPT_PACKLEN(0x04, "接收数据包长度大于缓冲区长度"),
    RPT_PACKAGE(0x05, "接收数据包解析出错"),
    RPT_STATE(0x06, "状态处理出错了"),
    BLOCK(0x07, "不支持的接口"),
    ABNORMAL(0x08, "基站异常，阻塞中"),
    RPT_TAGLEN(0x09, "读取的标签长度不对"),
    RPT_BUFSMALL(0x0A, "正在集中扫描，请求被拒绝"),
    RPT_SCANING(0x0B, "有扫描任务正在执行中，日常扫描被拒绝"),
    RPT_RXSMALL(0x0C, "接收缓冲区过小"),
    RPT_TXSMALL(0x0D, "执行失败，未知原因"),
    RPT_TXERR(0x0E, "发送缓冲区过小"),
    RPT_WFLASH(0x0F, "写flash出错"),
    RPT_HEADER(0x10, "接收数据包头长度不够"),
    RPT_OTHER(0x11, "接收到不支持的指令"),
    RPT_NTTERR(0x12, "非透传状态下收到透传指令"),
    RPT_NOTAG(0x13, "没有标签"),
    RPT_STOP(0x14, "集中扫描已停止"),
    ;
    /**
     * 代码
     */
    Integer code;
    /**
     * 描述
     */
    String name;

    public static String getName(Integer code) {
        for (ResultEnums status : ResultEnums.values()) {
            if (status.getCode().equals(code)) {
                return status.getName();
            }
        }
        return null;
    }
}