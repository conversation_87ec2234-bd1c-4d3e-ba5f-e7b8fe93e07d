package com.hightop.benyin.share.infrastructure.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hightop.benyin.share.infrastructure.enums.MessageType;
import com.hightop.benyin.share.socket.enums.CommandType;
import lombok.AccessLevel;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.experimental.FieldDefaults;

import java.time.LocalDateTime;

/**
 * 第三方调用日志
 *
 * @Author: X.S
 * @date 2023/11/16 13:44
 */
@FieldDefaults(level = AccessLevel.PRIVATE)
@Data
@Accessors(chain = true)
@TableName("b_api_log")
public class ApiLog {
    @TableId(type = IdType.ASSIGN_ID)
    Long id;

    @TableField("type")
    MessageType type;

    @TableField("reader_code")
    String readerCode;

    @TableField("device_id")
    String deviceId;

    @TableField
    CommandType command;

    @TableField("request_header")
    String requestHeader;

    @TableField("request_payload")
    String requestPayload;

    @TableField("remark")
    String remark;

    @TableField("requested_at")
    LocalDateTime requestedAt;

    @TableField("status_code")
    Boolean statusCode;

}
