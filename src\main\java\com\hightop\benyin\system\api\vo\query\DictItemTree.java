package com.hightop.benyin.system.api.vo.query;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.hightop.magina.core.custom.entry.TreeEntry;
import com.hightop.magina.standard.code.dictionary.item.DictItem;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldDefaults;

import java.util.Comparator;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@FieldDefaults(level = AccessLevel.PRIVATE)
public class DictItemTree extends DictItem implements TreeEntry<Long, DictItemTree> {
    @ApiModelProperty("子部门")
    List<DictItemTree> children;

    @Override
    @JsonIgnore
    public Boolean getIsEnable() {
        return super.getIsEnable();
    }

    @Override
    public Integer getSort() {
        return super.getSort();
    }

    /**
     * 排序比较器
     */
    public static final Comparator<DictItemTree> COMPARATOR =
            Comparator.comparing(DictItemTree::getSort).thenComparing(DictItemTree::getValue);
}
