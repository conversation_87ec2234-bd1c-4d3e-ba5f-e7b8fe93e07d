package com.hightop.benyin.share.application.service;

import com.hightop.benyin.share.application.vo.CosBucket;
import com.hightop.benyin.share.domain.service.CosTokenDomainService;
import com.hightop.benyin.share.infrastructure.restful.tencent.CosProperties;
import com.hightop.benyin.share.infrastructure.restful.tencent.sts.FederationCredentials;
import com.hightop.fario.base.util.StringUtils;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.concurrent.TimeUnit;

/**
 * cos服务
 * @Author: X.S
 * @date 2023/10/25 19:50
 */
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Service
@Transactional(propagation = Propagation.NOT_SUPPORTED)
public class CosService {
    CosTokenDomainService cosTokenDomainService;
    CosProperties cosProperties;
    @NonFinal
    CosBucket bucket;
    @NonFinal
    String policy;
    @NonFinal
    FederationCredentials federationCredentials;
    @NonFinal
    LocalDateTime tokenExpiredAt = LocalDateTime.now().minusYears(1L);
    Object credentialsLock = new Object();
    /**
     * 默认凭证超时时长
     */
    private static final Integer DURATION = Math.toIntExact(TimeUnit.HOURS.toSeconds(2));

    @PostConstruct
    public void init() throws IOException {
        // 初始化桶信息
        this.bucket =
            new CosBucket().setBucket(this.cosProperties.getBucket())
                .setRegion(this.cosProperties.getRegion())
                .setPrefix(this.cosProperties.getPrefix());

        // 资源策略
        String resource =
            String.format(
                "qcs::cos:%s:uid/%s:%s/*",
                this.cosProperties.getRegion(),
                this.cosProperties.getAppId(),
                this.cosProperties.getBucket()
            );
        // json策略配置
        String policy = StringUtils.read(new ClassPathResource("cos-policy.json").getInputStream());
        // 按照配置的桶信息初始化COS策略
        this.policy = String.format(policy, resource);
    }

    /**
     * 获得临时凭证
     * @return {@link CosBucket}
     */
    public FederationCredentials credentials() {
        LocalDateTime now = LocalDateTime.now();
        if (this.isCredentialsExpired(now)) {
            synchronized (this.credentialsLock) {
                if (this.isCredentialsExpired(now)) {
                    this.tokenExpiredAt = now.plusSeconds(DURATION);
                    this.federationCredentials = this.cosTokenDomainService.token(this.policy, DURATION);
                }
            }
        }

        return this.federationCredentials;
    }

    /**
     * cos桶信息
     * @return {@link CosBucket}
     */
    public CosBucket bucket() {
        return this.bucket;
    }

    /**
     * 凭证是否无效或即将过期
     * @param now 当前时间
     * @return true/false
     */
    protected boolean isCredentialsExpired(LocalDateTime now) {
        // 凭证剩余时长不足半小时 考虑上传需要时间 预留20分钟
        return this.tokenExpiredAt.isBefore(now.plusMinutes(20));
    }
}
