package com.hightop.benyin.system.domain.event;

import com.hightop.benyin.system.infrastructure.entity.DataLog;
import com.hightop.benyin.system.infrastructure.enums.BusinessType;
import com.hightop.benyin.system.infrastructure.enums.OperationType;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.SuperBuilder;

import java.util.List;

@FieldDefaults(level = AccessLevel.PRIVATE)
@SuperBuilder
@Getter
@AllArgsConstructor
@NoArgsConstructor
public class DataLogEvent {

    /**
     * 数据变更日志
     */
   List<DataLog> dataLogs;
}
