package com.hightop.benyin.rfid.infrastructure.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelIgnore;
import com.baomidou.mybatisplus.annotation.*;
import com.hightop.benyin.configurer.annotation.RecordLog;
import com.hightop.benyin.configurer.annotation.RecordLogField;
import com.hightop.benyin.configurer.enums.DataType;
import com.hightop.benyin.rfid.infrastructure.enums.ReaderStatus;
import com.hightop.benyin.share.socket.enums.ReportModeEnums;
import com.hightop.benyin.system.infrastructure.enums.BusinessType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * RFID基站实体
 *
 * <AUTHOR>
 * @date 2024-10-12 16:36:01
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@TableName(value = "b_rfid_reader", autoResultMap = true)
@ApiModel
@RecordLog(businessType = BusinessType.READER)
public class RfidReader {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @ApiModelProperty("id")
    Long id;

    @TableField("code")
    @ApiModelProperty("基站编码")
    @NotBlank(message = "基站编码不能为空")
    @Excel(name = "基站编码", width = 20, orderNum = "1")
    @RecordLogField(value = "基站编码")
    String code;

    @TableField("device_id")
    @ApiModelProperty("设备id")
    @Excel(name = "设备ID", width = 20, orderNum = "2")
    String deviceId;

    @TableField(exist = false)
    @ApiModelProperty("所在位置")
    @Excel(name = "所在位置", width = 30, orderNum = "3")
    String location;

    @TableField(exist = false)
    @ApiModelProperty("位置编码")
    @Excel(name = "位置编码", width = 20, orderNum = "4")
    String locationCode;

    @TableField("is_enable")
    @ApiModelProperty("启停状态")
    @RecordLogField(value = "启停状态", dataType = DataType.ENABLE)
    @Excel(name = "启停状态", width = 20, orderNum = "5", replace = {"启用_true", "停用_false"})
    Boolean isEnable;

    @TableField("is_report")
    @ApiModelProperty("异动上报")
    @RecordLogField(value = "异动上报", dataType = DataType.ENABLE)
    @Excel(name = "异动上报", width = 20, orderNum = "5", replace = {"开启_true", "关闭_false"})
    Boolean isReport;

    @TableField("heat_interval")
    @ApiModelProperty("心跳间隔默认20")
    @Excel(name = "心跳间隔", width = 30, orderNum = "6")
    Integer heatInterval;

    @TableField("heat_status")
    @ApiModelProperty("心跳状态")
    @Excel(name = "心跳状态", width = 30, orderNum = "8", replace = {"开启_1", "关闭_2"})
    Integer heatStatus;


    @TableField("scan_mode")
    @ApiModelProperty("扫描方式")
    @RecordLogField(value = "扫描方式")
    @Excel(name = "扫描方式", width = 30, orderNum = "8",enumExportField = "name")
    ReportModeEnums scanMode;


    @TableField("scan_status")
    @ApiModelProperty("扫描状态2：开启 1：关闭")
    @RecordLogField(value = "扫描状态", dataType = DataType.ON_OFF)
    @Excel(name = "扫描状态", width = 30, orderNum = "8", replace = {"开启_2", "关闭_1"})
    Integer scanStatus;


    @TableField("scan_interval")
    @ApiModelProperty("日常空闲时间")
    @Excel(name = "日常空闲时间", width = 30, orderNum = "7")
    Integer scanInterval;


    @TableField("scan_duration")
    @ApiModelProperty("日常扫描时长")
    @Excel(name = "日常扫描时长", width = 30, orderNum = "7")
    Integer scanDuration;

    @TableField("default_interval")
    @ApiModelProperty("默认扫描间隔")
    @ExcelIgnore
    Integer defaultInterval;


    @TableField("default_duration")
    @ApiModelProperty("默认扫描持续时间")
    @ExcelIgnore
    Integer defaultDuration;

    @TableField("focus_status")
    @ApiModelProperty("集中扫描状态2：开启 1：关闭")
    Integer focusStatus;


    @TableField("focus_interval")
    @ApiModelProperty("集中扫描间隔")
    @Excel(name = "集中扫描间隔", width = 30, orderNum = "7")
    Integer focusInterval;


    @TableField("focus_duration")
    @ApiModelProperty("集中扫描持续时间")
    @Excel(name = "集中扫描持续时间", width = 30, orderNum = "7")
    Integer focusDuration;

    @TableField("bell_status")
    @ApiModelProperty("蜂鸣器开关1：开启 2：关闭")
    @RecordLogField(value = "蜂鸣器", dataType = DataType.ON_OFF)
    @Excel(name = "蜂鸣器开关", width = 30, orderNum = "9", replace = {"开启_2", "关闭_1"})
    Integer bellStatus;

    @TableField("energy_status")
    @ApiModelProperty("节能状态1：开启 2：关闭")
    @RecordLogField(value = "节能状态", dataType = DataType.ON_OFF)
    @Excel(name = "节能状态", width = 30, orderNum = "10", replace = {"开启_1", "关闭_2"})
    Integer energyStatus;

    @TableField("voltage")
    @ApiModelProperty("电压")
    @Excel(name = "电压", width = 20, orderNum = "11")
    Integer voltage;

    @TableField("location_id")
    @ApiModelProperty("位置编码")
    @RecordLogField(value = "所在位置", dataType = DataType.LOCATION)
    @NotNull(message = "所在位置不能为空")
    Long locationId;


    @TableField(exist = false)
    @ApiModelProperty("位置全路径")
//    @Excel(name = "所属单位", width = 30, orderNum = "2")
    String fullIdPath;

    @TableField(exist = false)
    @ApiModelProperty("所属单位")
    @ExcelIgnore
    List<Long> departmentIds;

    @TableField(exist = false)
    @ApiModelProperty("所属单位列表")
    @ExcelIgnore
    String departmentNames;


    @TableField("tag_num")
    @ApiModelProperty("下发标签数量")
    @ExcelIgnore
    Integer tagNum;


    @TableField("power1")
    @ApiModelProperty("天线功率1")
    @Excel(name = "天线功率1", width = 30, orderNum = "12")
    Integer power1;

    @TableField("power2")
    @ApiModelProperty("天线功率2")
    @Excel(name = "天线功率2", width = 30, orderNum = "13")
    Integer power2;

    @TableField("power3")
    @ApiModelProperty("天线功率3")
    @Excel(name = "天线功率3", width = 30, orderNum = "14")
    Integer power3;

    @TableField("power4")
    @ApiModelProperty("天线功率4")
    @Excel(name = "天线功率4", width = 30, orderNum = "15")
    Integer power4;

    @TableField(value = "ip_addr", updateStrategy = FieldStrategy.IGNORED)
    @ApiModelProperty("ip地址")
//    @RecordLogField(value = "IP地址")
//    @NotBlank(message = "ip地址不能为空")
    @Excel(name = "IP地址", width = 30, orderNum = "16")
    String ipAddr;


    @TableField(value = "expire_at", updateStrategy = FieldStrategy.IGNORED)
    @ApiModelProperty("失效时间")
    LocalDateTime expireAt;

    @TableField("created_by")
    @ApiModelProperty("创建人")
    Long createdBy;

    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @ApiModelProperty("创建时间")
    LocalDateTime createdAt;


    @TableField(exist = false)
    @ApiModelProperty("最近更新人")
    @Excel(name = "最近更新人", width = 30, orderNum = "17")
    String updatedByName;


    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty("更新时间")
    @Excel(name = "最近更新人", width = 30, orderNum = "18", format = "yyyy/MM/dd HH:mm:ss")
    LocalDateTime updatedAt;


    @TableField("direct_status")
    @ApiModelProperty("透传状态")
    @ExcelIgnore
    Integer directStatus;


    @TableField("status")
    @ApiModelProperty("状态")
    @ExcelIgnore
    ReaderStatus status;

    @TableField("error_info")
    @ApiModelProperty("状态")
    @ExcelIgnore
    ReaderStatus errorInfo;


    @TableField("versions")
    @ApiModelProperty("版本")
    @ExcelIgnore
    String versions;

    @TableField(value = "updated_by")
    @ApiModelProperty("更新人")
    Long updatedBy;


    @TableField("deleted")
    @ApiModelProperty("是否删除  0未删除  1删除")
    @TableLogic
    Integer deleted;


    @TableField(exist = false)
    @ApiModelProperty("连接状态1已连接0断线")
    Integer connectState;


}
