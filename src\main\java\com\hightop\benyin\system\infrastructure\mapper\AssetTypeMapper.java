package com.hightop.benyin.system.infrastructure.mapper;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.yulichang.base.MPJBaseMapper;
import com.hightop.benyin.system.infrastructure.entity.AssetType;
import org.apache.ibatis.annotations.Delete;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public interface AssetTypeMapper extends MPJBaseMapper<AssetType> {
    /**
     * 获得所有未删除{@link  AssetType#getIsAvailable()}的类型列表
     *
     * @return {@link List}
     */
    default List<AssetType> getAvailableAssetTypes() {
        return this.selectList(Wrappers.<AssetType>lambdaQuery().eq(AssetType::getIsAvailable, true));
    }

    /**
     * 获得所有未删除{@link  AssetType#getIsAvailable()}类型的id和类型映射
     *
     * @return {@link Map}
     */
    default Map<Long, AssetType> getAvailableAssetTypeMapping() {
        return this.getAvailableAssetTypes().stream().collect(Collectors.toMap(AssetType::getId, it -> it));
    }

    /**
     * 获得启用状态{@link  AssetType#getIsEnable()}的类型列表
     *
     * @return {@link List}
     */
    default List<AssetType> getEnabledAssetTypes() {
        return this.getAvailableAssetTypes().stream().filter(AssetType::getIsEnable).collect(Collectors.toList());
    }

    /**
     * 获得所有启用状态{@link  AssetType#getIsEnable()}类型的id和类型映射
     *
     * @return {@link Map}
     */
    default Map<Long, AssetType> getEnabledAssetTypeMapping() {
        return this.getEnabledAssetTypes().stream().collect(Collectors.toMap(AssetType::getId, it -> it));
    }

    @Delete("TRUNCATE TABLE B_ASSET_TYPE")
    void clearAssetType();

}
