package com.hightop.benyin.system.application.service;

import com.github.yulichang.toolkit.MPJWrappers;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.hightop.benyin.rfid.infrastructure.entity.RfidReader;
import com.hightop.benyin.system.api.vo.query.DataLogQuery;
import com.hightop.benyin.system.domain.service.DataLogDomainService;
import com.hightop.benyin.system.domain.service.LocationDomainService;
import com.hightop.benyin.system.infrastructure.entity.DataLog;
import com.hightop.benyin.system.infrastructure.entity.DepartmentInfo;
import com.hightop.benyin.system.infrastructure.entity.Location;
import com.hightop.benyin.system.infrastructure.entity.UserInfo;
import com.hightop.fario.base.util.StringUtils;
import com.hightop.fario.common.mybatis.DataGrid;
import com.hightop.fario.common.mybatis.PageQuery;
import com.hightop.fario.common.mybatis.util.PageHelper;
import com.hightop.magina.standard.ums.user.basic.UserBasic;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 数据操作日志管理服务
 *
 * <AUTHOR>
 * @date 2024-10-12 16:36:01
 */
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Service
public class DataLogService {

    DataLogDomainService dataLogDomainService;
    LocationDomainService locationDomainService;
    /**
     * 用户日志分页查询
     *
     * @param pageQuery {@link PageQuery}
     * @return {@link DataGrid}
     */
    public DataGrid<DataLog> userLogPage(DataLogQuery pageQuery) {
        MPJLambdaWrapper<Object> wrapper = MPJWrappers.lambdaJoin()
                .selectAll(DataLog.class)
                .selectAs(UserInfo::getCode, DataLog::getCode)
                .selectAs(UserInfo::getName, DataLog::getName)
                .leftJoin(UserBasic.class, UserBasic::getId, DataLog::getCreatedBy)
                .leftJoin(UserInfo.class, UserInfo::getId, DataLog::getBusinessId);
        addCondition(pageQuery, wrapper);
        wrapper.like(StringUtils.isNotBlank(pageQuery.getCode()), UserInfo::getCode, pageQuery.getCode())
                .like(StringUtils.isNotBlank(pageQuery.getCode()),UserInfo::getName, pageQuery.getName());
        wrapper.orderByDesc(DataLog::getCreatedAt);
        return PageHelper.startPage(pageQuery, p ->
                this.dataLogDomainService.selectJoinList(DataLog.class, wrapper)
        );
    }

    /**
     * 部门日志分页查询
     *
     * @param pageQuery {@link PageQuery}
     * @return {@link DataGrid}
     */
    public DataGrid<DataLog> deptLogPage(DataLogQuery pageQuery) {
        MPJLambdaWrapper<Object> wrapper = MPJWrappers.lambdaJoin()
                .selectAll(DataLog.class)
                .selectAs(DepartmentInfo::getCode, DataLog::getCode)
                .selectAs(DepartmentInfo::getName, DataLog::getName)
                .leftJoin(UserBasic.class, UserBasic::getId, DataLog::getCreatedBy)
                .leftJoin(DepartmentInfo.class, DepartmentInfo::getId, DataLog::getBusinessId);
        addCondition(pageQuery, wrapper);
        wrapper.like(StringUtils.isNotBlank(pageQuery.getCode()), DepartmentInfo::getCode, pageQuery.getCode())
                        .like(StringUtils.isNotBlank(pageQuery.getCode()),DepartmentInfo::getName, pageQuery.getName());
        wrapper.orderByDesc(DataLog::getCreatedAt);
        return PageHelper.startPage(pageQuery, p ->
                this.dataLogDomainService.selectJoinList(DataLog.class, wrapper)
        );
    }

    /**
     * 用户日志分页查询
     *
     * @param pageQuery {@link PageQuery}
     * @return {@link DataGrid}
     */
    public DataGrid<DataLog> locationLogPage(DataLogQuery pageQuery) {
        MPJLambdaWrapper<Object> wrapper = MPJWrappers.lambdaJoin()
                .selectAll(DataLog.class)
                .selectAs(Location::getCode, DataLog::getCode)
                .selectAs(Location::getFullName, DataLog::getName)
                .leftJoin(UserBasic.class, UserBasic::getId, DataLog::getCreatedBy)
                .leftJoin(Location.class, Location::getId, DataLog::getBusinessId);
        addCondition(pageQuery, wrapper);
        wrapper.like(StringUtils.isNotBlank(pageQuery.getCode()), Location::getCode, pageQuery.getCode())
                .like(StringUtils.isNotBlank(pageQuery.getName()), Location::getFullName, pageQuery.getName());

        wrapper.orderByDesc(DataLog::getCreatedAt);
        return PageHelper.startPage(pageQuery, p ->
                this.dataLogDomainService.selectJoinList(DataLog.class, wrapper)
        );
    }

    /**
     * 用户日志分页查询
     *
     * @param pageQuery {@link PageQuery}
     * @return {@link DataGrid}
     */
    public DataGrid<DataLog> readerLogPage(DataLogQuery pageQuery) {
        MPJLambdaWrapper<Object> wrapper = MPJWrappers.lambdaJoin()
                .selectAll(DataLog.class)
                .selectAs(RfidReader::getCode, DataLog::getCode)
                .selectAs(RfidReader::getDeviceId, DataLog::getName)
                .selectAs(Location::getFullName, DataLog::getLocation)
                .leftJoin(UserBasic.class, UserBasic::getId, DataLog::getCreatedBy)
                .leftJoin(RfidReader.class, RfidReader::getId, DataLog::getBusinessId)
                .leftJoin(Location.class, Location::getId, RfidReader::getLocationId);
        addCondition(pageQuery, wrapper);
        wrapper.like(StringUtils.isNotBlank(pageQuery.getCode()), RfidReader::getCode, pageQuery.getCode())
                .like(StringUtils.isNotBlank(pageQuery.getName()), RfidReader::getDeviceId, pageQuery.getName());
        wrapper.orderByDesc(DataLog::getCreatedAt);
        return PageHelper.startPage(pageQuery, p ->
                this.dataLogDomainService.selectJoinList(DataLog.class, wrapper)
        );
    }

    /**
     * 添加查询条件
     *
     * @param pageQuery {@link DataLogQuery}
     * @param wrapper   {@link MPJLambdaWrapper}
     */
    private void addCondition(DataLogQuery pageQuery, MPJLambdaWrapper<Object> wrapper) {
        wrapper.like(StringUtils.isNotBlank(pageQuery.getBusinessCode()), DataLog::getBusinessCode, pageQuery.getBusinessCode())
                .like(StringUtils.isNotBlank(pageQuery.getOriData()), DataLog::getOriData, pageQuery.getOriData())
                .like(StringUtils.isNotBlank(pageQuery.getNewData()), DataLog::getNewData, pageQuery.getNewData())
                .eq(pageQuery.getBusinessType() != null, DataLog::getBusinessType, pageQuery.getBusinessType())
                .like(StringUtils.isNotBlank(pageQuery.getOperationType()), DataLog::getOperationType, pageQuery.getOperationType())
                .like(StringUtils.isNotBlank(pageQuery.getCreatedBy()), UserBasic::getName, pageQuery.getCreatedBy())
                .ge(Objects.nonNull(pageQuery.getStartDate()), DataLog::getCreatedAt, pageQuery.getStartDate() + " 00:00:00")
                .le(Objects.nonNull(pageQuery.getEndDate()), DataLog::getCreatedAt, pageQuery.getEndDate() + " 23:59:59");
    }

}
