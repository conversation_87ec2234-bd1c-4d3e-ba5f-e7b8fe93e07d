package com.hightop.benyin.rfid.application.vo.query;

import com.hightop.benyin.system.api.vo.query.AuthPageQuery;
import com.hightop.fario.common.mybatis.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldDefaults;

import java.util.List;

/**
 * 资产查询DTO
 * <AUTHOR>
 * @date 2024-10-12 16:36:01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@FieldDefaults(level = AccessLevel.PRIVATE)
@ApiModel("资产流程查询DTO")
public class RfidAssetFlowQuery extends AuthPageQuery {

    @ApiModelProperty("id")
    Long id;

    @ApiModelProperty("单号")
    String code;

    @ApiModelProperty("部门ids")
    List<Long> departmentIds;

    @ApiModelProperty("部门")
    String departmentName;

    @ApiModelProperty("登记人")
    String createdBy;

    @ApiModelProperty("保管人姓名")
    String applyName;

    @ApiModelProperty("审核人")
    String approveName;

    @ApiModelProperty("位置编码")
    List<String> locations;

    @ApiModelProperty("审核状态")
    List<String> status;

    @ApiModelProperty("位置")
    String location;

    @ApiModelProperty("基站")
    String deviceId;

    @ApiModelProperty("操作类型")
    List<String> operateTypes;

    @ApiModelProperty("登记起始时间")
    String startDate;

    @ApiModelProperty("登记截止时间")
    String endDate;

    @ApiModelProperty("审核起始时间")
    String startApproveDate;

    @ApiModelProperty("审核截止时间")
    String endApproveDate;

    @ApiModelProperty("领用起始时间")
    String startApplyDate;

    @ApiModelProperty("领用截止时间")
    String endApplyDate;
}
