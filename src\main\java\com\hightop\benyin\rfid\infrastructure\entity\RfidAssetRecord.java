package com.hightop.benyin.rfid.infrastructure.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.hightop.benyin.rfid.infrastructure.enums.AssetFlowStatus;
import com.hightop.magina.standard.ums.user.bind.UserBind;
import com.hightop.magina.standard.ums.user.bind.UserEntry;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.time.LocalDateTime;

/**
 * 资产审核记录表
 *
 * <AUTHOR>
 * @date 2023-11-15 17:19:37
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@TableName("b_rfid_asset_record")
@ApiModel
public class RfidAssetRecord {

    @TableId(value = "id", type =IdType.ASSIGN_ID)
    @ApiModelProperty("id")
    Long id;

    @TableField("status")
    @ApiModelProperty("状态")
    AssetFlowStatus status;

    @TableField("flow_id")
    @ApiModelProperty("流水id")
    Long flowId;

    @TableField("content")
    @ApiModelProperty("内容")
    String content;

    @TableField("created_by")
    @ApiModelProperty("创建人")
    @UserBind
    UserEntry createdBy;

    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @ApiModelProperty("创建时间")
    LocalDateTime createdAt;


}
