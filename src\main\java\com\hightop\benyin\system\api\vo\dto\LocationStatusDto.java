package com.hightop.benyin.system.api.vo.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotNull;


/**
 * 位置启停DTO
 *
 * @Author: X.S
 * @date 2024/09/29 13:58
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@ApiModel("位置启停DTO")
public class LocationStatusDto {

    @NotNull(message = "启停状态不能为空")
    @ApiModelProperty("启停状态")
    Integer status;

    @ApiModelProperty("名称")
    String name;

    /**
     * 仓库id
     */
    @ApiModelProperty(value = "仓库id")
    @NotNull(message = "位置id不能为空")
    Long id;
}