server:
  servlet:
    context-path: /api
  port: 8081

spring:
  servlet:
    multipart:
      enabled: true
      max-file-size: 20MB
      max-request-size: 20MB
  profiles:
    active: dev
  application:
    name: asset-api
  datasource:
    hikari:
      pool-name: HIKARI-POOL
      # 核心数的2倍 16核配置 默认10
      maximum-pool-size: 32
      # 参考数据库连接超时时间 默认30分钟
      max-lifetime: 1800000
      # 数据库连接心跳检测时间间隔 每分钟检测一次 默认不检测
      keepalive-time: 60000
      # 连接有效性测试语句 若不配置使用JDBC4.0 isValid
      connection-test-query: SELECT 1

# mybatis逻辑删除配置
mybatis-plus:
  typeEnumsPackage: com.hightop.benyin.*
  global-config:
    db-config:
      logic-delete-value: 1
      logic-not-delete-value: 0
      logic-delete-field: 'deleted'

benyin:
  tencent:
    # 腾讯云cos
    cos:
      bucket: benyin-1315885374
      region: ap-chengdu
      # 默认临时目录 prod应该修改为其他目录便于区分
      prefix: temp/
      secret-id: AKIDUq0wd6LVSBi17ze5e082iBQSGqENIFzX
      secret-key: NYUEqFQODrLeppKhv31nOlxveFertjPu

logging:
  level:
    feign: debug
