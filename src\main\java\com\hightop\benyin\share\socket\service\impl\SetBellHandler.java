package com.hightop.benyin.share.socket.service.impl;

import com.hightop.benyin.share.socket.enums.ResultEnums;
import com.hightop.benyin.share.socket.service.CommandHandler;
import com.hightop.benyin.share.socket.util.MsgUtil;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 设置基站的蜂鸣器参数-回执处理
 */
@Service
@Slf4j
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class SetBellHandler implements CommandHandler {

    @Override
    public void handle(String clientName, String deviceId, Integer length, String params) {
        log.info("设置基站的蜂鸣器参数-回执处理, clientName: {}, 结果: {}", clientName, ResultEnums.getName(MsgUtil.stringToInteger(params)));
    }
}
