package com.hightop.benyin.system.api.vo.dto;

import com.hightop.benyin.system.infrastructure.entity.Location;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotNull;

@Data
@EqualsAndHashCode(callSuper = true)
@FieldDefaults(level = AccessLevel.PRIVATE)
public class LocationUpdateDto extends LocationAddDto {
    @ApiModelProperty("id")
    @NotNull(message = "id不能为空")
    Long id;

    @Override
    public Location toLocation() {
        return super.toLocation().setId(this.id);
    }
}
