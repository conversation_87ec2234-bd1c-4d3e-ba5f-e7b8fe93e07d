package com.hightop.benyin.rfid.application.vo.dto;

import com.hightop.benyin.rfid.infrastructure.enums.TakeRangeEnums;
import com.hightop.benyin.rfid.infrastructure.enums.TakeTypeEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * 盘点DTO
 *
 * @Author: X.S
 * @date 2024/09/29 13:58
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@ApiModel("盘点DTO")
public class RfidAssetTakeDto {

    @ApiModelProperty(value = "盘点类型")
    @NotNull(message = "盘点类型不能为空")
    TakeTypeEnums takeType;

    @ApiModelProperty(value = "盘点范围")
    @NotNull(message = "盘点范围不能为空")
    TakeRangeEnums takeRange;

    @ApiModelProperty(value = "盘点部门(可多选)")
    List<Long> departmentIds;

    @ApiModelProperty(value = "盘点位置(可多选)")
    List<Long> locations;

    @ApiModelProperty(value = "保管人(可多选)")
    List<Long> applyIds;

}