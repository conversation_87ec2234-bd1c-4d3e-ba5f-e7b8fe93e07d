package com.hightop.benyin.system.application.service;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.github.yulichang.toolkit.MPJWrappers;
import com.hightop.benyin.share.infrastructure.util.DictUtil;
import com.hightop.benyin.share.infrastructure.util.DownloadResponseUtil;
import com.hightop.benyin.share.infrastructure.util.KeyUtils;
import com.hightop.benyin.system.api.vo.UserManageExtendVo;
import com.hightop.benyin.system.api.vo.dto.UserAddDto;
import com.hightop.benyin.system.api.vo.dto.UserResetPassDto;
import com.hightop.benyin.system.api.vo.dto.UserStatusDto;
import com.hightop.benyin.system.api.vo.dto.UserUpdateDto;
import com.hightop.benyin.system.api.vo.excel.UserExcel;
import com.hightop.benyin.system.api.vo.query.UserExtendPageQuery;
import com.hightop.benyin.system.api.vo.query.UserRolePageQuery;
import com.hightop.benyin.system.application.handler.UserExcelVerifyHandler;
import com.hightop.benyin.system.domain.service.DepartmentInfoDomainService;
import com.hightop.benyin.system.domain.service.UserInfoDomainService;
import com.hightop.benyin.system.domain.service.UserPrivacyInfoDomainService;
import com.hightop.benyin.system.infrastructure.entity.DepartmentInfo;
import com.hightop.benyin.system.infrastructure.entity.Location;
import com.hightop.benyin.system.infrastructure.entity.UserInfo;
import com.hightop.benyin.system.infrastructure.entity.UserPrivacyInfo;
import com.hightop.fario.base.util.CollectionUtils;
import com.hightop.fario.base.util.StringUtils;
import com.hightop.fario.common.mybatis.DataGrid;
import com.hightop.fario.common.mybatis.util.PageHelper;
import com.hightop.magina.casual.key.KeyDomainService;
import com.hightop.magina.casual.session.ApplicationSessions;
import com.hightop.magina.core.exception.MaginaException;
import com.hightop.magina.standard.cipher.CipherText;
import com.hightop.magina.standard.ums.role.Role;
import com.hightop.magina.standard.ums.role.api.RoleVo;
import com.hightop.magina.standard.ums.user.basic.UserBasic;
import com.hightop.magina.standard.ums.user.basic.UserState;
import com.hightop.magina.standard.ums.user.basic.UserType;
import com.hightop.magina.standard.ums.user.manage.UserManageVo;
import com.hightop.magina.standard.ums.user.manage.UserResetPasswordDto;
import com.hightop.magina.standard.ums.user.privacy.UserPrivacy;
import com.hightop.magina.standard.ums.user.privacy.UserSex;
import com.hightop.magina.standard.ums.user.role.UserRole;
import com.hightop.magina.standard.ums.user.role.UserRoleDomainService;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Service
public class UserExtendService {

    UserPrivacyInfoDomainService userPrivacyDomainService;
    RedisTemplate<String, String> redisTemplate;
    KeyDomainService keyDomainService;
    UserInfoDomainService userInfoDomainService;
    DepartmentInfoDomainService departmentInfoDomainService;
    UserRoleDomainService userRoleDomainService;

    /**
     * 用户分页查询
     *
     * @param pageQuery 查询条件
     * @return {@link DataGrid}
     */
    public DataGrid<UserManageExtendVo> page(UserExtendPageQuery pageQuery) {
        return PageHelper.startPage(pageQuery, p -> this.list(pageQuery));
    }

    /**
     * 用户分页查询
     *
     * @param pageQuery 查询条件
     * @return {@link DataGrid}
     */
    public DataGrid<UserManageExtendVo> authPage(UserExtendPageQuery pageQuery) {
        if (CollectionUtils.isEmpty(pageQuery.getDepartmentIds())) {
            //获取当前用户所属公司的部门列表
            pageQuery.setDepartmentIds(userInfoDomainService.getCurrCompanyDeptList());
        }
        return PageHelper.startPage(pageQuery, p -> this.list(pageQuery));
    }

    private List<UserManageExtendVo> list(UserExtendPageQuery pageQuery) {
        LocalDateTime now = LocalDateTime.now();
        return this.userInfoDomainService.getBaseMapper()
                .selectJoinList(
                        UserManageExtendVo.class,
                        MPJWrappers.<UserInfo>lambdaJoin()
                                .selectAll(UserInfo.class)
                                .selectAll(UserPrivacyInfo.class)
                                .selectAs(DepartmentInfo::getName, UserManageExtendVo::getDepartmentName)
                                .selectAs(DepartmentInfo::getCode, UserManageExtendVo::getDepartmentCode)
                                .selectAs(UserBasic::getName, UserManageExtendVo::getUpdatedByName)
                                .selectAs(Location::getFullName, UserManageExtendVo::getLocation)
                                .selectAs(Location::getCode, UserManageExtendVo::getLocationCode)
                                // 严禁列名重复
                                .selectIgnore(UserPrivacyInfo::getId)
                                .innerJoin(UserPrivacyInfo.class, UserPrivacyInfo::getId, UserInfo::getId)
                                .leftJoin(DepartmentInfo.class, DepartmentInfo::getId, UserInfo::getDepartmentId)
                                .leftJoin(Location.class, Location::getId, UserInfo::getLocationId)
                                .leftJoin(UserBasic.class, UserBasic::getId, UserInfo::getUpdatedBy)
                                .eq(UserInfo::getIsBuildIn, false)
                                .in(CollectionUtils.isNotEmpty(pageQuery.getDepartmentIds()), UserInfo::getDepartmentId, pageQuery.getDepartmentIds())
                                .in(CollectionUtils.isNotEmpty(pageQuery.getLocations()), UserInfo::getLocationId, pageQuery.getLocations())
                                .like(StringUtils.isNotEmpty(pageQuery.getDepartmentCode()), DepartmentInfo::getCode, pageQuery.getDepartmentCode())
                                .like(StringUtils.isNotEmpty(pageQuery.getName()), UserInfo::getName, pageQuery.getName())
                                .like(StringUtils.isNotEmpty(pageQuery.getCode()), UserInfo::getCode, pageQuery.getCode())
                                .like(StringUtils.isNotEmpty(pageQuery.getUpdatedByName()), UserBasic::getName, pageQuery.getUpdatedByName())
                                .like(StringUtils.isNotEmpty(pageQuery.getIdentityCardNumber()), UserPrivacyInfo::getIdentityCardNumber, pageQuery.getIdentityCardNumber())
                                .like(StringUtils.isNotEmpty(pageQuery.getEmail()), UserPrivacyInfo::getEmail, pageQuery.getEmail())
                                .like(StringUtils.isNotEmpty(pageQuery.getMobileNumber()), UserPrivacyInfo::getMobileNumber, pageQuery.getMobileNumber())
                                // 用户类型
                                .eq(Objects.nonNull(pageQuery.getType()), UserInfo::getType, pageQuery.getType())
                                .eq(Objects.nonNull(pageQuery.getId()), UserInfo::getId, pageQuery.getId())
                                .ge(StringUtils.isNotBlank(pageQuery.getStartDate()), UserInfo::getCreatedAt, pageQuery.getStartDate() + " 00:00:00")
                                .le(StringUtils.isNotBlank(pageQuery.getEndDate()), UserInfo::getCreatedAt, pageQuery.getEndDate() + " 23:59:59")
                                .ge(StringUtils.isNotBlank(pageQuery.getStartUpdateDate()), UserInfo::getUpdatedAt, pageQuery.getStartUpdateDate() + " 00:00:00")
                                .le(StringUtils.isNotBlank(pageQuery.getEndUpdateDate()), UserInfo::getUpdatedAt, pageQuery.getEndUpdateDate() + " 23:59:59")
                                .ge(StringUtils.isNotBlank(pageQuery.getStartDisableDate()), UserInfo::getAccountExpireAt, pageQuery.getStartDisableDate() + " 00:00:00")
                                .le(StringUtils.isNotBlank(pageQuery.getEndDisableDate()), UserInfo::getAccountExpireAt, pageQuery.getEndDisableDate() + " 23:59:59")
                                .ge(StringUtils.isNotBlank(pageQuery.getStartEnableDate()), UserInfo::getCreatedAt, pageQuery.getStartEnableDate() + " 00:00:00")
                                .le(StringUtils.isNotBlank(pageQuery.getEndEnableDate()), UserInfo::getCreatedAt, pageQuery.getEndEnableDate() + " 23:59:59")

                                .eq(Objects.nonNull(pageQuery.getIsAvailable()), UserInfo::getIsAvailable, pageQuery.getIsAvailable())
                                // 用户状态查询
                                .and(
                                        Objects.nonNull(pageQuery.getState()),
                                        m -> {
                                            switch (pageQuery.getState()) {
                                                case NORMAL: {
                                                    // 正常状态
                                                    m.eq(UserInfo::getState, UserState.NORMAL)
                                                            // 排除在锁定时间内的
                                                            .and(t ->
                                                                    t.isNull(UserInfo::getLockedExpireAt)
                                                                            .or()
                                                                            .lt(UserInfo::getLockedExpireAt, now)
                                                            )
                                                            // 长期用户或短期用户未过期
                                                            .and(t ->
                                                                    t.eq(UserInfo::getType, UserType.PERMANENT)
                                                                            .or(n ->
                                                                                    n.eq(UserInfo::getType, UserType.TEMPORARY)
                                                                                            .ge(
                                                                                                    UserInfo::getAccountExpireAt,
                                                                                                    now.toLocalDate()
                                                                                            )
                                                                            )
                                                            );
                                                    break;
                                                }
                                                case LOCKED: {
                                                    // 锁定状态 排除已过锁定时间的
                                                    m.eq(UserInfo::getState, UserState.NORMAL)
                                                            .ge(UserInfo::getLockedExpireAt, now);
                                                    break;
                                                }
                                                case SUSPENDED: {
                                                    m.and(n ->
                                                            // 等于休眠状态
                                                            n.eq(UserInfo::getState, UserState.SUSPENDED)
                                                                    .or(t ->
                                                                            // 或者正常状态、短期用户、已失效
                                                                            t.eq(UserInfo::getState, UserState.NORMAL)
                                                                                    .eq(UserInfo::getType, UserType.TEMPORARY)
                                                                                    .lt(UserInfo::getAccountExpireAt, now.toLocalDate())
                                                                                    // 锁定状态优先 排除锁定状态
                                                                                    .and(j ->
                                                                                            j.isNull(UserInfo::getLockedExpireAt)
                                                                                                    .or()
                                                                                                    .lt(UserInfo::getLockedExpireAt, now)
                                                                                    )
                                                                    )
                                                    );
                                                }
                                                default: {
                                                    break;
                                                }
                                            }
                                        }
                                )
                                .orderByDesc(UserInfo::getId)
                );
    }

    public List<UserManageExtendVo> getUserList(UserExtendPageQuery pageQuery) {
        String role = userInfoDomainService.getCurrRoleCode();
        //负责人查看自己负责的部门
        if(!ApplicationSessions.code().equals(DictUtil.ADMIN)) {

            if (DictUtil.LEADER_ROLE.equals(role)) {
                List<Long> departmentInfos = userInfoDomainService.getCurrDeptIds();
                pageQuery.setDepartmentIds(departmentInfos);
            }
            //员工查看自己部门
            if (DictUtil.STAFF_ROLE.equals(role)) {
                pageQuery.setId(ApplicationSessions.id());
            }
        }
        return this.userInfoDomainService.getBaseMapper()
                .selectJoinList(
                        UserManageExtendVo.class,
                        MPJWrappers.<UserInfo>lambdaJoin()
                                .select(UserInfo::getId)
                                .select(UserInfo::getName)
                                .eq(UserInfo::getIsAvailable, Boolean.TRUE)
                                .eq(UserInfo::getIsBuildIn, Boolean.FALSE)
                                .in(CollectionUtils.isNotEmpty(pageQuery.getDepartmentIds()), UserInfo::getDepartmentId, pageQuery.getDepartmentIds())
                                .eq(pageQuery.getDepartmentId() != null, UserInfo::getDepartmentId, pageQuery.getDepartmentId())
                                .eq(pageQuery.getLocationId() != null, UserInfo::getLocationId, pageQuery.getLocationId())
                                .eq(pageQuery.getId() != null, UserInfo::getId, pageQuery.getId())
                                .in(CollectionUtils.isNotEmpty(pageQuery.getLocations()), UserInfo::getLocationId, pageQuery.getLocations())
                );
    }

    /**
     * 用户启停用
     *
     * @param userStatusDto {@link UserStatusDto}
     * @return true/false
     */
    public boolean enableDisable(UserStatusDto userStatusDto) {
        UserInfo userBasic = userInfoDomainService.getById(userStatusDto.getId());
        if (Objects.isNull(userBasic)) {
            throw new MaginaException("用户不存在！");
        }
        userBasic.setIsAvailable(userStatusDto.getIsAvailable());
        if (!userStatusDto.getIsAvailable()) {
            userBasic.setAccountExpireAt(LocalDateTime.now());
        } else {
            userBasic.setAccountExpireAt(null);
        }
        return userInfoDomainService.updateById(userBasic);
    }

    /**
     * 用户添加
     *
     * @param userAddDto {@link UserAddDto}
     * @return 是否添加成功
     */
    public boolean add(UserAddDto userAddDto) {
        long userId = IdWorker.getId();
        LocalDateTime now = LocalDateTime.now();
        UserInfo userBasic =
                userAddDto.toBasic(userId)
                        // 初始化数据
                        .setState(UserState.NORMAL)
                        .setIsAvailable(Boolean.TRUE)
                        .setIsBuildIn(Boolean.FALSE)
                        .setName(userAddDto.getName())
                        .setType(UserType.PERMANENT)
                        .setCode(userAddDto.getCode())
                        .setDepartmentId(userAddDto.getDepartmentId())
                        .setLocationId(userAddDto.getLocationId())
                        .setCreatedBy(ApplicationSessions.id())
                        .setCreatedAt(now)
                        .setUpdatedAt(now);
        UserPrivacyInfo userPrivacy = userAddDto.toPrivacy(userId, this.keyDomainService);
        Long count = userInfoDomainService.lambdaQuery()
                .eq(UserInfo::getCode, userAddDto.getCode()).count();

        if (count > 0) {
            throw new MaginaException("用户账号已存在！");
        }
        // 保存基本信息
        this.userInfoDomainService.save(userBasic);
        // 保存私密信息
        this.userPrivacyDomainService.save(userPrivacy);
        return true;
    }


    /**
     * 用户修改
     *
     * @param userUpdateDto {@link UserStatusDto}
     * @return true/false
     */
    public boolean update(UserUpdateDto userUpdateDto) {
        UserInfo userInfo = userInfoDomainService.getById(userUpdateDto.getId());
        if (Objects.isNull(userInfo)) {
            throw new MaginaException("用户不存在！");
        }
        userInfo.setIsAvailable(userUpdateDto.getIsAvailable());
        userInfo.setCode(userUpdateDto.getCode());
        userInfo.setName(userUpdateDto.getName());
        userInfo.setDepartmentId(userUpdateDto.getDepartmentId());
        userInfo.setLocationId(userUpdateDto.getLocationId());
        if (!userUpdateDto.getIsAvailable()) {
            userInfo.setAccountExpireAt(LocalDateTime.now());
        } else {
            userInfo.setAccountExpireAt(null);
        }
        UserPrivacyInfo userPrivacy = userUpdateDto.toPrivacy(userInfo.getId(), this.keyDomainService);
        userPrivacyDomainService.saveOrUpdate(userPrivacy);
        return userInfoDomainService.updateById(userInfo);
    }

    /**
     * 下载模板
     *
     * @param response
     * @return
     */
    public Boolean downTemplate(HttpServletResponse response) {
        List<UserExcel> excelList = new ArrayList<>();
        try {
            //页面下载设置
            DownloadResponseUtil.addDownLoadHeader(response, "用户信息导入模板.xlsx");
            //将并添加信息，实体类结构，类的数据做成excel表格对象workbook
            Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams(),
                    UserExcel.class, excelList
            );
            workbook.write(response.getOutputStream());
        } catch (IOException e) {
            e.printStackTrace();
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }


    /**
     * 用户密码修改
     *
     * @param resetPasswordDto {@link UserResetPasswordDto}
     * @return 是否成功
     */
    public boolean updatePassword(UserResetPassDto resetPasswordDto) {

        String privateKey = redisTemplate.opsForValue().get(DictUtil.PRIVATE_KEY + resetPasswordDto.getCipherToken());
        // 原密码解密
        String oriPassword = KeyUtils.decrypt(privateKey, resetPasswordDto.getPassword());
        UserPrivacyInfo userPrivacy = this.userPrivacyDomainService.getNonNullById(resetPasswordDto.getId());
        if (!userPrivacy.isPasswordMatch(oriPassword)) {
            throw new MaginaException("原密码错误！");
        }
        String password = this.keyDomainService.decrypt(
                resetPasswordDto.getCipherToken(), resetPasswordDto.getNewPassword());
        return this.userPrivacyDomainService.resetPassword(resetPasswordDto.getId(), password, false);
    }

    /**
     * 用户密码重置
     *
     * @param resetPasswordDto {@link UserResetPasswordDto}
     * @return 是否成功
     */
    public boolean resetPassword(UserResetPasswordDto resetPasswordDto) {
        String password = this.keyDomainService.decrypt(
                resetPasswordDto.getCipherToken(), resetPasswordDto.getNewPassword());
        return this.userPrivacyDomainService.resetPassword(resetPasswordDto.getId(), password, false);
    }

    /**
     * 导入用户
     *
     * @param file
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean importData(MultipartFile file) {
        List<UserExcel> excels;
        try {
            InputStream in = file.getInputStream();
            ImportParams params = new ImportParams();
            params.setNeedVerify(true);
            params.setVerifyHandler(new UserExcelVerifyHandler());
            ExcelImportResult<UserExcel> excelImportResult = ExcelImportUtil.importExcelMore(in, UserExcel.class, params);
            if (excelImportResult.isVerifyFail()) {
                String errorMsg = excelImportResult.getFailList().stream().map(v -> {
                    return "第" + v.getRowNum() + "行，" + v.getErrorMsg();
                }).collect(Collectors.joining("！"));
                throw new MaginaException(errorMsg);
            }
            excels = excelImportResult.getList();
            Map<String, List<UserExcel>> stringListMap = excels.stream().collect(Collectors.groupingBy(UserExcel::getCode));
            for (Map.Entry<String, List<UserExcel>> entry : stringListMap.entrySet()) {
                List<UserExcel> value = entry.getValue();
                if (value.size() > 1) {
                    throw new MaginaException("用户账号" + entry.getKey() + "本次导入存在重复数据");
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new MaginaException("导入失败！原因：" + e.getMessage());
        }

        if (CollectionUtils.isEmpty(excels)) {
            throw new MaginaException("导入失败！解析数据为空！");
        }
        //存数据库
        for (UserExcel data : excels) {
            this.saveUser(data);
        }
        return true;
    }

    public boolean saveUser(UserExcel userExcel) {

        LocalDateTime now = LocalDateTime.now();
        UserInfo userInfo = new UserInfo()
                .setId(userExcel.getId())
                .setCode(userExcel.getCode())
                .setName(userExcel.getName())
                .setDepartmentId(userExcel.getDepartmentId())
                .setLocationId(userExcel.getLocationId())
                .setType(UserType.PERMANENT)
                .setAccountExpireAt(null)
                .setState(UserState.NORMAL)
                .setIsAvailable(Boolean.TRUE)
                .setIsBuildIn(Boolean.FALSE)
                .setCreatedBy(ApplicationSessions.id())
                .setCreatedAt(now)
                .setUpdatedAt(now);

        UserPrivacyInfo userPrivacy = new UserPrivacyInfo()
                .setId(userInfo.getId())
                .setSex(StringUtils.isEmpty(userExcel.getSex()) ? UserSex.UNKNOWN : userExcel.getSexEnum())
                .setEmail(new CipherText(userExcel.getEmail()))
                .setMobileNumber(new CipherText(userExcel.getMobileNumber()))
                .setIdentityCardNumber(new CipherText(userExcel.getIdentityCardNumber()))
                .setPassword(userExcel.getCode() + "123");

//         保存基本信息
        this.userInfoDomainService.saveOrUpdate(userInfo);
//         保存私密信息
        this.userPrivacyDomainService.save(userPrivacy);
        return true;
    }

    /**
     * 查询角色下的用户
     *
     * @param roleId    角色id
     * @param pageQuery 分页查询条件
     * @return {@link DataGrid}
     */
    public DataGrid<UserManageVo> getUsersByRole(Long roleId, UserRolePageQuery pageQuery) {
        return
                PageHelper.startPage(
                        pageQuery,
                        p ->
                                this.userRoleDomainService.getBaseMapper()
                                        .selectJoinList(
                                                UserManageVo.class,
                                                MPJWrappers.<UserRole>lambdaJoin()
                                                        .selectAll(UserInfo.class)
                                                        .selectAll(UserPrivacy.class)
                                                        // 严禁列名重复
                                                        .selectIgnore(UserPrivacy::getId)
                                                        .innerJoin(
                                                                UserInfo.class,
                                                                on ->
                                                                        on.eq(UserInfo::getId, UserRole::getUserId)
                                                                                .eq(UserInfo::getIsAvailable, true)
                                                                                .eq(UserRole::getRoleId, roleId)
                                                        )
                                                        .innerJoin(UserPrivacy.class, UserPrivacy::getId, UserInfo::getId)
                                                        .like(StringUtils.isNotEmpty(p.getName()), UserInfo::getName, p.getName())
                                                        .like(StringUtils.isNotEmpty(p.getCode()), UserInfo::getCode, p.getCode())
                                                        .orderByDesc(UserInfo::getId)
                                        )
                );
    }

    /**
     * 角色添加用户查询
     *
     * @param roleId    角色id
     * @param pageQuery 查询条件
     * @return {@link DataGrid}
     */
    public DataGrid<UserManageVo> toBeAddedUsers(Long roleId, UserRolePageQuery pageQuery) {
        return
                PageHelper.startPage(
                        pageQuery,
                        p ->
                                this.userInfoDomainService.getBaseMapper()
                                        .selectJoinList(
                                                UserManageVo.class,
                                                MPJWrappers.<UserInfo>lambdaJoin()
                                                        .selectAll(UserInfo.class)
                                                        .selectAll(UserPrivacy.class)
                                                        // 严禁列名重复
                                                        .selectIgnore(UserPrivacy::getId)
                                                        .leftJoin(
                                                                UserRole.class,
                                                                on ->
                                                                        on.eq(UserRole::getUserId, UserInfo::getId)
                                                                                .eq(UserRole::getRoleId, roleId)
                                                        )
                                                        .leftJoin(UserPrivacy.class, UserPrivacy::getId, UserInfo::getId)
                                                        .isNull(UserRole::getUserId)
                                                        .eq(UserInfo::getIsAvailable, true)
                                                        .like(StringUtils.isNotEmpty(p.getName()), UserInfo::getName, p.getName())
                                                        .like(StringUtils.isNotEmpty(p.getCode()), UserInfo::getCode, p.getCode())
                                                        .orderByDesc(UserInfo::getId)
                                        )
                );
    }

    /**
     * 导出用户信息
     *
     * @param pageQuery
     * @return
     */
    public Workbook downloadData(UserExtendPageQuery pageQuery) {
        pageQuery.setIsBuildIn(false);
        //查询数据
        List<UserManageExtendVo> excelList = this.list(pageQuery);
        //将并添加信息，实体类结构，类的数据做成excel表格对象workbook
        return ExcelExportUtil.exportExcel(new ExportParams(), UserManageExtendVo.class, excelList);
    }

    public List<Role> getUserRoles() {
        return this.userRoleDomainService.getBaseMapper()
                        .selectJoinList(Role.class,
                                MPJWrappers.<UserRole>lambdaJoin()
                                        .selectAll(Role.class)
                                        .leftJoin(Role.class, on -> on.eq(Role::getId, UserRole::getRoleId).eq(Role::getIsEnable, true)
                                        ).eq(UserRole::getUserId, ApplicationSessions.id())
                        );
    }


    public Boolean clearUser() {
        userPrivacyDomainService.deleteUser();
        userInfoDomainService.clearUser();
        return Boolean.TRUE;
    }

}
