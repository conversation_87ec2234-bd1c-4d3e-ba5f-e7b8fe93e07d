package com.hightop.benyin.system.application.handler;

import cn.afterturn.easypoi.excel.entity.result.ExcelVerifyHandlerResult;
import cn.afterturn.easypoi.handler.inter.IExcelVerifyHandler;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.hightop.benyin.system.api.vo.excel.AssetTypeExcel;
import com.hightop.benyin.system.domain.service.AssetTypeDomainService;
import com.hightop.benyin.system.infrastructure.entity.AssetType;
import com.hightop.fario.base.util.StringUtils;
import com.hightop.magina.core.component.ApplicationContexts;

import java.util.Objects;
import java.util.StringJoiner;

public class AssetTypeExcelVerifyHandler implements IExcelVerifyHandler<AssetTypeExcel> {
    @Override
    public ExcelVerifyHandlerResult verifyHandler(AssetTypeExcel data) {
        AssetTypeDomainService locationDomainService = ApplicationContexts.getBean(AssetTypeDomainService.class);

        StringJoiner joiner = new StringJoiner(",");

        if (StringUtils.isNotBlank(data.getParentCode())) {
            AssetType assetType = locationDomainService.lambdaQuery()
                    .eq(AssetType::getCode, data.getParentCode()).one();
            if (Objects.isNull(assetType)) {
//                joiner.add("上级位置编码有误");
            } else {
                data.setParentId(assetType.getId());
            }
        }
        // 校验编码是否存在 允许修改
        AssetType assetType = locationDomainService.lambdaQuery()
                .eq(AssetType::getCode, data.getCode()).one();
        if (Objects.nonNull(assetType)) {
            data.setId(assetType.getId());
        } else {
            data.setId(IdWorker.getId());
        }

        if (StringUtils.isNotBlank(data.getHasTagStr())) {
            if (data.getHasTagStr().trim().equals("是")) {
                data.setHasTag(true);
            } else {
                data.setHasTag(false);
            }
        }
        if (StringUtils.isNotBlank(data.getIsReportStr())) {
            if (data.getIsReportStr().trim().equals("是")) {
                data.setIsReport(true);
            } else {
                data.setIsReport(false);
            }
        }
        if (StringUtils.isNotBlank(data.getIsStatisStr())) {
            if (data.getIsStatisStr().trim().equals("是")) {
                data.setIsStatis(true);
            } else {
                data.setIsStatis(false);
            }
        }
        if (StringUtils.isNotBlank(data.getIsTakeStatisStr())) {
            if (data.getIsTakeStatisStr().trim().equals("是")) {
                data.setIsTakeStatis(true);
            } else {
                data.setIsTakeStatis(false);
            }
        }
        if (StringUtils.isNotBlank(data.getIsTakeStr())) {
            if (data.getIsTakeStr().trim().equals("是")) {
                data.setIsTake(true);
            } else {
                data.setIsTake(false);
            }
        }

        if (StringUtils.isNotBlank(data.getIsScanStr())) {
            if (data.getIsScanStr().trim().equals("是")) {
                data.setIsScan(true);
            } else {
                data.setIsScan(false);
            }
        }
        if (StringUtils.isNotBlank(data.getIsEnableStr())) {
            if (data.getIsEnableStr().trim().equals("是")) {
                data.setIsEnable(true);
            } else {
                data.setIsEnable(false);
            }
        }

        String errorMsg = joiner.toString();
        if (StringUtils.isNotBlank(errorMsg)) {
            return new ExcelVerifyHandlerResult(false, errorMsg);
        }
        return new ExcelVerifyHandlerResult(true);
    }
}
