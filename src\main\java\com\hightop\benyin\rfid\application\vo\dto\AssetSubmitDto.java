package com.hightop.benyin.rfid.application.vo.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.hightop.benyin.rfid.infrastructure.entity.RfidAsset;
import com.hightop.benyin.rfid.infrastructure.enums.AssetBusinessType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @Description: 资产提交DTO
 * @Author: X.S
 * @Date: 2023/12/22 17:16
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@ApiModel("资产提交DTO")
public class AssetSubmitDto {

    @ApiModelProperty("资产信息")
    RfidAsset rfidAsset;

    @ApiModelProperty("申请类型")
    AssetBusinessType operateType;

    @ApiModelProperty("流程编码")
    @NotBlank(message = "登记编号不能为空")
    String code;

    @ApiModelProperty("申请人id")
    Long applyId;

    @ApiModelProperty("申请时间")
    LocalDateTime applyAt;

    @ApiModelProperty("外部单位")
    String externalUnit;


    @ApiModelProperty("外部部门")
    String externalDept;


    @ApiModelProperty("外部人员")
    String externalPerson;

}
