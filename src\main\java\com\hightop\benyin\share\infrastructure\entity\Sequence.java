package com.hightop.benyin.share.infrastructure.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.experimental.FieldDefaults;

/**
 * 序列号实体
 * @Author: X.S
 * @date 2023/10/23 17:29
 */
@FieldDefaults(level = AccessLevel.PRIVATE)
@Data
@Accessors(chain = true)
@TableName("b_sequence")
public class Sequence {
    /**
     * 序号前缀
     */
    @TableId
    String prefix;
    @TableField
    Integer length;
    /**
     * 序号当前值
     */
    @TableField
    Long value;

    public Sequence increase() {
        this.value = this.value + 1;

        return this;
    }
}
