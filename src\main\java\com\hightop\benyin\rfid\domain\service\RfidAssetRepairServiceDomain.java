package com.hightop.benyin.rfid.domain.service;

import com.github.yulichang.base.MPJBaseServiceImpl;
import com.hightop.benyin.rfid.infrastructure.entity.RfidAssetApply;
import com.hightop.benyin.rfid.infrastructure.entity.RfidAssetRepair;
import com.hightop.benyin.rfid.infrastructure.mapper.RfidAssetApplyMapper;
import com.hightop.benyin.rfid.infrastructure.mapper.RfidAssetRepairMapper;
import org.springframework.stereotype.Service;

/**
 *资产维修记录表领域服务
 *
 * <AUTHOR>
 * @date 2024-10-12 16:36:01
 */
@Service
public class RfidAssetRepairServiceDomain extends MPJBaseServiceImpl<RfidAssetRepairMapper, RfidAssetRepair> {
}
