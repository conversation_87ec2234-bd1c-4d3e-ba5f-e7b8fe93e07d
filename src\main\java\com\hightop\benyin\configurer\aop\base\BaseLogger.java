package com.hightop.benyin.configurer.aop.base;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import javax.servlet.http.HttpServletRequest;
import java.util.Enumeration;
/**
 * <AUTHOR>
 */
public class BaseLogger {

    protected Logger logger = LoggerFactory.getLogger(this.getClass());

    protected synchronized void controllerBefore(JoinPoint joinPoint, HttpServletRequest request) {
        try {
            String requestUrl = request.getRequestURL().toString();
            String requestMethod = request.getMethod();
            String declaringTypeName = joinPoint.getSignature().getDeclaringTypeName();
            String signatureName = joinPoint.getSignature().getName();

            Enumeration<String> enu = request.getParameterNames();
            String reqParams = "";
            if (enu.hasMoreElements()) {
                JSONObject g = new JSONObject();
                while (enu.hasMoreElements()) {
                    String paraName = enu.nextElement();
                    g.put(paraName, request.getParameter(paraName));
                }
                reqParams = g.toJSONString();
            } else {
                reqParams = JSONUtil.toJsonStr(joinPoint.getArgs());
            }
            logger.info("=====>请求的地址：{}，请求的http_method：{}，请求的类：{}，执行的方法：{}，请求的参数：{}", requestUrl, requestMethod, declaringTypeName, signatureName, reqParams);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    protected synchronized void controllerAfter(Object ret) {
//        logger.info("请求返回结果：{}", JSON.toJSONString(ret));
    }

    protected synchronized void controllerDoAround(ProceedingJoinPoint pjp) {
        logger.info("reqBody=====>请求的参数：{}", JSON.toJSONString(pjp.getArgs()));
    }

}
