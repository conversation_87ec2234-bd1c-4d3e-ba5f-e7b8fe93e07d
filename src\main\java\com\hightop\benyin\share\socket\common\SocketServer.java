package com.hightop.benyin.share.socket.common;

import com.hightop.benyin.rfid.application.service.RfidReaderService;
import com.hightop.benyin.share.socket.util.SocketUtil;
import com.hightop.benyin.share.socket.util.ThreadPoolUtil;
import com.hightop.magina.core.component.ApplicationContexts;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import javax.annotation.PreDestroy;
import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.ServerSocket;
import java.net.Socket;

/**
 * <AUTHOR> @version 1.0
 * @description:
 * @date 2023/6/19 14:04
 */
@Slf4j
@Data
@Component
public class SocketServer {
    //服务器端口
    @Value("${socket.port}")
    private Integer port;
    //默认最大连接数
    @Value("${socket.maxConnectSize}")
    private int maxConnectSize;
    //是否启动
    @Value("${socket.enable}")
    private Boolean enable;

    //当前连接状态
    protected boolean isConn = false;

    //java ServerSocket 对象
    private ServerSocket serverSocket;


    /**
     * @description: 启动服务
     * <AUTHOR>
     * @date 2023/6/19 16:14
     * @version 1.0
     */
    @EventListener(ApplicationReadyEvent.class)
    public void startSocketServer() {
        if (!enable) {
            log.info("未开启socket服务");
            return;
        }
        new Thread(() -> {
            try {
                if (isConn) {
                    stopSocketServer();
                }
                serverSocket = new ServerSocket();
                //绑定数据连接地址端口号
                serverSocket.bind(new InetSocketAddress(port));
                //绑定成功设置当前服务器状态为true
                isConn = true;
                //循环等待客户端连接
                while (true) {
                    //阻塞 等待socket client 连接
                    Socket socket = serverSocket.accept();
                    socket.setKeepAlive(true);
                    //生成连接通讯对象
                    SocketStation socketStation = new SocketStation(socket);
                    String clientInfo = socket.getInetAddress().getHostAddress() + ":" + socket.getPort();
                    ThreadPoolUtil.run(new Runnable() {
                        public void run() {
                            try {
                                SocketUtil.stationConnect(socketStation, maxConnectSize);
                                socketStation.handle();
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                            SocketUtil.breakStation(clientInfo);
                        }
                    });
                }
            } catch (IOException e) {
                log.error("socket服务异常，端口：{}", port);
                e.printStackTrace();
            }
        }).start();
        log.info("socket服务启动，端口：{}", port);
    }

    /**
     * @description: 停止服务
     * <AUTHOR>
     * @date 2023/6/19 16:14
     * @version 1.0
     */
    @PreDestroy
    public void stopSocketServer() {
        RfidReaderService rfidReaderService = ApplicationContexts.getBean(RfidReaderService.class);
        rfidReaderService.setAllClose();
        if (serverSocket != null) {
            try {
                serverSocket.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
            isConn = false;
            SocketUtil.breakAllStation();
        }
    }

}
