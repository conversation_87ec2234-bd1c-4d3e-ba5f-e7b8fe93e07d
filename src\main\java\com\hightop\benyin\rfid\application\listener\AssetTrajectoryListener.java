package com.hightop.benyin.rfid.application.listener;


import com.hightop.benyin.rfid.application.service.RfidTrajectoryService;
import com.hightop.benyin.rfid.domain.event.AssetTrajectoryEvent;
import com.hightop.fario.base.util.CollectionUtils;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

/**
 * 资产轨迹监听器
 */
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Service
@Slf4j
public class AssetTrajectoryListener {

    RfidTrajectoryService rfidAssetTrajectoryService;

    @EventListener
    public void onCompleted(AssetTrajectoryEvent event) {
        if (CollectionUtils.isEmpty(event.getAssetTrajectoryDtos())) {
            log.error("资产轨迹上报 数量为0！");
            return;
        }
        log.info("资产轨迹上报 数量: {}！", event.getAssetTrajectoryDtos().size());
        boolean success = rfidAssetTrajectoryService.saveTrajectorys(event.getAssetTrajectoryDtos());
        if (success) {
            log.info("资产轨迹上报成功！", event.getAssetTrajectoryDtos().size());
        }
    }
}
