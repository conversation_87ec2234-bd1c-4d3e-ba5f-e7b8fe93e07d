package com.hightop.benyin.share.application.vo;

import com.hightop.magina.standard.ums.user.manage.UserManageVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldDefaults;

@Data
@EqualsAndHashCode(callSuper = true)
@FieldDefaults(level = AccessLevel.PRIVATE)
public class UserExtendsVo extends UserManageVo {

    @ApiModelProperty("所属单位id")
    String departmentId;

    @ApiModelProperty("所属单位名称 ")
    String departmentName;

    @ApiModelProperty("基站编码 ")
    String readerCode;

    @ApiModelProperty("位置 ")
    String location;

    @ApiModelProperty("位置编码 ")
    String locationCode;

}
