package com.hightop.benyin.system.api.controller;

import com.hightop.benyin.share.infrastructure.util.DownloadResponseUtil;
import com.hightop.benyin.system.api.vo.DepartmentInfoTreeVo;
import com.hightop.benyin.system.api.vo.dto.DepartmentInfoAddDto;
import com.hightop.benyin.system.api.vo.dto.DepartmentInfoUpdateDto;
import com.hightop.benyin.system.api.vo.query.DepartmentQuery;
import com.hightop.benyin.system.application.service.DepartmentExtendService;
import com.hightop.fario.base.web.Operation;
import com.hightop.fario.base.web.RestResponse;
import com.hightop.magina.standard.behavior.operation.IgnoreOperationLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

@RequestMapping("/dept-extends")
@RestController
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Api(tags = "部门管理-扩展功能")
public class DepartmentExtendController {

    DepartmentExtendService departmentExtendService;

    @PostMapping
    @ApiOperation("部门树查询")
    @IgnoreOperationLog
    public List<DepartmentInfoTreeVo> tree(@RequestBody DepartmentQuery query) {
        return this.departmentExtendService.getDepartmentTree(query);
    }

    @PostMapping("/auth")
    @ApiOperation("带权限的部门树")
    @IgnoreOperationLog
    public List<DepartmentInfoTreeVo> authTree(@RequestBody DepartmentQuery query) {
        return this.departmentExtendService.getAuthDepartmentTree(query);
    }

    @PostMapping("/searchTree")
    @ApiOperation("带权限的部门树查询用")
    @IgnoreOperationLog
    public List<DepartmentInfoTreeVo> searchTree(@RequestBody DepartmentQuery query) {
        return this.departmentExtendService.getAuthDepartmentTree(query);
    }

    @PostMapping("/add")
    @ApiOperation("部门添加")
    public RestResponse<Void> add(@Validated @RequestBody DepartmentInfoAddDto department) {
        return Operation.ADD.response(this.departmentExtendService.save(department.toDepartment()));
    }

    @PutMapping
    @ApiOperation("部门修改")
    public RestResponse<Void> update(@Validated @RequestBody DepartmentInfoUpdateDto department) {
        return Operation.UPDATE.response(this.departmentExtendService.updateById(department.toDepartment()));
    }

    @DeleteMapping("/{id}")
    @ApiOperation("部门删除")
    public RestResponse<Void> delete(@ApiParam("部门id") @PathVariable Long id) {
        return Operation.DELETE.response(this.departmentExtendService.removeById(id));
    }

    @PutMapping("/enable/{id}/{enable}")
    @ApiOperation("部门启停")
    public RestResponse<Void> enable(@ApiParam(value = "部门id", required = true) @PathVariable Long id,
                                     @ApiParam(value = "启停标识", required = true) @PathVariable Boolean enable) {
        return Operation.UPDATE.response(this.departmentExtendService.updateEnable(id, enable));
    }

    @DeleteMapping("clear")
    @ApiOperation("清理部门数据")
    public RestResponse<Void> delete() {
        return Operation.UPDATE.response(this.departmentExtendService.clearDepartment());
    }

    /**
     * 导入部门
     **/
    @PostMapping("/import")
    @ApiOperation(value = "导入部门数据")
    public RestResponse<Void> impor(@RequestParam("file") MultipartFile file) throws Exception {
        boolean b = this.departmentExtendService.importData(file);
        if (!b) {
            return new RestResponse(500, "解析失败", null, null);
        }
        return RestResponse.message("导入成功");
    }

    /**
     * 下载位置模板
     *
     * @param response
     * @return
     */
    @ApiOperation("下载导入部门模板")
    @GetMapping("/downTemplate")
    public RestResponse<Void> downTemplate(HttpServletResponse response) {
        Boolean b = departmentExtendService.downTemplate(response);
        if (!b) {
            return new RestResponse(500, "下载失败", null, null);
        }
        return RestResponse.message("下载成功");
    }

    /**
     * 导出当前资产数据
     *
     * @param response
     * @param pageQuery
     * @return
     */
    @ApiOperation("/导出部门信息")
    @PostMapping("/export")
    @IgnoreOperationLog
    public RestResponse<Void> downOrderData(HttpServletResponse response, @RequestBody DepartmentQuery pageQuery) {
        try {
            //页面下载设置
            Workbook workbook = departmentExtendService.downloadData(pageQuery);
            DownloadResponseUtil.addDownLoadHeader(response, "部门信息.xlsx");
            workbook.write(response.getOutputStream());
        } catch (IOException e) {
            e.printStackTrace();
            return new RestResponse(500, "导出失败", null, null);
        }
        return RestResponse.message("导出成功");
    }


}
