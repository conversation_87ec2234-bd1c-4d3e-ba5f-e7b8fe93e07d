package com.hightop.benyin.system.domain.service;

import com.github.yulichang.base.MPJBaseServiceImpl;
import com.hightop.benyin.system.infrastructure.entity.DataLog;
import com.hightop.benyin.system.infrastructure.mapper.DataLogMapper;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.stereotype.Service;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Service
public class DataLogDomainService extends MPJBaseServiceImpl<DataLogMapper, DataLog> {

}
