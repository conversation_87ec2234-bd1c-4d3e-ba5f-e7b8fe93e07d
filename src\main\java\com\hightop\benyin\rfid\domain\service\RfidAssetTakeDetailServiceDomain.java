package com.hightop.benyin.rfid.domain.service;

import com.github.yulichang.base.MPJBaseServiceImpl;
import com.hightop.benyin.rfid.infrastructure.entity.RfidAssetTakeDetail;
import com.hightop.benyin.rfid.infrastructure.mapper.RfidAssetTakeDetailMapper;
import org.springframework.stereotype.Service;

/**
 * 资产盘点明细领域服务
 *
 * <AUTHOR>
 * @date 2024-10-12 16:36:01
 */
@Service
public class RfidAssetTakeDetailServiceDomain extends MPJBaseServiceImpl<RfidAssetTakeDetailMapper, RfidAssetTakeDetail> {

    public void clear() {
        baseMapper.clear();
    }
}
