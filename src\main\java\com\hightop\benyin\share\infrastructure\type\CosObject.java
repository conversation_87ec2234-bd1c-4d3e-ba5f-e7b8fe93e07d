package com.hightop.benyin.share.infrastructure.type;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;

/**
 * cos存储对象
 * @Author: X.S
 * @date 2023/10/26 11:03
 */
@FieldDefaults(level = AccessLevel.PRIVATE)
@Data
public class CosObject implements Serializable {
    /**
     * 存储key
     */
    String key;
    /**
     * 对象名称(源文件名称)
     */
    String name;
    /**
     * 对象访问地址
     */
    String url;
}
