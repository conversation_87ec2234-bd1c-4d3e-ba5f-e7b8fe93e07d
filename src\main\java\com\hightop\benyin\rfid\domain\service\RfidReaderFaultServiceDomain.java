package com.hightop.benyin.rfid.domain.service;

import com.github.yulichang.base.MPJBaseServiceImpl;
import com.hightop.benyin.rfid.infrastructure.entity.RfidReaderFault;
import com.hightop.benyin.rfid.infrastructure.mapper.RfidReaderFaultMapper;
import org.springframework.stereotype.Service;

/**
 * rfid基站故障管理领域服务
 * <AUTHOR>
 * @date 2024-10-12 16:36:01
 */
@Service
public class RfidReaderFaultServiceDomain extends MPJBaseServiceImpl<RfidReaderFaultMapper, RfidReaderFault> {
}
