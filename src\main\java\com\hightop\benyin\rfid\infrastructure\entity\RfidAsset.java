package com.hightop.benyin.rfid.infrastructure.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelIgnore;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.hightop.benyin.configurer.annotation.RecordLog;
import com.hightop.benyin.configurer.annotation.RecordLogField;
import com.hightop.benyin.configurer.enums.DataType;
import com.hightop.benyin.rfid.infrastructure.enums.AssetApplyStatus;
import com.hightop.benyin.rfid.infrastructure.enums.AssetBusinessType;
import com.hightop.benyin.share.infrastructure.util.DictUtil;
import com.hightop.benyin.system.infrastructure.enums.BusinessType;
import com.hightop.fario.common.jackson.annotation.JsonAmount;
import com.hightop.magina.standard.code.dictionary.bind.DictItemBind;
import com.hightop.magina.standard.code.dictionary.bind.DictItemEntry;
import com.hightop.magina.standard.ums.user.bind.UserBind;
import com.hightop.magina.standard.ums.user.bind.UserEntry;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * rfid资产信息
 *
 * <AUTHOR>
 * @date 2024-10-12 16:36:01
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@TableName(value = "b_rfid_asset", autoResultMap = true)
@ApiModel
@RecordLog(businessType = BusinessType.ASSET)
public class RfidAsset {


    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @ApiModelProperty("id")
    @ExcelIgnore
    Long id;

    @TableField(value = "rfid_code",updateStrategy =FieldStrategy.IGNORED )
    @ApiModelProperty(value = "当前rfid编码", required = true)
    @Excel(name = "当前rfid编码", width = 30, orderNum = "0")
    @RecordLogField(value = "RFID标签")
    String rfidCode;


    @TableField("ori_rfid_code")
    @ApiModelProperty("原rfid编码(选填)")
    @Excel(name = "原rfid编码", width = 30, orderNum = "1")
    String oriRfidCode;


    @TableField("code")
    @Excel(name = "资产编码", width = 30, orderNum = "2")
    @NotBlank(message = "资产编码不能为空")
    @ApiModelProperty(value = "资产编码(填)", required = true)
    @RecordLogField(value = "资产编码")
    String code;

    @TableField("name")
    @Excel(name = "资产名称", width = 30, orderNum = "3")
    @NotBlank(message = "资产名称不能为空")
    @ApiModelProperty(value = "资产名称(填)", required = true)
    @RecordLogField(value = "资产名称")
    String name;

    @TableField("model")
    @ApiModelProperty("规格型号(选填)")
    @Excel(name = "规格型号", width = 30, orderNum = "4")
    @RecordLogField(value = "规格型号")
    String model;

    @TableField("parent_code")
    @ApiModelProperty("规格上级资产编码型号(选填)")
    @Excel(name = "上级资产编码", width = 30, orderNum = "5")
    String parentCode;

    @TableField(exist = false)
    @Excel(name = "资产分类编码", width = 30, orderNum = "6")
    String asseTypeCode;

    @TableField(exist = false)
    @Excel(name = "资产门类", width = 30, orderNum = "7")
    String categoryType;

    @TableField(exist = false)
    @Excel(name = "资产大类", width = 30, orderNum = "7")
    String assetParentTypeName;

    @TableField(exist = false)
    @Excel(name = "资产小类", width = 30, orderNum = "8")
    String assetMiddleTypeName;

    @TableField(exist = false)
    @Excel(name = "分类名称", width = 30, orderNum = "9")
    String assetTypeName;

    @TableField("sign_code")
    @ApiModelProperty(value = "登记单号", required = true)
    @ExcelIgnore
    String signCode;


    @TableField("asset_type")
    @ApiModelProperty("资产类型")
    @ExcelIgnore
    @RecordLogField(value = "资产类型", dataType = DataType.ASSET_TYPE)
    String assetType;

    @TableField("asset_middle_type")
    @ApiModelProperty("资产小类")
    @ExcelIgnore
    String assetMiddleType;

    @TableField("asset_parent_type")
    @ApiModelProperty("资产大类")
    @ExcelIgnore
    String assetParentType;

    @TableField(DictUtil.DICT_ASSET_PURPOSE)
    @ApiModelProperty("资产用途")
    @DictItemBind(DictUtil.DICT_ASSET_PURPOSE)
    @ExcelIgnore
    @RecordLogField(value = "资产用途", dataType = DataType.DICT, dictCode = DictUtil.DICT_ASSET_PURPOSE)
    DictItemEntry assetPurpose;

    @ApiModelProperty("资产用途")
    @TableField(exist = false)
    @Excel(name = "资产用途", width = 30, orderNum = "10")
    String assetPurposeStr;

    @ApiModelProperty("使用状态")
    @TableField(exist = false)
    @Excel(name = "使用状态", width = 30, orderNum = "11")
    String useStateStr;


    @TableField(DictUtil.DICT_CARD_TYPE)
    @ApiModelProperty("卡片类型")
    @DictItemBind(DictUtil.DICT_CARD_TYPE)
    @ExcelIgnore
    DictItemEntry cardType;

    @TableField(exist = false)
    @ApiModelProperty(value = "卡片类型", required = true)
    @Excel(name = "卡片类型", width = 30, orderNum = "13")
    String cardTypeName;


    @TableField("cheer_code")
    @ApiModelProperty("久其编码(选填)")
    @Excel(name = "久其编码", width = 30, orderNum = "14")
    String cheerCode;

    @TableField(DictUtil.DICT_ACQUIRE_MODE)
    @ApiModelProperty("取得方式")
    @DictItemBind(DictUtil.DICT_ACQUIRE_MODE)
    @ExcelIgnore
    @RecordLogField(value = "使用状态", dataType = DataType.DICT, dictCode = DictUtil.DICT_ACQUIRE_MODE)
    DictItemEntry acquireMode;

    @TableField(exist = false)
    @ApiModelProperty("取得方式")
    @Excel(name = "取得方式", width = 30, orderNum = "15")
    String acquireModeName;


    @TableField("acquire_date")
    @ApiModelProperty("取得日期")
    @Excel(name = "取得日期(yyyy-MM-dd)", width = 30, orderNum = "16")
    String acquireDate;


    @TableField("enter_date")
    @ApiModelProperty("入账日期")
    @Excel(name = "入账日期(yyyy-MM-dd)", width = 30, orderNum = "17")
    String enterDate;

    @TableField(exist = false)
    @ApiModelProperty("基站编码")
    @Excel(name = "基站编码", width = 30, orderNum = "18")
    String readerCode;

    @TableField(exist = false)
    @ApiModelProperty("基站编码")
    @Excel(name = "基站设备ID", width = 30, orderNum = "19")
    String readerDeviceId;

    @TableField(exist = false)
    @ApiModelProperty("位置编码")
    @Excel(name = "位置编码", width = 60, orderNum = "20")
    String locationCode;

    @TableField(exist = false)
    @ApiModelProperty("位置名称")
    @Excel(name = "位置名称", width = 60, orderNum = "21")
    String location;

    @TableField("storage_location")
    @ApiModelProperty("存放位置(选填)")
    @Excel(name = "存放位置", width = 60, orderNum = "22")
    String storageLocation;

    @TableField(exist = false)
    @ApiModelProperty("财务归口")
    @Excel(name = "财务归口", width = 30, orderNum = "22")
    String financialClassifys;


    @TableField("data_source")
    @ApiModelProperty("资产来源2报溢入库1期初导入0业务新增")
    @Excel(name = "数据来源", width = 30, orderNum = "23", replace = {"期初导入_1", "业务新增_0"})
    Integer dataSource;


    @TableField("has_tag")
    @ApiModelProperty("是否贴标签")
    @Excel(name = "是否贴标签", width = 30, orderNum = "24", replace = {"是_true", "否_false"})
    Boolean hasTag;

    @TableField("is_scan")
    @ApiModelProperty("是否扫描")
    @Excel(name = "是否扫描", width = 30, orderNum = "25", replace = {"是_true", "否_false"})
    Boolean isScan;

    @TableField("is_report")
    @ApiModelProperty("是否上报异常")
    @Excel(name = "是否上报异常", width = 30, orderNum = "26", replace = {"是_true", "否_false"})
    Boolean isReport;

    @TableField("is_take")
    @ApiModelProperty("是否盘点扫描")
    @Excel(name = "是否盘点", width = 30, orderNum = "27", replace = {"是_true", "否_false"})
    Boolean isTake;

    @TableField("is_flow")
    @ApiModelProperty("是否流动(流动、固定)")
    @ExcelIgnore
    Boolean isFlow;

    @TableField("is_take_statis")
    @ApiModelProperty("是否盘点统计")
    @Excel(name = "是否盘点统计", width = 30, orderNum = "28", replace = {"是_true", "否_false"})
    Boolean isTakeStatis;

    @TableField("is_statis")
    @ApiModelProperty("是否财务统计")
    @Excel(name = "是否财务统计", width = 30, orderNum = "29", replace = {"是_true", "否_false"})
    Boolean isStatis;


    @TableField("price")
    @JsonAmount
    @ApiModelProperty("价格(选填)")
    @ExcelIgnore
    Long price;

    @TableField(exist = false)
    @ApiModelProperty("采购价格(选填)")
    @Excel(name = "采购价格", width = 30, orderNum = "30")
    BigDecimal priceDecimal;

    @TableField(exist = false)
    @ApiModelProperty("初始残值 ")
    @Excel(name = "初始残值", width = 30, orderNum = "31")
    BigDecimal initSalvageDecimal;

    @TableField(exist = false)
    @ApiModelProperty("当前残值")
    @Excel(name = "当前残值", width = 30, orderNum = "32")
    BigDecimal nowSalvageDecimal;

    @TableField(value = "breakage_at")
    @ApiModelProperty("报废时间")
    @Excel(name = "报废时间", width = 30, orderNum = "33", format = "yyyy/MM/dd")
    LocalDate breakageAt;


    @TableField("manager_dept_id")
    @ApiModelProperty(value = "责任部门", required = true)
    @ExcelIgnore
    @RecordLogField(value = "责任部门", dataType = DataType.DEPT)
    Long managerDeptId;

    @TableField(exist = false)
    @Excel(name = "责任部门编码", width = 30, orderNum = "34")
    @ApiModelProperty("责任部门编码")
    String managerDeptCode;

    @TableField(exist = false)
    @Excel(name = "责任部门", width = 30, orderNum = "35")
    @ApiModelProperty("责任部门")
    String managerDeptName;

    @TableField("manager_id")
    @ApiModelProperty(value = "责任人", required = true)
    @ExcelIgnore
    @RecordLogField(value = "责任人", dataType = DataType.USER)
    Long managerId;

    @TableField(exist = false)
    @Excel(name = "责任人账号", width = 30, orderNum = "36")
    @ApiModelProperty("责任人账号")
    String managerCode;

    @TableField("manager_name")
    @ApiModelProperty(value = "责任人姓名", required = true)
    @Excel(name = "责任人姓名", width = 30, orderNum = "37")
    String managerName;


    @TableField("type")
    @ApiModelProperty("资产类型")
    @ExcelIgnore
    Integer type;

    @TableField(value = "reader_id", updateStrategy = FieldStrategy.IGNORED)
    @ApiModelProperty("基站编码")
    @RecordLogField(value = "绑定基站", dataType = DataType.READER)
    Long readerId;


    @TableField(value = "department_id", updateStrategy = FieldStrategy.IGNORED)
    @ApiModelProperty(value = "领用部门", required = true)
    @ExcelIgnore
    @RecordLogField(value = "保管部门", dataType = DataType.DEPT)
    Long departmentId;

    @TableField(exist = false)
    @Excel(name = "当前保管部门编码", width = 30, orderNum = "38")
    @ApiModelProperty("当前保管部门编码")
    String departmentCode;

    @TableField(exist = false)
    @Excel(name = "当前保管部门名称", width = 30, orderNum = "39")
    @ApiModelProperty("当前保管部门名称")
    String departmentName;

    @TableField(exist = false)
    @ApiModelProperty("当前保管人账号")
    @Excel(name = "当前保管人账号", width = 30, orderNum = "40")
    String applyCode;


    @TableField(value = "apply_name", updateStrategy = FieldStrategy.IGNORED)
    @ApiModelProperty("当前保管人姓名")
    @Excel(name = "当前保管人姓名", width = 30, orderNum = "41")
    String applyName;

    @TableField(value = "apply_at", updateStrategy = FieldStrategy.IGNORED)
    @ApiModelProperty("领用时间(选填)")
    @Excel(name = "领用时间(yyyy-MM-dd)", width = 30, orderNum = "42", format = "yyyy-MM-dd HH:mm:ss")
    LocalDateTime applyAt;

    @TableField(value = "location_id", updateStrategy = FieldStrategy.IGNORED)
    @ApiModelProperty("所在位置")
    @RecordLogField(value = "所在位置", dataType = DataType.LOCATION)
    Long locationId;


    @TableField(value = "apply_id", updateStrategy = FieldStrategy.IGNORED)
    @ApiModelProperty(value = "归属人id", required = true)
    @RecordLogField(value = "保管人", dataType = DataType.USER)
    @ExcelIgnore
    Long applyId;

    @TableField("use_state")
    @ApiModelProperty("使用状态")
    @DictItemBind(DictUtil.DICT_USE_STATE)
    @ExcelIgnore
    @RecordLogField(value = "使用状态", dataType = DataType.DICT, dictCode = DictUtil.DICT_USE_STATE)
    DictItemEntry useState;

    @TableField("status")
    @ApiModelProperty("状态")
    AssetApplyStatus status;

    @TableField(exist = false)
    @ApiModelProperty("状态")
//    @Excel(name = "状态", width = 30, orderNum = "19")
    String statusName;


    @TableField("init_salvage")
    @ApiModelProperty("初始残值 ")
    @ExcelIgnore
    @JsonAmount
    Long initSalvage;

    @TableField("now_salvage")
    @ApiModelProperty("现残值 ")
    @JsonAmount
    @ExcelIgnore
    Long nowSalvage;


    @TableField(value = "financial_classify", typeHandler = JacksonTypeHandler.class)
    @ApiModelProperty("财务归口")
    @ExcelIgnore
    List<Integer> financialClassify;


    @TableField("sync_state")
    @ApiModelProperty("同步状态")
    @ExcelIgnore
    Boolean syncState;

    @TableField("in_status")
    @ApiModelProperty("入库状态")
    @ExcelIgnore
    Boolean inStatus;


    @TableField(value = "curr_location_id", updateStrategy = FieldStrategy.IGNORED)
    @ApiModelProperty("当前所在位置")
    Long currLocationId;

    @TableField(exist = false)
    @ApiModelProperty("当前所在位置")
    String currLocation;


    @TableField(value = "curr_reader_id", updateStrategy = FieldStrategy.IGNORED)
    @ApiModelProperty("当前所在基站")
    Long currReaderId;

    @TableField(exist = false)
    @ApiModelProperty("登记人部门编码")
    @Excel(name = "登记人部门编码", width = 30, orderNum = "43")
    String createDeptCode;

    @TableField(exist = false)
    @ApiModelProperty("登记人部门名称")
    @Excel(name = "登记人部门名称", width = 30, orderNum = "44")
    String createDeptName;


    @TableField("created_by")
    @ApiModelProperty("创建人")
    @UserBind
    @ExcelIgnore
    UserEntry createdBy;


    @TableField(value = "create_dept")
    @ApiModelProperty("创建人部门")
    @ExcelIgnore
    Long createDept;

    @TableField(exist = false)
    @ApiModelProperty("登记人账号")
    @Excel(name = "登记人账号", width = 30, orderNum = "45")
    String createdByCode;


    @TableField(exist = false)
    @ApiModelProperty("登记人姓名")
    @Excel(name = "登记人姓名", width = 30, orderNum = "46")
    String createdByName;


    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @ApiModelProperty("创建时间")
    @Excel(name = "登记时间(yyyy-MM-dd)", width = 30, orderNum = "47", format = "yyyy/MM/dd HH:mm:ss")
    LocalDateTime createdAt;


    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty("更新时间")
    @ExcelIgnore
    LocalDateTime updatedAt;

    @TableField(value = "updated_by")
    @ApiModelProperty("更新人")
    @ExcelIgnore
    Long updatedBy;

    @TableField("deleted")
    @ApiModelProperty("是否删除  0未删除  1删除")
    @TableLogic
    @ExcelIgnore
    Integer deleted;

    @TableField(exist = false)
    @ApiModelProperty("下级资产")
    @ExcelIgnore
    List<RfidAsset> children;

    @TableField(exist = false)
    @ApiModelProperty("下级资产id")
    @ExcelIgnore
    List<Long> childrenIds;


    @TableField(exist = false)
    @ApiModelProperty("资产id")
    @ExcelIgnore
    Long assetId;


    @TableField(exist = false)
    @ApiModelProperty("基站")
    @ExcelIgnore
    String reader;

    @TableField(exist = false)
    @ApiModelProperty("申请类型")
    @ExcelIgnore
    AssetBusinessType operateType;

    @TableField(exist = false)
    @ApiModelProperty("外部单位")
    String externalUnit;

    @TableField(exist = false)
    @ApiModelProperty("外部部门")
    String externalDept;

    @TableField(exist = false)
    @ApiModelProperty("外部人员")
    String externalPerson;

    public String getCardTypeName() {
        return cardType != null ? cardType.getLabel() : null;
    }

    public String getUseStateName() {
        return useState != null ? useState.getLabel() : null;
    }

    public String getAcquireModeName() {
        return acquireMode != null ? acquireMode.getLabel() : null;
    }

    public String getAssetPurposeStr() {
        return assetPurpose != null ? assetPurpose.getLabel() : null;
    }

    public String getCardTypeStr() {
        return cardType != null ? cardType.getLabel() : null;
    }

    public String getUseStateStr() {
        return useState != null ? useState.getLabel() : null;
    }

    public String getCreatedByName() {
        return createdBy.getName();
    }

    public BigDecimal getPriceDecimal() {
        return price != null ? new BigDecimal(price).divide(new BigDecimal(100)).setScale(2, RoundingMode.FLOOR) : null;
    }

    public BigDecimal getInitSalvageDecimal() {
        return initSalvage != null ? new BigDecimal(initSalvage).divide(new BigDecimal(100)).setScale(2, RoundingMode.FLOOR) : null;
    }

    public BigDecimal getNowSalvageDecimal() {
        return nowSalvage != null ? new BigDecimal(nowSalvage).divide(new BigDecimal(100)).setScale(2, RoundingMode.FLOOR) : null;
    }
}
