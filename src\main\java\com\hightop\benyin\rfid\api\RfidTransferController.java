package com.hightop.benyin.rfid.api;

import com.hightop.benyin.rfid.application.service.RfidAssetTransferService;
import com.hightop.benyin.rfid.application.vo.dto.AssetFlowAuditDto;
import com.hightop.benyin.rfid.application.vo.dto.AssetTransferAuditDto;
import com.hightop.benyin.rfid.application.vo.dto.RfidChangerDto;
import com.hightop.benyin.rfid.application.vo.dto.RfidTransferDto;
import com.hightop.benyin.rfid.application.vo.query.RfidAssetTransferDetailQuery;
import com.hightop.benyin.rfid.application.vo.query.RfidAssetTransferQuery;
import com.hightop.benyin.rfid.infrastructure.entity.RfidAssetTransfer;
import com.hightop.benyin.rfid.infrastructure.entity.RfidAssetTransferDetail;
import com.hightop.fario.base.web.Operation;
import com.hightop.fario.base.web.RestResponse;
import com.hightop.fario.common.mybatis.DataGrid;
import com.hightop.magina.standard.behavior.operation.IgnoreOperationLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * Rfid变更数据管理rest接口
 *
 * <AUTHOR>
 * @date 2024-10-12 16:36:01
 */
@RequestMapping("/transfer")
@RestController
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Api(tags = "Rfid变更数据管理")
public class RfidTransferController {
    RfidAssetTransferService rfidAssetTransferService;


    @PostMapping("/page")
    @IgnoreOperationLog
    @ApiOperation("分页查询")
    public RestResponse<DataGrid<RfidAssetTransfer>> page(@RequestBody RfidAssetTransferQuery pageQuery) {
        return RestResponse.ok(this.rfidAssetTransferService.page(pageQuery));
    }

    @GetMapping("/{transferCode}")
    @ApiOperation("查看明细")
    public RestResponse<RfidAssetTransfer> detail(@PathVariable @ApiParam("transferCode") String transferCode) {
        return RestResponse.ok(this.rfidAssetTransferService.getByCode(transferCode));
    }

    @DeleteMapping("/{id}")
    @ApiOperation("删除")
    public RestResponse<Void> delete(@PathVariable @ApiParam("id") Long id) {
        return Operation.DELETE.response(this.rfidAssetTransferService.remove(id));
    }

    @PostMapping
    @ApiOperation("添加资产变更")
    public RestResponse<Void> saveTransfer(@Validated @RequestBody RfidTransferDto rfidTransferDto) {
        return Operation.ADD.response(this.rfidAssetTransferService.saveTransfer(rfidTransferDto));
    }

    @PostMapping("/saveChange")
    @ApiOperation("添加位置变更")
    public RestResponse<Void> saveChange(@Validated @RequestBody RfidChangerDto rfidTransferDto) {
        return Operation.ADD.response(this.rfidAssetTransferService.saveChange(rfidTransferDto));
    }

    @PostMapping("approve")
    @ApiOperation("审核资产变更")
    public RestResponse<Void> approve(@Validated @RequestBody AssetTransferAuditDto assetTransferAuditDto) {
        return Operation.ADD.response(this.rfidAssetTransferService.approve(assetTransferAuditDto));
    }



    @PostMapping("/detailPage")
    @IgnoreOperationLog
    @ApiOperation("变更明细分页查询")
    public RestResponse<DataGrid<RfidAssetTransferDetail>> detailPage(@RequestBody RfidAssetTransferDetailQuery pageQuery) {
        return RestResponse.ok(this.rfidAssetTransferService.detailPage(pageQuery));
    }

}
