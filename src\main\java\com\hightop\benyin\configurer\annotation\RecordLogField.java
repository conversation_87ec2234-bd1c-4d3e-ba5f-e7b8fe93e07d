package com.hightop.benyin.configurer.annotation;

import com.fasterxml.jackson.annotation.JacksonAnnotationsInside;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.hightop.benyin.configurer.enums.DataType;
import com.hightop.fario.common.jackson.deserializer.AmountDeserializer;
import com.hightop.fario.common.jackson.serializer.AmountSerializer;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target({ElementType.FIELD, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@JacksonAnnotationsInside
@JsonDeserialize(using = AmountDeserializer.class)
@JsonSerialize(using = AmountSerializer.class)
public @interface RecordLogField {
    /**
     * 字段描述
     * @return
     */
    String value() ;
    /**
     * 数据字典编码
     * @return 默认为2位
     */
    String dictCode() default "" ;

    /**
     * 字段类型
     * @return 默认为2位
     */
    DataType dataType() default DataType.TXT;
}
