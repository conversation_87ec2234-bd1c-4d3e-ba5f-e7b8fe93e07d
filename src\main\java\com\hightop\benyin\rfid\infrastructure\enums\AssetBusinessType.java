package com.hightop.benyin.rfid.infrastructure.enums;

import com.hightop.magina.core.custom.entry.EnumEntry;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

/**
 * 业务操作类型
 *
 * @Author: X.S
 * @Date: 2023/12/19 11:43
 */
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@Getter
@RequiredArgsConstructor
public enum AssetBusinessType implements EnumEntry<String> {

    PURCHASE("采购入库"),
    BIND("标签绑定"),
    APPLY("领用出库"),
    RETURN("退还入库"),
    ALLOT_OUT("调拨出库"),
    ALLOT_IN("调拨入库"),
    BREAKAGE("报损出库"),
    OVERFLOW("报溢入库"),
    REPAIR("维修登记"),
    DONATE_IN("捐赠入库"),
    DONATE_OUT("捐赠出库"),
    DISPOSE("处置出库"),
    BREAKDOWN("报废出库"),
    HANDOVER("人员交接"),
    ;
    /**
     * 状态名称
     */
    String name;

    @Override
    public String getCode() {
        // 以枚举名称作为标识
        return super.name();
    }

    @Override
    public String getName() {
        return this.name;
    }
}
