package com.hightop.benyin.share.domain.service;

import com.github.yulichang.base.MPJBaseServiceImpl;
import com.hightop.benyin.rfid.infrastructure.entity.RfidReader;
import com.hightop.benyin.rfid.infrastructure.mapper.RfidReaderMapper;
import com.hightop.benyin.share.infrastructure.entity.ApiLog;
import com.hightop.benyin.share.infrastructure.mapper.ApiLogMapper;
import org.springframework.stereotype.Service;

/**
 * 日志领域服务
 * <AUTHOR>
 * @date 2024-10-12 16:36:01
 */
@Service
public class ApiLogServiceDomain extends MPJBaseServiceImpl<ApiLogMapper, ApiLog> {
}
