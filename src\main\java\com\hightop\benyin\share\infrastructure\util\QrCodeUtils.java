package com.hightop.benyin.share.infrastructure.util;


import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.MultiFormatWriter;
import com.google.zxing.WriterException;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;
import com.hightop.fario.base.util.StringUtils;
import com.hightop.fario.base.util.codec.Base64Codec;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import sun.font.FontDesignMetrics;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.font.FontRenderContext;
import java.awt.font.LineMetrics;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 二维码工具类
 * <AUTHOR>
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
@Slf4j
public class QrCodeUtils {
    /**
     * 黑白色
     */
    private static final int BLACK = 0xFF000000, WHITE = 0xFFFFFFFF;
    /**
     * 二维码大小
     */
    private static final int SIZE = 480;


    /**
     * 创建二维码
     * @param data        json数据
     * @param inputStream logo文件
     * @param text        底部文字
     * @return base二维码
     * <AUTHOR>
     */
    public static String createQrCode(String data, InputStream inputStream, String text)
        throws IOException, WriterException {
        Map<EncodeHintType, Object> hints = new HashMap<>(100);
        hints.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.H);
        hints.put(EncodeHintType.CHARACTER_SET, StandardCharsets.UTF_8.name());
        if (Objects.nonNull(inputStream)) {
            hints.put(EncodeHintType.MARGIN, 1);
        }
        // 添加文字时高度增加
        int offset = StringUtils.isNotBlank(text) ? 30 : 0;
        BitMatrix matrix = new MultiFormatWriter().encode(data, BarcodeFormat.QR_CODE, SIZE, SIZE + offset, hints);
        BufferedImage image = new BufferedImage(matrix.getWidth(), matrix.getHeight(), BufferedImage.TYPE_INT_RGB);
        for (int x = 0; x < SIZE; x++) {
            for (int y = 0; y < SIZE; y++) {
                image.setRGB(x, y, matrix.get(x, y) ? BLACK : WHITE);
            }
        }

        if (Objects.nonNull(inputStream)) {
            // 插入图片
            try {
                QrCodeUtils.insertImage(image, inputStream);
            } catch (Exception e) {
                // 若图片插入失败 不影响二维码生成
                log.warn(e.getMessage(), e);
            }
        }

        if (StringUtils.isNotBlank(text)) {
            // 插入底部文字
            try {
                insertFont(image, text);
            } catch (Exception e) {
                // 若文字生成失败 不影响二维码生成
                log.warn(e.getMessage(), e);
            }
        }

        return bufferedImageToBase64(image);
    }


    /**
     * 转Base64
     * @param bufferedImage {@link BufferedImage}
     * @return base64
     */
    private static String bufferedImageToBase64(BufferedImage bufferedImage) throws IOException {
        //io流
        try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
            //写入流中
            ImageIO.write(bufferedImage, "png", baos);

            return "data:image/png;base64," + Base64Codec.encode(baos.toByteArray());
        }
    }

    /**
     * 插入LOGO
     * @param image       二维码图片
     * @param inputStream 图片输入流
     * @throws IOException IOException
     */
    private static void insertImage(BufferedImage image, InputStream inputStream) throws IOException {
        Graphics2D g = image.createGraphics();
        BufferedImage logo = ImageIO.read(inputStream);

        int widthLogo = Math.min(logo.getWidth(null), image.getWidth() * 2 / 10),
            heightLogo = Math.min(logo.getHeight(null), image.getHeight() * 2 / 10),
            x = (image.getWidth() - widthLogo) / 2,
            y = (image.getHeight() - heightLogo) / 2;

        // 开始绘制图片
        g.drawImage(logo, x, y, widthLogo, heightLogo, null);
        g.drawRoundRect(x, y, widthLogo, heightLogo, 15, 15);
        //边框宽度
        g.setStroke(new BasicStroke(2));
        //边框颜色
        g.setColor(Color.WHITE);
        g.drawRect(x, y, widthLogo, heightLogo);
        g.dispose();

        logo.flush();
        image.flush();
    }

    /**
     * 添加 底部图片文字
     * @param source 图片源
     * @param text   文字本文
     */
    private static void insertFont(BufferedImage source, String text) {
        int width = SIZE, height = 50;
        BufferedImage textImage = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2 = (Graphics2D) textImage.getGraphics();
        //开启文字抗锯齿
        g2.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
        g2.setBackground(Color.WHITE);
        g2.clearRect(0, 0, width, height);
        g2.setPaint(Color.BLACK);
        FontRenderContext context = g2.getFontRenderContext();
        Font font = new Font("微软雅黑", Font.PLAIN, 16);
        g2.setFont(font);
        LineMetrics lineMetrics = font.getLineMetrics(text, context);
        FontMetrics fontMetrics = FontDesignMetrics.getMetrics(font);
        float offset = (width - fontMetrics.stringWidth(text)) / 2.0F;
        float y = (height + lineMetrics.getAscent() - lineMetrics.getDescent() - lineMetrics.getLeading()) / 2;

        g2.drawString(text, (int) offset, (int) y);
        Graphics2D graph = source.createGraphics();
        //开启文字抗锯齿
        graph.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
        graph.drawImage(textImage, 0, width - 30, textImage.getWidth(null), textImage.getHeight(null) + 10, null);
        graph.dispose();
        
        textImage.flush();
        source.flush();
    }
}