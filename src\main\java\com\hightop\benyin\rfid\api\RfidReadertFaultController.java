package com.hightop.benyin.rfid.api;

import com.hightop.benyin.rfid.application.service.RfidReaderFaultService;
import com.hightop.benyin.rfid.application.vo.dto.ReaderFaultDealDto;
import com.hightop.benyin.rfid.application.vo.query.RfidReaderFaultQuery;
import com.hightop.benyin.rfid.infrastructure.entity.RfidReaderFault;
import com.hightop.fario.base.web.Operation;
import com.hightop.fario.base.web.RestResponse;
import com.hightop.fario.common.mybatis.DataGrid;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * RFID基站故障管理rest接口
 *
 * <AUTHOR>
 * @date 2024-10-12 16:36:01
 */
@RequestMapping("/reader-fault")
@RestController
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Api(tags = "RFID基站故障管理")
public class RfidReadertFaultController {
    RfidReaderFaultService rfidReaderFaultService;


    @PostMapping("/page")
    @ApiOperation("分页查询")
    public RestResponse<DataGrid<RfidReaderFault>> page(@RequestBody RfidReaderFaultQuery pageQuery) {
        return RestResponse.ok(this.rfidReaderFaultService.page(pageQuery));
    }

    @PostMapping
    @ApiOperation("添加")
    public RestResponse<Void> add(@Validated @RequestBody RfidReaderFault rfidReaderFault) {
        return Operation.ADD.response(this.rfidReaderFaultService.save(rfidReaderFault));
    }

    @PutMapping
    @ApiOperation("处理故障")
    public RestResponse<Void> deal(@Validated @RequestBody ReaderFaultDealDto rfidReaderFaultDealDto) {
        return Operation.ADD.response(this.rfidReaderFaultService.deal(rfidReaderFaultDealDto));
    }

}
