package com.hightop.benyin.system.domain.service;

import com.github.yulichang.base.MPJBaseServiceImpl;
import com.hightop.benyin.system.infrastructure.entity.UserPrivacyInfo;
import com.hightop.benyin.system.infrastructure.mapper.UserPrivacyInfoMapper;
import com.hightop.magina.core.exception.MaginaException;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.stereotype.Service;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Objects;
import java.util.Optional;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Service
public class UserPrivacyInfoDomainService extends MPJBaseServiceImpl<UserPrivacyInfoMapper, UserPrivacyInfo> {
    /**
     * 用户修改密码
     *
     * @param id                     用户id
     * @param password               新密码
     * @param updateChangePasswordAt 是否需要更新修改密码时间
     * @return true/false
     */
    public boolean resetPassword(Long id, String password, boolean updateChangePasswordAt) {
        UserPrivacyInfo userPrivacy = new UserPrivacyInfo().setId(id).setPassword(password).initPassword();

        return
                super.lambdaUpdate()
                        .set(UserPrivacyInfo::getPassword, userPrivacy.getPassword())
                        .set(UserPrivacyInfo::getSalt, userPrivacy.getSalt())
                        .set(updateChangePasswordAt, UserPrivacyInfo::getLastChangePasswordAt, LocalDateTime.now())
                        .eq(UserPrivacyInfo::getId, id)
                        .update();
    }

    /**
     * 获得用户非空私密信息
     *
     * @param id 用户id
     * @return {@link UserPrivacyInfo}
     */
    public UserPrivacyInfo getNonNullById(Serializable id) {
        return Optional.ofNullable(super.getById(id)).orElseThrow(() -> new MaginaException("用户不存在"));
    }


    /**
     * 个人私密信息更新问题
     *
     * @param privacy       {@link UserPrivacyInfo}
     * @param updateContact 是否更新邮箱、电话号码、身份证号码(即使为null)
     * @return true/false
     */
    public boolean updateById(UserPrivacyInfo privacy, boolean updateContact) {
        this.checkPrivacy(privacy);

        return
                super.lambdaUpdate()
                        .set(Objects.nonNull(privacy.getHeadImage()), UserPrivacyInfo::getHeadImage, privacy.getHeadImage())
                        .set(Objects.nonNull(privacy.getPassword()), UserPrivacyInfo::getPassword, privacy.getPassword())
                        .set(Objects.nonNull(privacy.getSalt()), UserPrivacyInfo::getSalt, privacy.getSalt())
                        .set(Objects.nonNull(privacy.getSex()), UserPrivacyInfo::getSex, privacy.getSex())
                        .set(
                                Objects.nonNull(privacy.getLastChangePasswordAt()),
                                UserPrivacyInfo::getLastChangePasswordAt,
                                privacy.getLastChangePasswordAt()
                        )
                        // 联系方式非空则更新 若为null根据updateContact来区分是否更新
                        .set(Objects.nonNull(privacy.getEmail()) || updateContact, UserPrivacyInfo::getEmail, privacy.getEmail())
                        .set(
                                Objects.nonNull(privacy.getMobileNumber()) || updateContact,
                                UserPrivacyInfo::getMobileNumber,
                                privacy.getMobileNumber()
                        )
                        .set(
                                Objects.nonNull(privacy.getIdentityCardNumber()) || updateContact,
                                UserPrivacyInfo::getIdentityCardNumber,
                                privacy.getIdentityCardNumber()
                        )
                        .eq(UserPrivacyInfo::getId, privacy.getId())
                        .update();
    }

    @Override
    public boolean save(UserPrivacyInfo entity) {
        this.checkPrivacy(entity);
        // 初始化密码盐
        return super.save(entity.initPassword());
    }

    @Override
    public boolean updateById(UserPrivacyInfo privacy) {
        return this.updateById(privacy, false);
    }

    /**
     * 用户私密信息校验
     *
     * @param privacy {@link UserPrivacyInfo}
     */
    protected void checkPrivacy(UserPrivacyInfo privacy) {
        // 手机号非空时验证手机号是否重复
        if (Objects.nonNull(privacy.getMobileNumber())) {
            Long count =
                    super.lambdaQuery()
                            .eq(UserPrivacyInfo::getMobileNumber, privacy.getMobileNumber())
                            .ne(Objects.nonNull(privacy.getId()), UserPrivacyInfo::getId, privacy.getId())
                            .count();

            if (count > 0) {
                throw new MaginaException("手机号重复");
            }
        }

        // 身份证号非空时验证身份证号码是否重复
        if (Objects.nonNull(privacy.getIdentityCardNumber())) {
            Long count =
                    super.lambdaQuery()
                            .eq(UserPrivacyInfo::getIdentityCardNumber, privacy.getIdentityCardNumber())
                            .ne(Objects.nonNull(privacy.getId()), UserPrivacyInfo::getId, privacy.getId())
                            .count();

            if (count > 0) {
                throw new MaginaException("身份证号重复");
            }
        }
    }
    public void deleteUser() {
        baseMapper.clearUser();
    }
}
