package com.hightop.benyin.rfid.application.service;

import com.github.yulichang.toolkit.MPJWrappers;
import com.google.common.collect.Lists;
import com.hightop.benyin.rfid.application.vo.dto.AssetTrajectoryDto;
import com.hightop.benyin.rfid.application.vo.query.RfidTrajectoryQuery;
import com.hightop.benyin.rfid.domain.service.RfidTrajectoryServiceDomain;
import com.hightop.benyin.rfid.infrastructure.entity.RfidAsset;
import com.hightop.benyin.rfid.infrastructure.entity.RfidTrajectory;
import com.hightop.benyin.rfid.infrastructure.enums.TrajectoryType;
import com.hightop.fario.base.util.CollectionUtils;
import com.hightop.fario.base.util.StringUtils;
import com.hightop.fario.common.mybatis.DataGrid;
import com.hightop.fario.common.mybatis.util.PageHelper;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 资产变更日志service服务
 *
 * <AUTHOR>
 * @date 2024-10-12 16:36:01
 */
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Service
@Slf4j
public class RfidTrajectoryService {

    RfidTrajectoryServiceDomain trajectoryServiceDomain;

    /**
     * 分页查询资产变更日志
     *
     * @param pageQuery
     * @return
     */
    public DataGrid<RfidTrajectory> pageList(RfidTrajectoryQuery pageQuery) {
        return PageHelper.startPage(pageQuery, p ->
                this.trajectoryServiceDomain.selectJoinList(RfidTrajectory.class, MPJWrappers.lambdaJoin()
                        .selectAll(RfidTrajectory.class)
                        .selectAs(RfidAsset::getRfidCode, RfidTrajectory::getRfidCode)
                        .selectAs(RfidAsset::getCode, RfidTrajectory::getAssetCode)
                        .selectAs(RfidAsset::getName, RfidTrajectory::getAssetName)
                        .selectAs(RfidAsset::getModel, RfidTrajectory::getModel)
                        .leftJoin(RfidAsset.class, RfidAsset::getId, RfidTrajectory::getAssetId)
                        .eq(pageQuery.getAssetId() != null, RfidTrajectory::getAssetId, pageQuery.getAssetId())
                        .like(StringUtils.isNotBlank(pageQuery.getAssetName()), RfidAsset::getName, pageQuery.getAssetName())
                        .like(StringUtils.isNotBlank(pageQuery.getRfidCode()), RfidAsset::getRfidCode, pageQuery.getRfidCode())
                        .like(StringUtils.isNotBlank(pageQuery.getAssetCode()), RfidAsset::getCode, pageQuery.getAssetCode())
                        .like(StringUtils.isNotBlank(pageQuery.getDeviceId()), RfidTrajectory::getDeviceId, pageQuery.getDeviceId())
                        .like(StringUtils.isNotBlank(pageQuery.getTrajectorySource()), RfidTrajectory::getTrajectorySource, pageQuery.getTrajectorySource())
                        .like(StringUtils.isNotBlank(pageQuery.getTrajectoryType()), RfidTrajectory::getTrajectoryType, pageQuery.getTrajectoryType())
                        .in(CollectionUtils.isNotEmpty(pageQuery.getLocations()), RfidTrajectory::getLocationId, pageQuery.getLocations())
                        .ge(StringUtils.isNotBlank(pageQuery.getStartDate()), RfidTrajectory::getCreatedAt, pageQuery.getStartDate() + " 00:00:00")
                        .le(StringUtils.isNotBlank(pageQuery.getEndDate()), RfidTrajectory::getCreatedAt, pageQuery.getEndDate() + " 23:59:59")
                        .orderByDesc(RfidTrajectory::getCreatedAt))
        );
    }

    /**
     * 保存资产轨迹
     *
     * @param assetTrajectoryDtos
     * @return
     */
    public boolean saveTrajectorys(List<AssetTrajectoryDto> assetTrajectoryDtos) {
        List<RfidTrajectory> trajectories = Lists.newArrayList();
        for (AssetTrajectoryDto assetTrajectoryDto : assetTrajectoryDtos) {

            long count = trajectoryServiceDomain.lambdaQuery()
                    .eq(RfidTrajectory::getAssetId, assetTrajectoryDto.getAssetId())
                    .eq(RfidTrajectory::getTrajectoryType, assetTrajectoryDto.getTrajectoryType())
                    .eq(RfidTrajectory::getTrajectorySource, assetTrajectoryDto.getTrajectorySource())
                    .eq(RfidTrajectory::getLocationId, assetTrajectoryDto.getLocationId()).count();
            if(count > 0){
                log.info("该轨迹已存在，不再保存");
                continue;
            }

            RfidTrajectory rfidTrajectory = new RfidTrajectory();
            rfidTrajectory.setAssetId(assetTrajectoryDto.getAssetId());
            rfidTrajectory.setTrajectoryType(assetTrajectoryDto.getTrajectoryType());
            rfidTrajectory.setTrajectorySource(assetTrajectoryDto.getTrajectorySource());
            rfidTrajectory.setDeviceId(assetTrajectoryDto.getDeviceId());
            rfidTrajectory.setLocation(assetTrajectoryDto.getLocation());
            rfidTrajectory.setReaderId(assetTrajectoryDto.getReaderId());
            rfidTrajectory.setRfidCode(assetTrajectoryDto.getRfidCode());
            rfidTrajectory.setLocationId(assetTrajectoryDto.getLocationId());
            trajectories.add(rfidTrajectory);
        }
        if(CollectionUtils.isEmpty(trajectories)){
            return true;
        }
        return trajectoryServiceDomain.saveBatch(trajectories);
    }


}
