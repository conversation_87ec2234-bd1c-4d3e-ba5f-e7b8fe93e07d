package com.hightop.benyin.rfid.infrastructure.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelIgnore;
import com.baomidou.mybatisplus.annotation.*;
import com.hightop.benyin.configurer.annotation.RecordLogField;
import com.hightop.benyin.configurer.enums.DataType;
import com.hightop.benyin.rfid.infrastructure.enums.AssetBusinessType;
import com.hightop.benyin.rfid.infrastructure.enums.AssetFlowStatus;
import com.hightop.benyin.share.infrastructure.util.DictUtil;
import com.hightop.fario.common.jackson.annotation.JsonAmount;
import com.hightop.magina.standard.code.dictionary.bind.DictItemBind;
import com.hightop.magina.standard.code.dictionary.bind.DictItemEntry;
import com.hightop.magina.standard.ums.user.bind.UserBind;
import com.hightop.magina.standard.ums.user.bind.UserEntry;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 资产领借用明细表
 *
 * <AUTHOR>
 * @date 2023-11-15 17:19:37
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@TableName("b_rfid_asset_apply")
@ApiModel
public class RfidAssetApply {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @ApiModelProperty("id")
    Long id;

    @TableField("asset_id")
    @ApiModelProperty("资产id")
    Long assetId;

    @TableField("rfid_code")
    @ApiModelProperty("rfid编码")
    String rfidCode;

    @TableField("flow_code")
    @ApiModelProperty("申请单号")
    String flowCode;

    @TableField("agent_id")
    @ApiModelProperty("经办人id")
    Long agentId;

    @TableField("agent_name")
    @ApiModelProperty("经办人")
    String agentName;

    @TableField(value = "agent_at")
    @ApiModelProperty("经办人处理时间")
    LocalDateTime agentAt;

    @ApiModelProperty("使用状态")
    @DictItemBind(DictUtil.DICT_USE_STATE)
    @TableField(value = "use_state")
    DictItemEntry useState;

    @TableField(exist = false)
    @ApiModelProperty("资产编码")
    String code;

    @TableField(exist = false)
    @ApiModelProperty("资产名称")
    String name;


    @TableField(exist = false)
    @ApiModelProperty("规格")
    String model;


    @TableField(exist = false)
    @ApiModelProperty("价格")
    @JsonAmount
    Long price;

    @TableField(exist = false)
    @ApiModelProperty("资产小类")
    String assetType;

    @TableField(exist = false)
    @ApiModelProperty("资产小类")
    Integer type;

    @TableField(exist = false)
    @ApiModelProperty("资产类型")
    String assetTypeName;


    @TableField(exist = false)
    @ApiModelProperty("位置编码")
    String locationCode;

    @TableField(exist = false)
    @ApiModelProperty("原位置")
    String location;

    @TableField(exist = false)
    @ApiModelProperty(value = "责任人姓名", required = true)
    String managerName;

    @TableField(exist = false)
    @ApiModelProperty("责任部门")
    String managerDeptName;

    @TableField(exist = false)
    @ApiModelProperty("基站编码")
    String readerCode;

    @TableField(exist = false)
    @ApiModelProperty("申请部门")
    String departmentName;


    @TableField(exist = false)
    @ApiModelProperty("子级资产")
    List<RfidAssetApply> children;

    @TableField(exist = false)
    @ApiModelProperty("业务类型")
    @Excel(name = "业务类型", width = 22, orderNum = "2",enumExportField = "name")
    AssetBusinessType operateType;

    @TableField(exist = false)
    @ApiModelProperty("审核人姓名")
    String approveName;

    @TableField(exist = false)
    @ApiModelProperty("审核时间")
    @Excel(name = "审核时间", width = 30, orderNum = "10", format = "yyyy/MM/dd HH:mm:ss")
    LocalDateTime approveAt;

    @TableField(exist = false)
    @ApiModelProperty("变更位置")
    String changeLocation;

    @TableField(exist = false)
    @ApiModelProperty("变更基站")
    String changeReader;


    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty("最近更新时间")
    @Excel(name = "最近更新时间", width = 30, format = "yyyy/MM/dd HH:mm:ss", orderNum = "8")
    LocalDateTime updatedAt;

    @TableField(value = "updated_by")
    @ApiModelProperty("最近更新人")
    Long updatedBy;

    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @ApiModelProperty("创建时间")
    LocalDateTime createdAt;

    @TableField("created_by")
    @ApiModelProperty("创建人")
    @UserBind
    UserEntry createdBy;


}
