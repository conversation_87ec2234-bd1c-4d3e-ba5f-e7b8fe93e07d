package com.hightop.benyin.share.application.service;

import com.github.yulichang.toolkit.MPJWrappers;
import com.hightop.magina.standard.code.dictionary.dict.Dict;
import com.hightop.magina.standard.code.dictionary.item.DictItem;
import com.hightop.magina.standard.code.dictionary.item.DictItemDomainService;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

@Service
@Primary
public class DictItemDomainServiceExtends extends DictItemDomainService {
    @Override
    public DictItem getByDictCodeAndValue(String code, String value) {
        return this.selectJoinOne(DictItem.class, MPJWrappers.<DictItem>lambdaJoin()
                                        .selectAll(DictItem.class)
                                        .innerJoin(Dict.class, on -> on.eq(Dict::getId, DictItem::getDictId).eq(Dict::getCode, code))
                                        .eq(DictItem::getValue, value)
                        );
    }

    /**
     * 通过字典id、字典项值查询字典
     * @param dictId 字典id
     * @param value  字典项值
     * @return {@link DictItem}
     * @since 2.3.3
     */
    @Override
    public DictItem getByDictIdAndValue(Long dictId, String value) {
        return this.lambdaQuery()
                        .eq(DictItem::getDictId, dictId)
                        .eq(DictItem::getValue, value)
                        .one();
    }
}
