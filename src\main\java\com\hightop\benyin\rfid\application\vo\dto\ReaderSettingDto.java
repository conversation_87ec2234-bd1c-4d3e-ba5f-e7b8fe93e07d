package com.hightop.benyin.rfid.application.vo.dto;

import com.hightop.benyin.share.socket.enums.CommandType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Description: 资产领用DTO
 * @Author: X.S
 * @Date: 2023/12/22 17:16
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@ApiModel("资产领用DTO")
public class ReaderSettingDto {


    @ApiModelProperty("id")
    @NotNull(message = "基站信息不能为空")
    Long id;

    @ApiModelProperty("命令类型REST-复位,DORMANCY-节能模式,BELL-蜂鸣器 ")
    @NotNull(message = "命令类型不能为空")
    CommandType command;

    @ApiModelProperty("状态1-开启，2-关闭")
    Integer status;
}
