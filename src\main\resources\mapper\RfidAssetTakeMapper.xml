<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hightop.benyin.rfid.infrastructure.mapper.RfidAssetTakeMapper">

    <resultMap id="takeResultMap" type="com.hightop.benyin.rfid.infrastructure.entity.RfidAssetTake" autoMapping="true">
        <result column="range_info" property="rangeInfo"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
    </resultMap>

    <select id="findAssetTakeList" resultMap="takeResultMap">
        select t.*
        from b_rfid_asset_take t
        left join st_user_basic t1 on t1.id = t.created_by
        <where>
           t.deleted = 0
            <if test="qo.code!= null and qo.code!= ''">
                and t.code like concat ('%',#{qo.code},'%')
            </if>

            <if test="qo.createdBy!= null and qo.createdBy!= ''">
                and t1.name like concat ('%',#{qo.createdBy},'%')
            </if>

            <if test="qo.auditName!= null and qo.auditName!= ''">
                and t.audit_name like concat ('%',#{qo.auditName},'%')
            </if>

            <if test="qo.status!= null and qo.status!= ''">
                and t.status =#{qo.status}
            </if>

            <if test="null!=qo.scanStatus and !qo.scanStatus.isEmpty()">
                and t.scan_status in
                <foreach collection="qo.scanStatus" item="id" separator="," open="(" close=")">
                    #{id}
                </foreach>
            </if>
            <if test="null!=qo.takeStatus and !qo.takeStatus.isEmpty()">
                and t.take_status in
                <foreach collection="qo.takeStatus" item="id" separator="," open="(" close=")">
                    #{id}
                </foreach>
            </if>
            <if test="null!=qo.takeType and !qo.takeType.isEmpty()">
                and t.take_type in
                <foreach collection="qo.takeType" item="id" separator="," open="(" close=")">
                    #{id}
                </foreach>
            </if>

            <if test="null!=qo.takeRange and !qo.takeRange.isEmpty()">
                and t.take_range in
                <foreach collection="qo.takeRange" item="id" separator="," open="(" close=")">
                    #{id}
                </foreach>
            </if>

            <if test="null!=qo.departmentIds and !qo.departmentIds.isEmpty()">
                exists (select 1 from b_rfid_asset_record t2 where t2.take_code = t.code and t2.department_id in
                <foreach collection="qo.departmentIds" item="id" separator="," open="(" close=")">
                    #{id}
                </foreach>
                )
            </if>

            <if test="null != qo.startDate and '' != qo.startDate ">
                and t.created_at &gt;= concat(#{qo.startDate},' 00:00:00')
            </if>
            <if test="null != qo.endDate and '' != qo.endDate ">
                and t.created_at &lt;= concat(#{qo.endDate},' 23:59:59')
            </if>

            <if test="null != qo.startAuditDate and '' != qo.startAuditDate ">
                and t.audit_at &gt;= concat(#{qo.startAuditDate},' 00:00:00')
            </if>
            <if test="null != qo.endAuditDate and '' != qo.endAuditDate ">
                and t.audit_at &lt;= concat(#{qo.endAuditDate},' 23:59:59')
            </if>

            <if test="null != qo.startCompletedDate and '' != qo.startCompletedDate ">
                and t.completed_at &gt;= concat(#{qo.startCompletedDate},' 00:00:00')
            </if>
            <if test="null != qo.endCompletedDate and '' != qo.endCompletedDate ">
                and t.completed_at &lt;= concat(#{qo.endCompletedDate},' 23:59:59')
            </if>

        </where>
        order by t.created_at desc
    </select>
    <resultMap id="takeDetailResultMap" type="com.hightop.benyin.rfid.application.vo.po.RfidAssetTakeDetailVo" autoMapping="true">
        <result column="range_info" property="rangeInfo" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler" />
    </resultMap>

    <select id="findAssetTakeDetailList" resultMap="takeDetailResultMap">
        select t.*,t1.take_type,t1.take_range,t1.range_info,
        t2.code,t2.name,t2.model,t2.price,t2.apply_name,t2.use_state,t.created_at,t2.asset_type,t2.parent_code
        ,t2.name assetTypeName
        from b_rfid_asset_take_detail t
        left join b_rfid_asset_take t1 on t1.code=t.take_code
        left join b_rfid_asset t2 on t2.id=t.asset_id
        left join b_asset_type t3 on t3.code=t2.asset_type
        <where>
            <if test="qo.takeCode!= null and qo.takeCode!= ''">
                and t.take_code like concat ('%',#{qo.takeCode},'%')
            </if>
            <if test="qo.rfidCode!= null and qo.rfidCode!= ''">
                and t.rfid_code like concat ('%',#{qo.code},'%')
            </if>

            <if test="qo.name!= null and qo.name!= ''">
                and t2.name like concat ('%',#{qo.name},'%')
            </if>

            <if test="qo.code!= null and qo.code!= ''">
                and t2.code like concat ('%',#{qo.code},'%')
            </if>

            <if test="qo.hasTag!= null ">
                and t.has_tag = #{qo.hasTag}
            </if>

            <if test="qo.model!= null and qo.model!= ''">
                and t2.model like concat ('%',#{qo.model},'%')
            </if>


            <if test="null!=qo.useState and !qo.useState.isEmpty()">
                and t2.use_state in
                <foreach collection="qo.useState" item="id" separator="," open="(" close=")">
                    #{id}
                </foreach>
            </if>
            <if test="null!=qo.assetType and !qo.assetType.isEmpty()">
                and t2.asset_type in
                <foreach collection="qo.assetType" item="id" separator="," open="(" close=")">
                    #{id}
                </foreach>
            </if>

            <if test="null != qo.startDate and '' != qo.startDate ">
                and t.created_at &gt;= concat(#{qo.startDate},' 00:00:00')
            </if>
            <if test="null != qo.endDate and '' != qo.endDate ">
                and t.created_at &lt;= concat(#{qo.endDate},' 23:59:59')
            </if>

            <if test="null!=qo.takeStatus and !qo.takeStatus.isEmpty()">
                and t.take_status in
                <foreach collection="qo.takeStatus" item="id" separator="," open="(" close=")">
                    #{id}
                </foreach>
            </if>
            <if test="null!=qo.scanStatus and !qo.scanStatus.isEmpty()">
                and t.scan_status in
                <foreach collection="qo.scanStatus" item="id" separator="," open="(" close=")">
                    #{id}
                </foreach>
            </if>
            <if test="null!=qo.status and !qo.status.isEmpty()">
                and t.status in
                <foreach collection="qo.status" item="id" separator="," open="(" close=")">
                    #{id}
                </foreach>
            </if>
            <if test="null!=qo.applyIds and !qo.applyIds.isEmpty()">
                and t.apply_id in
                <foreach collection="qo.applyIds" item="id" separator="," open="(" close=")">
                    #{id}
                </foreach>
            </if>

            <if test="null!=qo.managerIds and !qo.managerIds.isEmpty()">
                and t.manager_id in
                <foreach collection="qo.managerIds" item="id" separator="," open="(" close=")">
                    #{id}
                </foreach>
            </if>


            <if test="null!=qo.managerDeptIds and !qo.managerDeptIds.isEmpty()">
                and t.manager_dept_id in
                <foreach collection="qo.managerDeptIds" item="id" separator="," open="(" close=")">
                    #{id}
                </foreach>
            </if>


            <if test="null!=qo.departmentIds and !qo.departmentIds.isEmpty()">
                and t.department_id in
                <foreach collection="qo.departmentIds" item="id" separator="," open="(" close=")">
                    #{id}
                </foreach>
            </if>

            <if test="null!=qo.locations and !qo.locations.isEmpty()">
                and t.location_id in
                <foreach collection="qo.locations" item="id" separator="," open="(" close=")">
                    #{id}
                </foreach>
            </if>

        </where>
        order by t.created_at,t.take_code,t.department_id desc
    </select>


    <select id="findAssetTakeScanList" resultType="com.hightop.benyin.rfid.application.vo.po.RfidAssetTakeDetailVo">

        select t.* from (
        select distinct t.id,t.rfid_code,t.asset_id,t2.name
        ,t2.model,t2.code,t.location, case when t1.id is null then 'OVERFLOW' else 'NORMAL' end takeStatus
        from b_rfid_info t
        left join b_rfid_asset_take_detail t1 on t1.take_code = t.bind_code
        left join b_rfid_asset t2 on t2.id = t.asset_id
        where t.type = 20 and t.status = 3
        and t.bind_code = #{qo.takeCode}

        <if test="null!=qo.status and ''!= qo.status ">
            and t.status = #{qo.status}
        </if>
        <if test="null!=qo.rfidCode and ''!= qo.rfidCode ">
            and t.rfid_code like concat ('%',#{qo.rfidCode},'%')
        </if>
        ) t
        where 1=1
        <if test="qo.takeStatus!= null and qo.takeStatus!= ''">
            and t.takeStatus =#{qo.takeStatus}
        </if>
        order by t.rfid_code desc
    </select>

</mapper>