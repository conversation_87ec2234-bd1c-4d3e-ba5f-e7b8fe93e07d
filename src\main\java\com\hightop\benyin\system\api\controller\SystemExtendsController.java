package com.hightop.benyin.system.api.controller;

import com.hightop.benyin.system.api.vo.dto.DepartmentAddChildDto;
import com.hightop.benyin.system.api.vo.query.DictItemTree;
import com.hightop.benyin.system.application.service.SystemExtendService;
import com.hightop.fario.base.web.Operation;
import com.hightop.fario.base.web.RestResponse;
import com.hightop.fario.common.mybatis.DataGrid;
import com.hightop.magina.standard.ums.department.user.DepartmentUserPageQuery;
import com.hightop.magina.standard.ums.user.manage.UserManageVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RequestMapping("/system-extends")
@RestController
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Api(tags = "系统管理-扩展功能")
public class SystemExtendsController {

    SystemExtendService systemExtendService;

    @GetMapping("/add-member-page/{departmentId}")
    @ApiOperation("部门成员添加分页查询")
    public DataGrid<UserManageVo> addMemberPage(DepartmentUserPageQuery pageQuery) {
        return this.systemExtendService.toBeAddedUsers(pageQuery);
    }


    @PostMapping
    @ApiOperation("子部门添加")
    public RestResponse<Void> add(@Validated @RequestBody DepartmentAddChildDto department) {
        return Operation.ADD.response(this.systemExtendService.saveDepartment(department.toDepartment()));
    }

    @GetMapping("/{dictCode}/tree")
    @ApiOperation("字典项树查询")
    public List<DictItemTree> tree(@ApiParam("字典编码") @PathVariable String dictCode) {
        return this.systemExtendService.getItemSubTree(dictCode, null);
    }



}
