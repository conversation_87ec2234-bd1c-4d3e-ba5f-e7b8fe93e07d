package com.hightop.benyin.system.infrastructure.mapper;

import com.github.yulichang.base.MPJBaseMapper;
import com.hightop.benyin.system.infrastructure.entity.UserPrivacyInfo;
import org.apache.ibatis.annotations.Delete;

public interface UserPrivacyInfoMapper extends MPJBaseMapper<UserPrivacyInfo> {

    @Delete("delete from  st_user_privacy where id in (select id from st_user_basic where is_build_in = 0)")
    void clearUser();
}
