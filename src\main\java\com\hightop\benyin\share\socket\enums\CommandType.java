package com.hightop.benyin.share.socket.enums;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.hightop.benyin.share.infrastructure.enums.MessageType;
import com.hightop.magina.core.custom.entry.EnumEntry;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.Arrays;

/**
 * 命令类型枚举
 *
 * <AUTHOR>
 * @date 2023/12/20 17:22
 */
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Getter
public enum CommandType implements EnumEntry<Integer> {
    /**
     * 心跳检测 176
     */
    HEARTBEAT(0xB0, MessageType.RECEIVE, "心跳检测", "heartbeatHandler"),
    /**
     * 复位 177
     */
    REST(0xB1, MessageType.SEND, "复位", ""),
    /**
     * 上报迁入-178
     */
    REPORT_ADD(0xB2, MessageType.RECEIVE, "上报迁入", "reportAddHandler"),
    /**
     * 上报迁出-179
     */
    REPOST_SUB(0xB3, MessageType.RECEIVE, "上报迁出", "reportSubHandler"),
    /**
     * 工作参数查询-180
     */
    GET_WORK_PARAM(0xB4, MessageType.BOTHWAY, "基站工作参数", "workParamHandler"),
    /**
     * 下发标签到基站（全部） 181
     */
    DOWNLOAD_TAG(0xB5, MessageType.SEND, "下发标签", "sendTagHandler"),
    /**
     * 基站标签数据查询 182
     */
    MANAGER_TAG(0xB6, MessageType.BOTHWAY, "基站标签数据查询", "managerTagHandler"),
    /**
     * 集中扫描 183
     */
    TAKE_STOCK(0xB7, MessageType.RECEIVE, "集中扫描", "takeStockHandler"),

    /**
     * 设置日常扫描参数 184
     */
    SET_SCAN_STATE(0xB8, MessageType.BOTHWAY, "配置日常扫描参数", "setScanHandler"),
    /**
     * 设置心跳周期 185
     */
    SET_HEARTBEAT(0xB9, MessageType.BOTHWAY, "配置心跳周期", "setHeartbeatHandler"),

    /**
     * 远程升级到模块 186
     */
    DIRECT_COMMAND(0xBA, MessageType.BOTHWAY, "远程升级", "upgradesHandler"),

    /**
     * 节能模式 187
     */
    SET_DORMANCY(0xBB, MessageType.BOTHWAY, "节能模式", "setDormancyHandler"),

    /**
     * 蜂鸣器 188
     */
    SET_BELL(0xBC, MessageType.BOTHWAY, "蜂鸣器", "setBellHandler"),

    /**
     * 配置集中扫描参数 189
     */
    SET_FOCUS_SCAN(0xBD, MessageType.BOTHWAY, "配置集中扫描参数", "setFocusScanHandler"),

    /**
     * 配置自动空闲状态 190
     */
    SET_LEISURE(0xBE, MessageType.BOTHWAY, "配置自动空闲", "setLeisureHandler"),

    /**
     * 查询心跳的参数 191
     */
    GET_HEARTBEAT_PARAM(0xBF, MessageType.BOTHWAY, "查询心跳参数", "getHeartbeatParamHandler"),

    /**
     * 查询日常扫描参数 192
     */
    GET_SCAN_PARAM(0xC0, MessageType.BOTHWAY, "查询日常扫描参数", "getScanParamHandler"),

    /**
     * 查询集中扫描参数 193
     */
    GET_FOCUS_SCAN_PARAM(0xC1, MessageType.BOTHWAY, "查询集中扫描参数", "getFousParamHandler"),

    /**
     * 设置空闲时间/扫描时间的参数 194
     */
    SET_SCAN_TIME(0xC2, MessageType.BOTHWAY, "设置空闲时间/扫描时间的参数", "setScanTimeHandler"),

    /**
     * 查询固件版本/协议的版本 195
     */
    GET_VERSION(0xC3, MessageType.BOTHWAY, "查询固件版本/协议的版本", "getVersionHandler"),

    /**
     * 设置透传开关的状态 196
     */
    SET_DIRECT (0xC4, MessageType.BOTHWAY, "设置透传开关的状态", "setDirectHandler"),
    /**
     * 查询透传开关的状态 197
     */
    GET_DIRECT (0xC5, MessageType.BOTHWAY, "查询透传开关的状态", "getDirectHandler"),

    /**
     * 擦除配置数据 198
     */
    CLEAR(0xC6, MessageType.SEND, "擦除配置数据", "clearHandler"),

    /**
     * 故障上报 199
     */
    ERROR_REPORT(0xC7, MessageType.SEND, "故障上报", "reportErrorHandler"),
    /**
     * 设置WIFI和服务器的参数 200
     */
    SET_NET_SETTINGS (0xC8, MessageType.BOTHWAY, "设置WIFI和服务器的参数", "setNetSettingsHandler"),
    /**
     * 查询WIFI和服务器的参数 201
     */
    GET_NET_SETTINGS (0xC9, MessageType.BOTHWAY, "查询WIFI和服务器的参数", "getNetSettingsHandler"),
    /**
     * 设置基站的序列号 202
     */
    SET_DEVICEID (0xCA, MessageType.BOTHWAY, "设置基站的序列号", "setDeviceIdHandler"),
    /**
     * 查询基站的序列号 203
     */
    GET_DEVICEID (0xCB, MessageType.BOTHWAY, "查询基站的序列号", "getDeviceIdHandler"),

    /**
     * 上报日常扫到正常的标签 204
     */
    REPORT_NORMAL (0xCC, MessageType.RECEIVE, "上报日常扫到正常的标签", "reportNormalHandler"),

    /**
     * 上报日常扫到增加和减少的标签 205
     */
    REPORT_DIS (0xCC, MessageType.RECEIVE, "上报日常扫到增加和减少的标签", "reportDisHandler"),

    /**
     * 日常扫描所有标签 206
     */
    REPORT_ALL (0xCC, MessageType.RECEIVE, "日常扫描所有标签", "reportAllHandler")

    ;

    /**
     * 命令
     */
    Integer code;

    /**
     * 操作类型 0-上报 1-下发
     */
    @JsonIgnore
    MessageType type;

    /**
     * 操作说明
     */
    String name;

    /**
     * 处理类 接收类消息处理 及 发送消息失败处理
     */
    String beanName;

    public static CommandType getCommand(Integer code) {
        return Arrays.stream(CommandType.values()).filter(v -> v.code.equals(code)).findFirst().orElse(null);
    }

}
