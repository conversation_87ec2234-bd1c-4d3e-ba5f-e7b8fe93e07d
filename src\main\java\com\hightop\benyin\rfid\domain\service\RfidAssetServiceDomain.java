package com.hightop.benyin.rfid.domain.service;

import com.github.yulichang.base.MPJBaseServiceImpl;
import com.hightop.benyin.rfid.application.vo.po.AssetTotalVo;
import com.hightop.benyin.rfid.application.vo.query.RfidAssetQuery;
import com.hightop.benyin.rfid.infrastructure.entity.RfidAsset;
import com.hightop.benyin.rfid.infrastructure.mapper.RfidAssetMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * rfid资产管理领域服务
 *
 * <AUTHOR>
 * @date 2024-10-12 16:36:01
 */
@Service
public class RfidAssetServiceDomain extends MPJBaseServiceImpl<RfidAssetMapper, RfidAsset> {


    /**
     * 根据条件查询资产列表
     *
     * @param rfidAssetQuery
     * @return
     */
    public List<RfidAsset> getAssetList(RfidAssetQuery rfidAssetQuery) {
        return baseMapper.getAssetList(rfidAssetQuery);
    }

    /**
     * 根据条件查询资产总数
     *
     * @param rfidAssetQuery
     * @return
     */
    public AssetTotalVo getAssetTotal(@Param("qo") RfidAssetQuery rfidAssetQuery) {
        return baseMapper.getAssetTotal(rfidAssetQuery);
    }

    /**
     * 根据条件查询未绑定标签资产总数
     *
     * @param rfidAssetQuery
     * @return
     */
    public Long getNotBindTotal(RfidAssetQuery rfidAssetQuery) {
        return baseMapper.getNotBindTotal(rfidAssetQuery);
    }

    /**
     * 根据条件查询未绑定基站资产总数
     *
     * @param rfidAssetQuery
     * @return
     */
    public Long getNotReaderTotal(RfidAssetQuery rfidAssetQuery) {
        return baseMapper.getNotReaderTotal(rfidAssetQuery);
    }

    public void clearAsset() {
        baseMapper.clearAsset();
    }
}
