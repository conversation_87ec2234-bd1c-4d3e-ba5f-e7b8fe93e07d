package com.hightop.benyin.system.api.vo.query;

import com.hightop.fario.common.mybatis.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldDefaults;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@FieldDefaults(level = AccessLevel.PRIVATE)
@ApiModel("带权限查询分页父类DTO")
public class AuthPageQuery extends PageQuery {

    @ApiModelProperty("所属公司")
    Long companyId;

    @ApiModelProperty("部门权限")
    Long deptId;

    @ApiModelProperty("多个部门权限")
    List<Long> deptIds;

    @ApiModelProperty("多个人员权限")
    List<Long> userIds;

    @ApiModelProperty("人员权限")
    Long userId;


}
