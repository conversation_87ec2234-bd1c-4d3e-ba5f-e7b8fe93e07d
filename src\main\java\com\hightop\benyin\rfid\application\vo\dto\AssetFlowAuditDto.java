package com.hightop.benyin.rfid.application.vo.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.hightop.benyin.rfid.infrastructure.enums.AssetFlowStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.util.List;

/**
 * @Description: 资产登记领用审核DTO
 * @Author: X.S
 * @Date: 2023/12/22 17:16
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@ApiModel("资产登记领用审核DTO")
public class AssetFlowAuditDto {

    @ApiModelProperty("资产id集合")
    List<Long> ids;

    @ApiModelProperty("状态")
    AssetFlowStatus status;

    @ApiModelProperty("id")
    Long id;

    @ApiModelProperty("审批人id")
    Long approveId;

    @ApiModelProperty("审批人姓名")
    String approveName;

    @ApiModelProperty("审核意见")
    String content;

    @ApiModelProperty("变更位置")
    Long changeLocationId;

    @ApiModelProperty("变更基站")
    Long changeReaderId;

}
