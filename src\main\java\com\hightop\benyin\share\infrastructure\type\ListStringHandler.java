package com.hightop.benyin.share.infrastructure.type;

import com.hightop.fario.base.constant.StringConstants;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 分号分隔字符串与{@link List}之间的类型转换器
 * <AUTHOR>
 * @Date 2023/11/8 14:48
 */
@MappedJdbcTypes({JdbcType.VARCHAR, JdbcType.CHAR})
public class ListStringHandler extends BaseTypeHandler<List<String>> {
    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, List<String> param, JdbcType jdbcType)
        throws SQLException {
        ps.setString(i, String.join(StringConstants.SEMICOLON, param));
    }

    @Override
    public List<String> getNullableResult(ResultSet rs, String s) throws SQLException {
        return string2List(rs.getString(s), rs.wasNull());
    }

    @Override
    public List<String> getNullableResult(ResultSet rs, int i) throws SQLException {
        return string2List(rs.getString(i), rs.wasNull());
    }

    @Override
    public List<String> getNullableResult(CallableStatement cs, int i) throws SQLException {
        return string2List(cs.getString(i), cs.wasNull());
    }

    private static List<String> string2List(String str, boolean wasNull) {
        if (wasNull) {
            return new ArrayList<>();
        }

        return Stream.of(str.split(StringConstants.SEMICOLON)).collect(Collectors.toList());
    }
}
