package com.hightop.benyin.system.api.vo.dto;

import com.hightop.benyin.system.infrastructure.entity.UserInfo;
import com.hightop.benyin.system.infrastructure.entity.UserPrivacyInfo;
import com.hightop.magina.casual.key.DecryptUnit;
import com.hightop.magina.casual.key.KeyDomainService;
import com.hightop.magina.casual.key.KeyTokenDto;
import com.hightop.magina.standard.cipher.CipherText;
import com.hightop.magina.standard.ums.user.basic.UserBasic;
import com.hightop.magina.standard.ums.user.basic.UserType;
import com.hightop.magina.standard.ums.user.privacy.UserSex;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;


/**
 * 用户启停DTO
 *
 * @Author: X.S
 * @date 2024/09/29 13:58
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@ApiModel("用户修改DTO")
public class UserUpdateDto extends KeyTokenDto {

    @ApiModelProperty("id")
    Long id;

    @ApiModelProperty("账号")
    @NotBlank(message = "账号不能为空")
    @Size(min = 1, max = 64, message = "账号长度在{min}至{max}之间")
    String code;

    @NotBlank(message = "名字不能为空")
    @Size(min = 1, max = 64, message = "名字长度在{min}至{max}之间")
    @ApiModelProperty("名字")
    String name;

    @ApiModelProperty("账户失效日期")
    LocalDateTime accountExpireAt;

    @ApiModelProperty("邮箱密文")
    String email;

    @ApiModelProperty("电话号码密文")
    String mobileNumber;

    @ApiModelProperty("身份证号码密文")
    String identityCardNumber;

    @ApiModelProperty("性别")
    @NotNull(message = "性别不能为空")
    UserSex sex;

    @ApiModelProperty("启用状态")
    Boolean isAvailable;

    @ApiModelProperty("部门")
    Long departmentId;

    @ApiModelProperty("位置")
    Long locationId;

    /**
     * 转为{@link UserBasic}
     *
     * @param id 用户id
     * @return {@link UserBasic}
     */
    public UserInfo toBasic(Long id) {
        return new UserInfo().setId(id).setName(this.name).setType(UserType.PERMANENT).setAccountExpireAt(null);
    }

    /**
     * 转为{@link UserPrivacyInfo}
     *
     * @param id               用户id
     * @param keyDomainService {@link KeyDomainService}
     * @return {@link UserPrivacyInfo}
     */
    public UserPrivacyInfo toPrivacy(Long id, KeyDomainService keyDomainService) {
        UserPrivacyInfo userPrivacy = new UserPrivacyInfo().setId(id).setSex(this.sex);
        // 数据解密
        keyDomainService.decrypt(super.getCipherToken(), this.decryptUnits(userPrivacy).toArray(new DecryptUnit[0]));

        return userPrivacy;
    }

    /**
     * 加密单元
     *
     * @param userPrivacy {@link UserPrivacyInfo}
     * @return {@link List}
     */
    protected List<DecryptUnit> decryptUnits(UserPrivacyInfo userPrivacy) {
        List<DecryptUnit> list = new ArrayList<>();

        list.add(DecryptUnit.ofNotEmpty(this.email, it -> userPrivacy.setEmail(new CipherText(it))));
        list.add(DecryptUnit.ofNotEmpty(this.mobileNumber, it -> userPrivacy.setMobileNumber(new CipherText(it))));
        list.add(
                DecryptUnit.ofNotEmpty(
                        this.identityCardNumber,
                        it -> userPrivacy.setIdentityCardNumber(new CipherText(it))
                )
        );

        return list;
    }
}