package com.hightop.benyin.rfid.domain.service;

import com.github.yulichang.base.MPJBaseServiceImpl;
import com.hightop.benyin.rfid.application.vo.query.RfidInfoQuery;
import com.hightop.benyin.rfid.infrastructure.entity.RfidInfo;
import com.hightop.benyin.rfid.infrastructure.mapper.RfidInfoMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * rfid管理领域服务
 *
 * <AUTHOR>
 * @date 2024-10-12 16:36:01
 */
@Service
public class RfidInfoServiceDomain extends MPJBaseServiceImpl<RfidInfoMapper, RfidInfo> {

    /**
     * 获取扫描列表
     *
     * @return
     */
    public List<RfidInfo> getScanList(RfidInfoQuery query) {
        return baseMapper.getScanList(query);
    }

    public void clear() {
        baseMapper.clear();
    }
}
