package com.hightop.benyin.system.api.vo.query;

import com.baomidou.mybatisplus.annotation.TableField;
import com.hightop.benyin.system.infrastructure.enums.BusinessType;
import com.hightop.benyin.system.infrastructure.enums.OperationType;
import com.hightop.fario.common.mybatis.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldDefaults;

import java.util.List;

/**
 * 位置管理查询
 * <AUTHOR>
 * @date 2024-10-12 16:36:01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@FieldDefaults(level = AccessLevel.PRIVATE)
@ApiModel("位置管理查询DTO")
public class DataLogQuery extends PageQuery {

    @ApiModelProperty(" 数据编码")
    String businessCode;

    @ApiModelProperty("编码")
    String code;

    @ApiModelProperty(" 数据名称")
    String name;

    @ApiModelProperty("业务类型")
    BusinessType businessType;

    @ApiModelProperty("操作类型")
    String operationType;

    @ApiModelProperty("操作人")
    String createdBy;

    @ApiModelProperty("修改前")
    String oriData;

    @ApiModelProperty("修改后")
    String newData;

    @ApiModelProperty("开始日期")
    String startDate;
    @ApiModelProperty("结束日期")
    String endDate;

}
