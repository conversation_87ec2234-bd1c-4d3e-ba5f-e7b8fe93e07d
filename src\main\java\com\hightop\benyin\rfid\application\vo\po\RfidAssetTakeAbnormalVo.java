package com.hightop.benyin.rfid.application.vo.po;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelIgnore;
import com.baomidou.mybatisplus.annotation.TableField;
import com.hightop.benyin.rfid.infrastructure.enums.DiscrepancyStatus;
import com.hightop.benyin.rfid.infrastructure.enums.TakeScanStatus;
import com.hightop.benyin.rfid.infrastructure.enums.TakeStatus;
import com.hightop.benyin.share.infrastructure.util.DictUtil;
import com.hightop.fario.common.jackson.annotation.JsonAmount;
import com.hightop.magina.standard.code.dictionary.bind.DictItemBind;
import com.hightop.magina.standard.code.dictionary.bind.DictItemEntry;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.time.LocalDateTime;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@ApiModel("资产盘点异常明细DTO")
public class RfidAssetTakeAbnormalVo {

    @ApiModelProperty("id")
    Long id;

    @Excel(name = "盘点单号", width = 30, orderNum = "0")
    @ApiModelProperty("盘点单号")
    String takeCode;

    @ApiModelProperty("盘点部门")
    @Excel(name = "盘点部门", width = 30, orderNum = "1")
    String takeDepartmentName;

    @ApiModelProperty(value = "RFID编号", required = true)
    @Excel(name = "盘点编号", width = 30, orderNum = "2")
    String rfidCode;

    @ApiModelProperty(value = "资产编码", required = true)
    @Excel(name = "资产编号", width = 30, orderNum = "3")
    String code;

    @ApiModelProperty(value = "资产名称", required = true)
    @Excel(name = "资产名称", width = 30, orderNum = "4")
    String name;

    @ApiModelProperty("资产型号")
    @Excel(name = "资产型号", width = 30, orderNum = "5")
    String model;

    @TableField(exist = false)
    @ApiModelProperty("资产小类")
    @ExcelIgnore
    String assetType;

    @TableField(exist = false)
    @ApiModelProperty("资产小类")
    @ExcelIgnore
    String assetTypeName;

    @ApiModelProperty("使用状态")
    @DictItemBind(DictUtil.DICT_USE_STATE)
    @ExcelIgnore
    DictItemEntry useState;

    @JsonAmount
    @ApiModelProperty("资产价格")
    @Excel(name = "资产价格", width = 30, orderNum = "6")
    Long price;

    @ApiModelProperty(value = "责任人", required = true)
    Long managerId;

    @ApiModelProperty(value = "责任人", required = true)
    Long applyId;

    @ApiModelProperty("保管人姓名")
    @Excel(name = "保管人", width = 30, orderNum = "7")
    String applyName;

    @ApiModelProperty("部门负责人")
    String  managerName;
    @ApiModelProperty("责任部门")
    String managerDeptName;

    @ApiModelProperty("所在单位")
    @Excel(name = "所在单位", width = 30, orderNum = "9")
    String departmentName;

    @ApiModelProperty("资产位置")
    @Excel(name = "资产位置", width = 30, orderNum = "10")
    String location;

    @ApiModelProperty("资产位置id")
    Long locationId;

    @ApiModelProperty("所在单位id")
    Long departmentId;

    @ApiModelProperty("资产位置id")
    Long actualLocationId;

    @ApiModelProperty("所在单位id")
    Long actualDepartmentId;

    @ApiModelProperty("实际保管人id")
    @Excel(name = "保管人", width = 30, orderNum = "7")
    Long keeperId;

    @ApiModelProperty("实际保管人姓名")
    @Excel(name = "保管人", width = 30, orderNum = "7")
    String keeperName;

    @ApiModelProperty("所在单位")
    @Excel(name = "所在单位", width = 30, orderNum = "9")
    String actualDepartmentName;

    @ApiModelProperty("资产位置")
    @Excel(name = "资产位置", width = 30, orderNum = "10")
    String actualLocation;

    @ApiModelProperty("基站id")
    Long actualReaderId;

    @ApiModelProperty("是否标签")
    @ExcelIgnore
    Boolean hasTag;

    @ApiModelProperty("状态")
    DiscrepancyStatus status;

    @ApiModelProperty("扫描状态")
    TakeScanStatus scanStatus;

    @ApiModelProperty("盘点状态")
    TakeStatus takeStatus;


    @Excel(name = "盘点时间", width = 30, format = "yyyy/MM/dd HH:mm:ss", orderNum = "11")
    @ApiModelProperty("盘点时间")
    LocalDateTime createdAt;

}
