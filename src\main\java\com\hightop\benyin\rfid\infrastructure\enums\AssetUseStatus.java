package com.hightop.benyin.rfid.infrastructure.enums;

import com.hightop.magina.core.custom.entry.EnumEntry;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

/**
 * 出登记
 *
 * @Author: X.S
 * @Date: 2023/12/19 11:43
 */
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@Getter
@RequiredArgsConstructor
public enum AssetUseStatus implements EnumEntry<String> {


    USE("10", "在用"),
    IDLE("20", "闲置"),
    UNUSE("30", "未使用"),
    MAINTENANCE("40", "维修中"),
    BREAKAGE("50", "损毁待报废"),
    BORROW("60", "借用中"),
    INSTOCK("70", "回到库存"),
    REPAIR("80", "报修");
    /**
     * 代码
     */
    String code;
    /**
     * 描述
     */
    String name;

    public static String getName(String code) {
        for (AssetUseStatus status : AssetUseStatus.values()) {
            if (status.getCode().equals(code)) {
                return status.getName();
            }
        }
        return null;
    }
}
