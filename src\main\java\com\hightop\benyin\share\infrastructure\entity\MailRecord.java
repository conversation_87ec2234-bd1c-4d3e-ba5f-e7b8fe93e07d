package com.hightop.benyin.share.infrastructure.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.hightop.benyin.share.infrastructure.enums.MainChannel;
import com.hightop.magina.standard.ums.user.bind.UserBind;
import com.hightop.magina.standard.ums.user.bind.UserEntry;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.experimental.FieldDefaults;

import java.time.LocalDateTime;
import java.util.List;

@FieldDefaults(level = AccessLevel.PRIVATE)
@Data
@Accessors(chain = true)
@TableName(value = "b_mail_record",autoResultMap = true)
public class MailRecord {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @ApiModelProperty("id")
    Long id;

    @TableField("channel")
    @ApiModelProperty("发送渠道")
    MainChannel channel;

    @TableField("business_id")
    @ApiModelProperty("业务id")
    Long businessId;

    @TableField(value = "send_to", typeHandler = JacksonTypeHandler.class)
    @ApiModelProperty("发送对象")
    List<Long> sendTo;

    @TableField("send_to_name")
    @ApiModelProperty("发送对象")
    String sendToName;

    @TableField("subject")
    @ApiModelProperty("主题")
    String subject;

    @TableField("content")
    @ApiModelProperty("内容")
    String content;

    @TableField("status")
    @ApiModelProperty("发送状态0未提交1已发送2发送失败")
    Integer status;

    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @ApiModelProperty("创建时间")
    LocalDateTime createdAt;

    @TableField("created_by")
    @ApiModelProperty("创建人")
    @UserBind
    UserEntry createdBy;
}
