package com.hightop.benyin.rfid.infrastructure.mapper;

import com.github.yulichang.base.MPJBaseMapper;
import com.hightop.benyin.rfid.application.vo.query.RfidReaderQuery;
import com.hightop.benyin.rfid.infrastructure.entity.RfidReader;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * RFID基站管理mapper
 *
 * <AUTHOR>
 * @date 2024-10-12 16:36:01
 */
public interface RfidReaderMapper extends MPJBaseMapper<RfidReader> {

    /**
     * 列表查询
     * @param query
     * @return
     */
    public List<RfidReader> getReaderList(@Param("qo") RfidReaderQuery query);


    @Delete("TRUNCATE TABLE b_rfid_reader")
    void clearReader();
}
