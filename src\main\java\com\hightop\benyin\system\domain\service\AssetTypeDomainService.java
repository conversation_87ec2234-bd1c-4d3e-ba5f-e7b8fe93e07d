package com.hightop.benyin.system.domain.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.base.MPJBaseServiceImpl;
import com.hightop.benyin.system.infrastructure.entity.AssetType;
import com.hightop.benyin.system.infrastructure.mapper.AssetTypeMapper;
import com.hightop.fario.base.util.CollectionUtils;
import com.hightop.magina.standard.cipher.CipherText;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.stereotype.Service;

import java.io.Serializable;
import java.util.Collection;
import java.util.Collections;
import java.util.Map;

/**
 * 资产类型领域服务
 *
 * <AUTHOR>
 * @date 2022/09/13 14:28
 * @since 2.0.0
 */
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Service
public class AssetTypeDomainService extends MPJBaseServiceImpl<AssetTypeMapper, AssetType> {
    /**
     * 通过资产类型编码获得资产类型
     *
     * @param code 资产类型编码
     * @return {@link AssetType}
     */
    public AssetType getByCode(String code) {
        return super.lambdaQuery().eq(AssetType::getCode, code).one();
    }

    /**
     * 通过资产类型编码获得资产类型
     *
     * @param name 资产类型编码
     * @return {@link AssetType}
     */
    public AssetType getByName(String name) {
        return super.lambdaQuery().eq(AssetType::getName, name).one();
    }

    /**
     * 通过资产类型编码获得资产类型
     *
     * @param code 资产类型编码
     * @return {@link AssetType}
     */
    public AssetType getParentByCode(String code) {
        AssetType assetType = super.lambdaQuery().eq(AssetType::getCode, code).one();
        if(assetType.getParentId().equals(AssetType.TOP)){
            return  this.getById(assetType.getParentId());
        }
        return null;
    }

    /**
     * 通过资产类型编码获得资产类型
     *
     * @param parentId 资产类型编码
     * @param name 资产类型编码
     * @return {@link AssetType}
     */
    public AssetType getByParentName(Long parentId,String name) {
        AssetType assetType = super.lambdaQuery()
                .eq(AssetType::getParentId,parentId)
                .eq(AssetType::getName, name).one();

        return assetType;
    }

    /**
     * 获得未删除的资产类型映射
     *
     * @return {@link Map}
     */
    public Map<Long, AssetType> getAvailableAssetTypeMapping() {
        return super.baseMapper.getAvailableAssetTypeMapping();
    }


    public void clearAssetType() {
        baseMapper.clearAssetType();
    }

    /**
     * 获得启用的资产类型映射
     *
     * @return {@link Map}
     */
    public Map<Long, AssetType> getEnabledAssetTypeMapping() {
        return super.baseMapper.getEnabledAssetTypeMapping();
    }

    @Override
    public boolean removeById(Serializable id) {
        return this.removeByIds(Collections.singletonList(id));
    }

    @Override
    public boolean removeById(AssetType entity) {
        return this.removeByIds(Collections.singletonList(entity.getId()));
    }

    @Override
    public boolean removeByIds(Collection<? extends Serializable> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return false;
        }

        if (idList.size() == 1) {
            return this.remove(super.lambdaUpdate().eq(AssetType::getId, idList.iterator().next()).getWrapper());
        }

        return this.remove(super.lambdaUpdate().in(AssetType::getId, idList).getWrapper());
    }

    @Override
    public boolean remove(Wrapper<AssetType> queryWrapper) {
        return super.update(new AssetType().setIsAvailable(false), queryWrapper);
    }

    @Override
    public boolean removeByMap(Map<String, Object> columnMap) {
        throw new UnsupportedOperationException("资产类型删除不支持此方法");
    }
}
