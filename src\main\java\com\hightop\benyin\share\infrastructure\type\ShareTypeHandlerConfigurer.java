package com.hightop.benyin.share.infrastructure.type;

import com.hightop.fario.common.mybatis.mysql.type.supplier.JsonTypeSupplier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;

/**
 * share mybatis类型处理器配置
 * @Author: X.S
 * @date 2023/10/26 11:10
 */
@Configuration(proxyBeanMethods = false)
public class ShareTypeHandlerConfigurer {
    @Bean
    JsonTypeSupplier<?> shareJsonTypeSupplier() {
        return
            () ->
                Collections.unmodifiableSet(
                    new HashSet<>(
                        Arrays.asList(
                            CosObject.class,
                            CosObjectList.class,
                            StringValueMap.class,
                            ObjectValueMap.class
                        )
                    )
                );
    }
}
