package com.hightop.benyin.share.application.service;

import com.github.yulichang.toolkit.MPJWrappers;
import com.hightop.magina.casual.key.KeyDomainService;
import com.hightop.magina.standard.ums.role.Role;
import com.hightop.magina.standard.ums.role.RoleDomainService;
import com.hightop.magina.standard.ums.role.api.RoleVo;
import com.hightop.magina.standard.ums.user.basic.UserBasicDomainService;
import com.hightop.magina.standard.ums.user.manage.UserManageService;
import com.hightop.magina.standard.ums.user.privacy.UserPrivacyDomainService;
import com.hightop.magina.standard.ums.user.role.UserRole;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 重写用户管理服务{@link UserManageService}
 *
 * @Author: X.S
 * @date 2023/12/11 11:00
 */
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@Service
@Primary
public class ByUserManageService extends UserManageService {
    UserBasicDomainService userBasicDomainService;

    public ByUserManageService(KeyDomainService keyDomainService,
                               UserBasicDomainService userBasicDomainService,
                               UserPrivacyDomainService userPrivacyDomainService, RoleDomainService roleDomainService) {
        super(keyDomainService, userBasicDomainService, userPrivacyDomainService);
        this.userBasicDomainService = userBasicDomainService;
        this.roleDomainService = roleDomainService;
    }

    RoleDomainService roleDomainService;

    /**
     * 查询用户角色列表
     *
     * @param userId 用户id
     * @return {@link List}
     * @since 2.4.0
     */
    public List<RoleVo> getUserRoles(Long userId) {
        List<Role> listRole = this.roleDomainService.getBaseMapper()
                .selectJoinList(
                        Role.class,
                        MPJWrappers.<UserRole>lambdaJoin()
                                .selectAll(Role.class)
                                .innerJoin(
                                        UserRole.class,
                                        on -> on.eq(Role::getId, UserRole::getRoleId).eq(Role::getIsEnable, true).eq(UserRole::getUserId, userId)
                                )
                );
        List<RoleVo> list = new ArrayList<RoleVo>();
        if (!listRole.isEmpty()) {
            for (Role li : listRole) {
                RoleVo vo = new RoleVo();
                BeanUtils.copyProperties(li, vo);
                list.add(vo);
            }
        }
        return list;
    }
}
