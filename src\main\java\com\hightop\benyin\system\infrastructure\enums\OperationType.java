package com.hightop.benyin.system.infrastructure.enums;

import com.hightop.magina.core.custom.entry.EnumEntry;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

/**
 * 操作类型状态
 * <AUTHOR>
 * @date 2024/5/15 13:42
 */
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Getter
public enum OperationType implements EnumEntry<String> {
    /**
     * 新增
     */
    INSERT("新增"),
    /**
     * 已出库
     */
    UPDATE("修改"),
    /**
     * 删除
     */
    DELETE("删除"),
    /**
     * 启用
     */
    ENABLE("启用"),
    /**
     * 停用
     */
    DISABLE("停用");

    /**
     * 状态名称
     */
    String name;

    @Override
    public String getCode() {
        // 以枚举名称作为标识
        return super.name();
    }

    @Override
    public String getName() {
        return this.name;
    }

}
