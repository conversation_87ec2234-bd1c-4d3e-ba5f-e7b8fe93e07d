package com.hightop.benyin.share.socket.common;

import com.github.pagehelper.util.StringUtil;
import com.hightop.benyin.share.domain.event.ApiLogEvent;
import com.hightop.benyin.share.infrastructure.enums.MessageType;
import com.hightop.benyin.share.socket.enums.CommandType;
import com.hightop.benyin.share.socket.enums.ErrorType;
import com.hightop.benyin.share.socket.util.MsgUtil;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class SocketMessage {

    /**
     * 协议头
     */
    public static final int HEAD_ONE = 0xFE;
    /**
     * 协议头
     */
    public static final int HEAD_TWO = 0xEF;

    /**
     * 校验码
     */
    public static final int VERIFY = 0x00;

    /**
     * 命令
     */
    @Getter
    @Setter
    private CommandType command;

    /**
     * 协议控制
     */
    private int control;

    /**
     * 参数长度
     */
    @Getter
    private int length;


    @Getter
    private ErrorType errorType;

    /**
     * 参数
     */
    @Getter
    @Setter
    private String data;

    /**
     * 完整参数
     */
    @Getter
    @Setter
    private String message;


    public SocketMessage(byte[] bytes) {
        this.buildSockerMessage(bytes);
    }

    private void buildSockerMessage(byte[] bytes) {
        this.message = MsgUtil.convertMessage(bytes);
        //暂定5 长度小于肯定是错误的
        if (bytes.length < 5) {
            this.errorType = ErrorType.FORMAL_ERROR;
            return;
        }
        //校验头
        int head1 = MsgUtil.convertToHex(bytes[0]);
        int head2 = MsgUtil.convertToHex(bytes[1]);
        if (head1 != HEAD_ONE || head2 != HEAD_TWO) {
            this.errorType = ErrorType.HEAD_ERROR;
            return;
        }
        //解析命令
        this.control =  MsgUtil.convertToHex(bytes[2]);
        this.command = CommandType.getCommand(control);

        if (this.command == null) {
            this.errorType = ErrorType.UNKNOWN_CMD;
            return;
        }
        //解析长度
        this.length = bytes[3]  * 256+ bytes[4];
        //解析参数
        StringBuilder stringData = new StringBuilder();
        for (int i = 5; i < bytes.length ; i++) {
            String dataStr = String.format("%02X", bytes[i]);
            stringData.append(dataStr);
        }
        this.data = stringData.toString();
        errorType = ErrorType.NO;
    }

    public SocketMessage(CommandType command, Integer length, String data) {
        this.command = command;
        this.data = data;
        this.length = length;
        this.control = command.getCode();
        errorType = ErrorType.NO;
    }

    public SocketMessage( String message) {
        byte[] bytes = MsgUtil.convertMessage(message);
        this.buildSockerMessage(bytes);
    }

    public byte[] toBytes() {
        StringBuilder hexString = new StringBuilder();
        hexString.append(Integer.toHexString(HEAD_ONE));
        hexString.append(Integer.toHexString(HEAD_TWO));
        hexString.append(Integer.toHexString(this.control));
        hexString.append( MsgUtil.converFourToHex(this.length));
        hexString.append(StringUtil.isEmpty(this.data)?"":this.data);
//        hexString.append(Integer.toHexString(VERIFY));
        return MsgUtil.convertMessage(hexString.toString());
    }

    public ApiLogEvent getApiLogEvent(String deviceId,MessageType messageType,Boolean isSuccess) {
        ApiLogEvent apiLogEvent = new ApiLogEvent(this, messageType, this.getCommand(),
                MsgUtil.convertMessage(this.toBytes()), deviceId, this.getErrorType().getName(), isSuccess);
        return apiLogEvent;
    }

    public ApiLogEvent getApiLogEvent(String deviceId,MessageType messageType,Boolean isSuccess,ErrorType errorType) {
        ApiLogEvent apiLogEvent = new ApiLogEvent(this, messageType, this.getCommand(),
                MsgUtil.convertMessage(this.toBytes()), deviceId, errorType.getName(), isSuccess);
        return apiLogEvent;
    }

    @Override
    public String toString() {
        return (this.getCommand()!= null? this.getCommand().getName():"未知命令" )+ " | 参数长度：" + this.length + " | 参数：" + this.getData();
    }

}
