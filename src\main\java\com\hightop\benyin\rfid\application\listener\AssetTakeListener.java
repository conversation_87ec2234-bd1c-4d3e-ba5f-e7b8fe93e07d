package com.hightop.benyin.rfid.application.listener;


import com.hightop.benyin.rfid.application.service.RfidAssetTakeService;
import com.hightop.benyin.rfid.domain.event.AssetTakeEvent;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

/**
 * 资产盘点完成监听器
 */
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Service
@Slf4j
public class AssetTakeListener {

    RfidAssetTakeService rfidAssetTakeService;

    @EventListener
    public void onCompleted(AssetTakeEvent event) {
        log.info("开始处理资产盘点完成核算 单号: {}！", event.getTakeCode());
        boolean success = rfidAssetTakeService.complete(event.getTakeCode());
        if (success) {
            log.info("处理资产盘点核算 单号: {}成功！", event.getTakeCode());
        }
    }
}
