package com.hightop.benyin.system.api.vo.query;

import com.hightop.magina.standard.ums.user.manage.UserPageQuery;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class UserPageExtendsQuery extends UserPageQuery {
    @ApiModelProperty("手机号")
    String mobileNumber;

    @ApiModelProperty("部门id")
    Long departmentId;

}
