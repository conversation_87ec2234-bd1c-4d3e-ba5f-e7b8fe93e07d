package com.hightop.benyin.share.socket.service.impl;

import com.hightop.benyin.rfid.application.service.RfidInfoService;
import com.hightop.benyin.share.infrastructure.util.DictUtil;
import com.hightop.benyin.share.socket.service.CommandHandler;
import com.hightop.benyin.share.socket.util.MsgUtil;
import com.hightop.magina.standard.code.dictionary.item.DictItemDomainService;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 迁入标签上报-命令处理器
 */
@Service
@Slf4j
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class ReportAddHandler implements CommandHandler {

    RfidInfoService rfidInfoService;
    RedisTemplate<String, String> redisTemplate;
    DictItemDomainService dictItemDomainService;

    @Override
    public void handle(String clientName, String deviceId, Integer length, String params) {
        log.info("迁入标签上报, 设备: {}, params: {}", deviceId, params);
        //RFID 12位一个
//        DictItem dictItem = dictItemDomainService.lambdaQuery()
//                .eq(DictItem::getDictId, DictUtil.SUB_DICT_ID)
//                .eq(DictItem::getLabel, DictUtil.SUB_DICT_NAME)
//                .one();
//        Long times = dictItem!=null?Long.parseLong(dictItem.getValue()):null;
        List<String> rfidList = MsgUtil.splitString(params, MsgUtil.RFID_STR_LENGTH);
//        List<String> reportList = Lists.newArrayList();
//        for (String rfid : rfidList) {
//            String key = DictUtil.SCAN_CODE + deviceId+":"+DictUtil.ADD_SCAN+":"+rfid;
//            long count = redisTemplate.opsForValue().increment(key);
//            if(count==1L){
//                redisTemplate.expire(key, 120, TimeUnit.SECONDS);
//            }
//            log.info("迁入上报次数: {}, {}次", clientName, count);
//            if(count==times){
//                reportList.add(rfid);
//                redisTemplate.delete(key);
//            }
//        }
//        //上报次数统计 减少误报
//        if(CollectionUtils.isEmpty(reportList)){
//            return;
//        }
        rfidInfoService.reportHandler(rfidList, deviceId, DictUtil.ADD_SCAN, DictUtil.REPORT, null);
    }
}
