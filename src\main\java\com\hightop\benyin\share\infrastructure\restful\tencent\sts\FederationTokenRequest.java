package com.hightop.benyin.share.infrastructure.restful.tencent.sts;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.experimental.FieldDefaults;

/**
 * 联合身份临时访问凭证请求
 * @Author: X.S
 * @date 2023/10/25 19:20
 */
@FieldDefaults(level = AccessLevel.PRIVATE)
@Accessors(chain = true)
@Data
public class FederationTokenRequest {
    @JsonProperty("Name")
    String name = "benyin";
    /**
     * 策略
     */
    @JsonProperty("Policy")
    String policy;
    /**
     * 有效时长
     */
    @JsonProperty("DurationSeconds")
    Integer durationSeconds;
}
