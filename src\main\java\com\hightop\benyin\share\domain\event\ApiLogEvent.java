package com.hightop.benyin.share.domain.event;

import com.hightop.benyin.share.infrastructure.enums.MessageType;
import com.hightop.benyin.share.socket.enums.CommandType;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.experimental.FieldDefaults;
import org.springframework.context.ApplicationEvent;

/**
 * 消息日志事件
 *
 * @Author: X.S
 * @date 2024/09/16 14:55
 */
@FieldDefaults(level = AccessLevel.PRIVATE)
@Getter
public class ApiLogEvent extends ApplicationEvent {

    public ApiLogEvent(Object source, MessageType messageType, CommandType command, String message, String deviceId, String remark, Boolean isSuccess) {
        super(source);
        this.messageType = messageType;
        this.command = command;
        this.deviceId = deviceId;
        this.message = message;
        this.remark = remark;
        this.isSuccess = isSuccess;
    }

    /**
     * 消息类型
     */
    MessageType messageType;
    /**
     * 设备id
     */
    String deviceId;
    /**
     * 消息
     */
    String message;

    /**
     * 命令
     */
    CommandType command;

    /**
     * 结果
     */
    String remark;
    /**
     * 状态码
     * 1：成功
     * 0：失败
     */
    Boolean isSuccess;

    @Override
    public String toString() {
        String commandName = command != null ? command.getName() : "未知命令";
        return messageType.getName() + " | " + commandName + " | " + deviceId + " | " + message + " | " + remark + " | " + isSuccess + " | ";
    }
}
