package com.hightop.benyin.system.api.vo.dto;

import com.hightop.benyin.system.infrastructure.entity.Location;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class LocationAddDto {
    @ApiModelProperty("id")
    Long id;
    @ApiModelProperty("父id")
    Long parentId;
    @ApiModelProperty("名称")
    @NotBlank(message = "名称不能为空")
    @Size(min = 1, max = 64, message = "名称长度在{min}至{max}之间")
    String name;
    @ApiModelProperty("编码")
    String code;

    @ApiModelProperty("类型")
    Integer type;

    @ApiModelProperty("别称")
    String anotherName;

    @ApiModelProperty("关联的部门id列表")
    List<Long> departmentIds;

    @ApiModelProperty("排序号")
    Integer sort;

    public Location toLocation() {
        return
                new Location().setParentId(this.parentId)
                        .setName(this.name)
                        .setId(this.id)
                        .setCode(this.code)
                        .setAnotherName(this.anotherName)
                        .setDepartmentIds(this.departmentIds)
                        .setSort(this.sort);
    }
}
