package com.hightop.benyin.rfid.application.vo.query;

import com.hightop.fario.common.mybatis.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldDefaults;

/**
 * 资产审核查询DTO
 * <AUTHOR>
 * @date 2024-10-12 16:36:01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@FieldDefaults(level = AccessLevel.PRIVATE)
@ApiModel("资产审核查询DTO")
public class RfidAssetRecordQuery extends PageQuery {

    @ApiModelProperty("状态")
    String status;

    @ApiModelProperty("流水id")
    String flowId;

}
