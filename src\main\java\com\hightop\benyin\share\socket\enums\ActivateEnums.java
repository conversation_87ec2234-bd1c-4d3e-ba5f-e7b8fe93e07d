package com.hightop.benyin.share.socket.enums;

import com.hightop.magina.core.custom.entry.EnumEntry;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

/**
 * 出登记
 *
 * @Author: X.S
 * @Date: 2023/12/19 11:43
 */
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@Getter
@RequiredArgsConstructor
public enum ActivateEnums implements EnumEntry<Integer> {
    /**
     * 启动
     */
    ON(0x01, "启动"),
    /**
     * 停止
     */
    OFF(0x02, "停止");
    /**
     * 代码
     */
    Integer code;
    /**
     * 描述
     */
    String name;
}