package com.hightop.benyin.rfid.infrastructure.enums;

import com.hightop.magina.core.custom.entry.EnumEntry;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

/**
 * 轨迹来源状态
 * <AUTHOR>
 * @date 2024/5/15 13:42
 */
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Getter
public enum TrajectorySource implements EnumEntry<String> {
    /**
     * 位置绑定
     */
    LOCATION ("位置绑定"),
    /**
     * 盘点扫描
     */
    TAKE ("盘点扫描"),
    /**
     * 全局搜索
     */
    SEARCH("全局搜索"),
    /**
     * 异动解除
     */
    RELIEVE("异动解除"),
    /**
     * 异动上报
     */
    VARIATION("异动上报");

    /**
     * 状态名称
     */
    String name;

    @Override
    public String getCode() {
        // 以枚举名称作为标识
        return super.name();
    }

    @Override
    public String getName() {
        return this.name;
    }

}
