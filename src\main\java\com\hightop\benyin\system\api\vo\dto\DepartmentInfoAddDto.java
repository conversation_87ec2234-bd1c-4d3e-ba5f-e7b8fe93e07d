package com.hightop.benyin.system.api.vo.dto;

import com.hightop.benyin.system.infrastructure.entity.DepartmentInfo;
import com.hightop.magina.standard.cipher.CipherText;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 部门新增dto
 *
 * <AUTHOR>
 * @date 2022/10/09 15:48
 * @since 1.0.0
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class DepartmentInfoAddDto {
    @ApiModelProperty("父id")
    Long parentId;
    @ApiModelProperty("名称")
    @NotBlank(message = "名称不能为空")
    @Size(min = 1, max = 64, message = "名称长度在{min}至{max}之间")
    String name;
    @ApiModelProperty("编码")
    String code;
    @ApiModelProperty("排序号")
    @NotNull(message = "排序号不能为空")
    Integer sort;
    @ApiModelProperty("负责人id")
    Long managerId;



    public DepartmentInfo toDepartment() {
        return
                new DepartmentInfo().setParentId(this.parentId)
                        .setName(new CipherText(this.name))
                        .setCode(new CipherText(this.code))
                        .setManagerId(managerId)
                        .setSort(this.sort);
    }
}
