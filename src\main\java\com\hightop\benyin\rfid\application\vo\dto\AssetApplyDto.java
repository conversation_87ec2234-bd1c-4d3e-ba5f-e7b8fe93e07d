package com.hightop.benyin.rfid.application.vo.dto;

import com.hightop.benyin.rfid.infrastructure.enums.AssetApplyStatus;
import com.hightop.benyin.rfid.infrastructure.enums.AssetBusinessType;
import com.hightop.benyin.rfid.infrastructure.enums.AssetRecord;
import com.hightop.magina.standard.code.dictionary.bind.DictItemEntry;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @Description: 资产领/借用DTO
 * @Author: X.S
 * @Date: 2023/12/22 17:16
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@ApiModel("资产领/借用DTO")
public class AssetApplyDto {

    @ApiModelProperty("资产id集合")
    @NotNull(message = "资产信息不能为空")
    List<Long> ids;

    @ApiModelProperty("id")
    Long id;

    @ApiModelProperty("申请类型")
    AssetBusinessType operateType;

    @ApiModelProperty("申请时间")
    LocalDateTime applyAt;

    @ApiModelProperty("申请人")
    @NotNull(message = "业务人员信息不能为空")
    Long applyId;

    @ApiModelProperty("申请人")
    String applyName;

    @ApiModelProperty("申请人部门")
    Long departmentId;

    @ApiModelProperty("申请人部门")
    String departmentName;

    @ApiModelProperty("外部单位")
    String externalUnit;

    @ApiModelProperty("外部部门")
    String externalDept;

    @ApiModelProperty("外部人员")
    String externalPerson;

    @ApiModelProperty("变更位置")
    Long changeLocationId;

    @ApiModelProperty("变更基站")
    Long changeReaderId;

    @ApiModelProperty("备注")
    String remark;

    @ApiModelProperty(value = "/原资产保管部门")
    Long oriDeptId;


    @ApiModelProperty(value = "原资产保管人")
    Long oriApplyId;


    @ApiModelProperty("资产使用状态")
    DictItemEntry useState;

    @ApiModelProperty("流程状态")
    AssetApplyStatus status;

    @ApiModelProperty("流程记录")
    AssetRecord assetRecord;

    @ApiModelProperty("是否自动审核")
    Boolean autoAudit = false;

    @ApiModelProperty("审批人id")
    Long approveId;

    @ApiModelProperty("审批人姓名")
    String approveName;

}
