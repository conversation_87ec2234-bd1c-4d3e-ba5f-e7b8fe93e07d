package com.hightop.benyin.share.infrastructure.enums;

import com.hightop.magina.core.custom.entry.EnumEntry;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

/**
 * 微信物流模式 <a href="https://developers.weixin.qq.com/miniprogram/dev/platform-capabilities/business-capabilities/order-shipping/order-shipping.html#%E4%B8%80%E3%80%81%E5%8F%91%E8%B4%A7%E4%BF%A1%E6%81%AF%E5%BD%95%E5%85%A5%E6%8E%A5%E5%8F%A3">参见小程序发货信息录入</a>
 * @Author: X.S
 * @date 2023/12/20 17:22
 */
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Getter
public enum WeChatLogisticsType implements EnumEntry<Integer> {
    /**
     * 实体物流，一般用于异地
     */
    EXPRESS_DELIVERY(1, "实体物流配送"),
    /**
     * 同城配送，闪送、达达等
     */
    CITY_DELIVERY(2, "同城配送"),
    /**
     * 无需配送服务，如维修、话费充值等
     */
    VIRTUAL_DELIVERY(3, "虚拟配送"),
    /**
     * 用户自提，无配送费
     */
    SELF_PICKUP(4, "用户自提"),
    /**
     * 工程师带，无配送费
     */
    IN_PASSING(5, "工程师带");

    /**
     * 微信物流模式
     */
    Integer code;
    /**
     * 模式名称
     */
    String name;
}
