package com.hightop.benyin.rfid.domain.event;

import com.hightop.benyin.rfid.application.vo.dto.AssetTrajectoryDto;
import lombok.*;
import lombok.experimental.Accessors;
import lombok.experimental.FieldDefaults;

import java.util.List;

/**
 * 资产变更日志 事件
 *
 * <AUTHOR>
 * @date 2024/10/16 14:55
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class AssetTrajectoryEvent {

    /**
     * 上报轨迹
     */
    List<AssetTrajectoryDto> assetTrajectoryDtos;


}
