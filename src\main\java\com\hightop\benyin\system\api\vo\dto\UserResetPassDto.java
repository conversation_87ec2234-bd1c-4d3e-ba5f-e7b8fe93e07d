package com.hightop.benyin.system.api.vo.dto;

import com.hightop.magina.casual.key.KeyTokenDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@EqualsAndHashCode(callSuper = true)
@FieldDefaults(level = AccessLevel.PRIVATE)
public class UserResetPassDto extends KeyTokenDto {
    @ApiModelProperty("用户id")
    @NotNull(message = "用户id不能为空")
    Long id;

    @ApiModelProperty("私钥")
    @NotBlank(message = "私钥")
    String second;

    @ApiModelProperty("原密码密文")
    @NotBlank(message = "原密码不能为空")
    String password;

    @ApiModelProperty("新密码密文")
    @NotBlank(message = "新密码不能为空")
    String newPassword;
}
