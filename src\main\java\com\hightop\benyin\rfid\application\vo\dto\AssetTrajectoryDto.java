package com.hightop.benyin.rfid.application.vo.dto;

import com.hightop.benyin.rfid.infrastructure.enums.TrajectorySource;
import com.hightop.benyin.rfid.infrastructure.enums.TrajectoryType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.time.LocalDateTime;

/**
 * @Description: 资产轨迹管理DTO
 * @Author: X.S
 * @Date: 2023/12/22 17:16
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@ApiModel("资产管理DTO")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AssetTrajectoryDto {

    @ApiModelProperty("rfid编码")
    Long assetId;

    @ApiModelProperty("rfid编码")
    String rfidCode;

    @ApiModelProperty("轨迹类型")
    TrajectoryType trajectoryType;

    @ApiModelProperty("异动来源")
    TrajectorySource trajectorySource;

    @ApiModelProperty("基站id")
    Long readerId;

    @ApiModelProperty("基站设备id")
    String deviceId;

    @ApiModelProperty("位置id")
    Long locationId;

    @ApiModelProperty("位置")
    String location;

}
