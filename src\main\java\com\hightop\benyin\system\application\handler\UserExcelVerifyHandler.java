package com.hightop.benyin.system.application.handler;

import cn.afterturn.easypoi.excel.entity.result.ExcelVerifyHandlerResult;
import cn.afterturn.easypoi.handler.inter.IExcelVerifyHandler;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.hightop.benyin.system.api.vo.LocationVo;
import com.hightop.benyin.system.api.vo.excel.UserExcel;
import com.hightop.benyin.system.domain.service.DepartmentInfoDomainService;
import com.hightop.benyin.system.domain.service.LocationDomainService;
import com.hightop.benyin.system.infrastructure.entity.DepartmentInfo;
import com.hightop.benyin.system.infrastructure.entity.Location;
import com.hightop.fario.base.util.CollectionUtils;
import com.hightop.fario.base.util.StringUtils;
import com.hightop.magina.core.component.ApplicationContexts;
import com.hightop.magina.standard.ums.user.basic.UserBasic;
import com.hightop.magina.standard.ums.user.basic.UserBasicDomainService;
import com.hightop.magina.standard.ums.user.privacy.UserSex;

import java.util.List;
import java.util.Objects;
import java.util.StringJoiner;

public class UserExcelVerifyHandler implements IExcelVerifyHandler<UserExcel> {
    @Override
    public ExcelVerifyHandlerResult verifyHandler(UserExcel data) {
        DepartmentInfoDomainService departmentDomainService = ApplicationContexts.getBean(DepartmentInfoDomainService.class);
        UserBasicDomainService userBasicDomainService = ApplicationContexts.getBean(UserBasicDomainService.class);
        LocationDomainService locationDomainService = ApplicationContexts.getBean(LocationDomainService.class);
        StringJoiner joiner = new StringJoiner(",");

        if (StringUtils.isNotBlank(data.getDepartmentCode())) {
            DepartmentInfo department = departmentDomainService.lambdaQuery()
                    .eq(DepartmentInfo::getCode, data.getDepartmentCode()).one();
            if (Objects.isNull(department)) {
                joiner.add("所属单位编码有误");
            } else {
                data.setDepartmentId(department.getId());
                if (StringUtils.isEmpty(data.getLocationCode())) {
                    // 部门默认地址
                    List<LocationVo> locationTreeVos = locationDomainService.getByDepartmentId(data.getDepartmentId());
                    if (CollectionUtils.isNotEmpty(locationTreeVos)) {
                        data.setLocationId(locationTreeVos.get(0).getId());
                    }
                }
            }
        }
        if (StringUtils.isNotBlank(data.getLocationCode())) {
            Location location = locationDomainService.lambdaQuery()
                    .eq(Location::getCode, data.getLocationCode()).one();
            if (Objects.isNull(location)) {
                joiner.add("所在位置编码有误");
            } else {
                data.setLocationId(location.getId());
            }
        }
        if (StringUtils.isNotBlank(data.getSex())) {
            UserSex.values();
            for (UserSex sex : UserSex.values()) {
                if (sex.getName().equals(data.getSex())) {
                    data.setSexEnum(sex);
                    break;
                }
            }
            if (Objects.isNull(data.getSexEnum())) {
                joiner.add("性别有误");
            }
        }
        // 校验编码是否存在 允许修改
        UserBasic userBasic = userBasicDomainService.lambdaQuery()
                .eq(UserBasic::getCode, data.getCode()).one();
        if (Objects.nonNull(userBasic)) {
            data.setId(userBasic.getId());
        } else {
            data.setId(IdWorker.getId());
        }
        String errorMsg = joiner.toString();
        if (StringUtils.isNotBlank(errorMsg)) {
            return new ExcelVerifyHandlerResult(false, errorMsg);
        }
        return new ExcelVerifyHandlerResult(true);
    }
}
