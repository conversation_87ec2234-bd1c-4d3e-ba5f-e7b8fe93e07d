package com.hightop.benyin.share.infrastructure.restful.tencent;

import com.hightop.fario.base.constant.StringConstants;
import com.hightop.fario.base.util.StringUtils;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotEmpty;
import java.util.concurrent.TimeUnit;

/**
 * 腾讯cos属性配置
 * @Author: X.S
 * @date 2023/10/25 12:38
 */
@FieldDefaults(level = AccessLevel.PRIVATE)
@Data
@ConfigurationProperties("benyin.tencent.cos")
@Validated
public class CosProperties {
    /**
     * 区域
     */
    @NotEmpty(message = "cos区域不能为空")
    String region;
    /**
     * 桶名字 bucket-appid
     */
    @NotEmpty(message = "cos桶名称不能为空")
    String bucket;
    /**
     * access key
     */
    @NotEmpty(message = "cos secretId不能为空")
    String secretId;
    /**
     * 密钥
     */
    @NotEmpty(message = "cos secretKey不能为空")
    String secretKey;
    /**
     * 默认存储目录 默认为根目录
     */
    String prefix = StringConstants.EMPTY;
    /**
     * 文件最大传输时长(秒) 默认30分钟
     */
    Long maxTransferTimeout = TimeUnit.MINUTES.toSeconds(30L);

    /**
     * http请求地址
     * @return 请求地址
     */
    public String getUrl() {
        return String.format("https://%s", this.getHost());
    }

    /**
     * host
     * @return host
     */
    public String getHost() {
        return String.format("%s.cos.%s.myqcloud.com", this.bucket, this.bucket);
    }

    public String getPrefix() {
        if (StringUtils.isNotEmpty(this.prefix) && !this.prefix.endsWith(StringConstants.SLASH)) {
            return this.prefix + StringConstants.SLASH;
        }

        return prefix;
    }

    /**
     * 从桶中解析appId
     * @return appId
     */
    public String getAppId() {
        String[] split = this.bucket.split(StringConstants.MINUS);
        return split.length > 1 ? split[1] : null;
    }
}
