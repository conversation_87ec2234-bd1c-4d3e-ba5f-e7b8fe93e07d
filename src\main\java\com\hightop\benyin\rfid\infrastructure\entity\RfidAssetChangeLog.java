package com.hightop.benyin.rfid.infrastructure.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.hightop.benyin.rfid.infrastructure.enums.AssetChangeSource;
import com.hightop.benyin.rfid.infrastructure.enums.AssetOperatType;
import com.hightop.magina.standard.ums.user.bind.UserBind;
import com.hightop.magina.standard.ums.user.bind.UserEntry;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@Accessors(chain = true)
@TableName("b_rfid_asset_change_log")
@ApiModel
public class RfidAssetChangeLog implements Serializable {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @ApiModelProperty("id")
    Long id;

    @TableField("operation_type")
    @ApiModelProperty("变更类型")
    AssetOperatType operationType;

    @TableField("source")
    @ApiModelProperty("变更来源")
    AssetChangeSource source;

    @TableField("change_code")
    @ApiModelProperty("变更单号")
    String changeCode;

    @TableField("asset_id")
    @ApiModelProperty("资产id")
    Long assetId;

    /**
     * 变更前
     */
    @TableField("before_data")
    @ApiModelProperty("变更前")
    String beforeData;

    /**
     * 变更后
     */
    @TableField("after_data")
    @ApiModelProperty("变更后")
    String afterData;

    @TableField("created_by")
    @ApiModelProperty("创建人")
    @UserBind
    UserEntry createdBy;


    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @ApiModelProperty("创建时间")
    LocalDateTime createdAt;

    @TableField(exist = false)
    @ApiModelProperty("rfid编码")
    String rfidCode;

    @TableField(exist = false)
    @ApiModelProperty("资产名称")
    String assetName;

    @TableField(exist = false)
    @ApiModelProperty("资产编码")
    String assetCode;
}
